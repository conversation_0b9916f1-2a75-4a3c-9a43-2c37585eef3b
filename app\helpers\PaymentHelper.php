<?php

/**
 * Payment Helper Class
 * Handles payment processing for different payment methods
 */
class PaymentHelper
{
    /**
     * Process payment based on method
     */
    public static function processPayment($orderData, $paymentMethod, $paymentData)
    {
        switch ($paymentMethod) {
            case 'stripe':
                return self::processStripePayment($orderData, $paymentData);
            case 'paypal':
                return self::processPayPalPayment($orderData, $paymentData);
            case 'cash_on_delivery':
                return self::processCashOnDelivery($orderData);
            default:
                return [
                    'success' => false,
                    'error' => 'Invalid payment method'
                ];
        }
    }

    /**
     * Process Stripe payment
     */
    private static function processStripePayment($orderData, $paymentData)
    {
        // This is a basic implementation - in production, use Stripe SDK
        if (empty(STRIPE_SECRET_KEY)) {
            return [
                'success' => false,
                'error' => 'Stripe not configured'
            ];
        }

        try {
            // Simulate Stripe API call
            $amount = $orderData['total_amount'] * 100; // Convert to cents
            $currency = 'usd';

            // In a real implementation, you would:
            // 1. Create a payment intent with Stripe
            // 2. Confirm the payment
            // 3. Handle webhooks for payment status updates

            // For demo purposes, simulate success
            $paymentResult = [
                'id' => 'pi_' . uniqid(),
                'status' => 'succeeded',
                'amount' => $amount,
                'currency' => $currency
            ];

            return [
                'success' => true,
                'transaction_id' => $paymentResult['id'],
                'status' => 'paid',
                'amount' => $orderData['total_amount'],
                'payment_data' => $paymentResult
            ];

        } catch (Exception $e) {
            error_log("Stripe payment error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment processing failed'
            ];
        }
    }

    /**
     * Process PayPal payment
     */
    private static function processPayPalPayment($orderData, $paymentData)
    {
        if (empty(PAYPAL_CLIENT_ID)) {
            return [
                'success' => false,
                'error' => 'PayPal not configured'
            ];
        }

        try {
            // In a real implementation, you would:
            // 1. Create a PayPal order
            // 2. Capture the payment
            // 3. Handle webhooks

            // For demo purposes, simulate success
            $paymentResult = [
                'id' => 'PAYID-' . strtoupper(uniqid()),
                'status' => 'COMPLETED',
                'amount' => $orderData['total_amount']
            ];

            return [
                'success' => true,
                'transaction_id' => $paymentResult['id'],
                'status' => 'paid',
                'amount' => $orderData['total_amount'],
                'payment_data' => $paymentResult
            ];

        } catch (Exception $e) {
            error_log("PayPal payment error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment processing failed'
            ];
        }
    }

    /**
     * Process Cash on Delivery
     */
    private static function processCashOnDelivery($orderData)
    {
        return [
            'success' => true,
            'transaction_id' => 'COD-' . uniqid(),
            'status' => 'pending',
            'amount' => $orderData['total_amount'],
            'payment_data' => ['method' => 'cash_on_delivery']
        ];
    }

    /**
     * Validate payment data
     */
    public static function validatePaymentData($paymentMethod, $paymentData)
    {
        switch ($paymentMethod) {
            case 'stripe':
                return self::validateStripeData($paymentData);
            case 'paypal':
                return self::validatePayPalData($paymentData);
            case 'cash_on_delivery':
                return ['valid' => true];
            default:
                return ['valid' => false, 'error' => 'Invalid payment method'];
        }
    }

    /**
     * Validate Stripe payment data
     */
    private static function validateStripeData($paymentData)
    {
        $required = ['payment_method_id', 'billing_details'];
        
        foreach ($required as $field) {
            if (empty($paymentData[$field])) {
                return ['valid' => false, 'error' => "Missing required field: $field"];
            }
        }

        return ['valid' => true];
    }

    /**
     * Validate PayPal payment data
     */
    private static function validatePayPalData($paymentData)
    {
        $required = ['order_id'];
        
        foreach ($required as $field) {
            if (empty($paymentData[$field])) {
                return ['valid' => false, 'error' => "Missing required field: $field"];
            }
        }

        return ['valid' => true];
    }

    /**
     * Calculate payment fees
     */
    public static function calculateFees($amount, $paymentMethod)
    {
        switch ($paymentMethod) {
            case 'stripe':
                // Stripe: 2.9% + $0.30
                return ($amount * 0.029) + 0.30;
            case 'paypal':
                // PayPal: 2.9% + $0.30
                return ($amount * 0.029) + 0.30;
            case 'cash_on_delivery':
                // COD: Fixed fee
                return 2.00;
            default:
                return 0;
        }
    }

    /**
     * Get available payment methods
     */
    public static function getAvailablePaymentMethods()
    {
        $methods = [];

        if (!empty(STRIPE_PUBLIC_KEY)) {
            $methods['stripe'] = [
                'name' => 'Credit/Debit Card',
                'description' => 'Pay securely with your credit or debit card',
                'icon' => 'credit-card',
                'enabled' => true
            ];
        }

        if (!empty(PAYPAL_CLIENT_ID)) {
            $methods['paypal'] = [
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account',
                'icon' => 'paypal',
                'enabled' => true
            ];
        }

        $methods['cash_on_delivery'] = [
            'name' => 'Cash on Delivery',
            'description' => 'Pay when you receive your order',
            'icon' => 'money-bill',
            'enabled' => true
        ];

        return $methods;
    }

    /**
     * Refund payment
     */
    public static function refundPayment($transactionId, $amount, $paymentMethod)
    {
        switch ($paymentMethod) {
            case 'stripe':
                return self::refundStripePayment($transactionId, $amount);
            case 'paypal':
                return self::refundPayPalPayment($transactionId, $amount);
            case 'cash_on_delivery':
                return ['success' => true, 'message' => 'COD refund processed manually'];
            default:
                return ['success' => false, 'error' => 'Invalid payment method'];
        }
    }

    /**
     * Refund Stripe payment
     */
    private static function refundStripePayment($transactionId, $amount)
    {
        // In production, use Stripe SDK to create refund
        return [
            'success' => true,
            'refund_id' => 're_' . uniqid(),
            'amount' => $amount,
            'status' => 'succeeded'
        ];
    }

    /**
     * Refund PayPal payment
     */
    private static function refundPayPalPayment($transactionId, $amount)
    {
        // In production, use PayPal SDK to create refund
        return [
            'success' => true,
            'refund_id' => 'REFUND-' . strtoupper(uniqid()),
            'amount' => $amount,
            'status' => 'COMPLETED'
        ];
    }
}
