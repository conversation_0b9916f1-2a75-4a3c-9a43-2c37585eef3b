<!-- Hero Section with 3D Elements -->
<section class="hero perspective">
    <div class="container">
        <div class="hero-content">
            <div class="hero-text fade-in-left">
                <h1 class="hero-title">Welcome to <span class="text-gradient">Cleanance Lab</span></h1>
                <p class="hero-subtitle fade-in-left delay-200">Discover amazing products with exceptional quality and modern design. Your trusted source for everything you need.</p>
                <div class="hero-actions fade-in-left delay-400">
                    <a href="<?= UrlHelper::url('/products') ?>" class="btn-3d btn-3d-primary hover-lift ripple">
                        <i class="fas fa-shopping-bag"></i>
                        Shop Now
                    </a>
                    <a href="<?= UrlHelper::url('/about') ?>" class="btn btn-outline btn-lg hover-glow">
                        <i class="fas fa-info-circle"></i>
                        Learn More
                    </a>
                </div>
            </div>
            <div class="hero-image fade-in-right delay-300">
                <div class="hero-visual-3d preserve-3d">
                    <div class="hero-main-icon pulse-glow">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="floating-elements-3d">
                        <div class="element element-1 float">✨</div>
                        <div class="element element-2 float-delayed">🌟</div>
                        <div class="element element-3 float">💎</div>
                        <div class="element element-4 float-delayed">🚀</div>
                    </div>
                    <div class="hero-bg-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section with 3D Cards -->
<section class="categories-section scroll-reveal">
    <div class="container">
        <div class="section-header fade-in-up">
            <h2 class="section-title">Shop by <span class="text-gradient">Category</span></h2>
            <p class="section-subtitle">Explore our curated collection of products</p>
        </div>

        <div class="categories-grid-3d perspective">
            <?php $delay = 100;
            foreach ($categories as $category): ?>
                <a href="<?= UrlHelper::url('/products/' . $category['slug']) ?>"
                    class="category-card-3d glass-card hover-lift fade-in-up delay-<?= $delay ?>">
                    <div class="category-icon-3d">
                        <?php
                        $icons = [
                            'electronics' => 'fas fa-mobile-alt',
                            'fashion' => 'fas fa-tshirt',
                            'home-garden' => 'fas fa-home',
                            'sports' => 'fas fa-dumbbell',
                            'books' => 'fas fa-book'
                        ];
                        $icon = $icons[$category['slug']] ?? 'fas fa-tag';
                        ?>
                        <i class="<?= $icon ?>"></i>
                    </div>
                    <div class="category-info">
                        <h3 class="category-name"><?= htmlspecialchars($category['name']) ?></h3>
                        <p class="category-description"><?= htmlspecialchars($category['description'] ?? 'Explore amazing products') ?></p>
                        <div class="category-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </a>
            <?php $delay += 100;
            endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                Featured Products
            </h2>
            <p class="section-subtitle">Handpicked products just for you</p>
        </div>

        <div class="products-grid-3d grid-cols-5 perspective">
            <?php $delay = 100;
            foreach ($featuredProducts as $product): ?>
                <div class="product-card-3d fade-in-up delay-<?= $delay ?> compact-card featured-card hover-lift">
                    <div class="product-image">
                        <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                            alt="<?= htmlspecialchars($product['name']) ?>"
                            loading="lazy">
                        <?php if ($product['sale_price']): ?>
                            <div class="product-badge sale-badge">
                                <span>-<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%</span>
                            </div>
                        <?php endif; ?>
                        <?php if ($product['is_featured']): ?>
                            <div class="product-badge featured-badge">
                                <i class="fas fa-star"></i>
                            </div>
                        <?php endif; ?>
                        <div class="product-overlay">
                            <button class="btn-3d btn-3d-primary btn-sm quick-view ripple" data-product-id="<?= $product['id'] ?>" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm add-to-wishlist hover-glow" data-product-id="<?= $product['id'] ?>" title="Add to Wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="btn-3d btn-3d-primary btn-sm add-to-cart-quick ripple" data-product-id="<?= $product['id'] ?>" title="Add to Cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-content">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            <?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?>
                        </div>
                        <h3 class="product-title">
                            <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>

                        <div class="product-price">
                            <span class="product-price-current">
                                $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                            </span>
                            <?php if ($product['sale_price']): ?>
                                <span class="product-price-original">
                                    $<?= number_format($product['price'], 2) ?>
                                </span>
                                <span class="discount-amount">
                                    Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="product-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?= $i <= 4 ? 'filled' : '' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-count">(<?= rand(10, 50) ?>)</span>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary add-to-cart"
                                data-product-id="<?= $product['id'] ?>"
                                data-quantity="1">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            <?php $delay += 100;
            endforeach; ?>
        </div>

        <div class="text-center mt-5">
            <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-outline btn-lg view-all-btn">
                <i class="fas fa-th-large"></i>
                View All Products
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
</section>

<!-- Latest Products Section -->
<section class="latest-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-clock"></i>
                Latest Arrivals
            </h2>
            <p class="section-subtitle">Fresh products added to our collection</p>
        </div>

        <div class="products-grid grid-cols-4">
            <?php foreach ($latestProducts as $product): ?>
                <div class="product-card fade-in compact-card latest-card">
                    <div class="product-image">
                        <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                            alt="<?= htmlspecialchars($product['name']) ?>"
                            loading="lazy">
                        <div class="product-badge new-badge">
                            <i class="fas fa-sparkles"></i>
                            New
                        </div>
                        <div class="product-overlay">
                            <button class="btn btn-primary btn-sm quick-view" data-product-id="<?= $product['id'] ?>" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm add-to-wishlist" data-product-id="<?= $product['id'] ?>" title="Add to Wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="btn btn-primary btn-sm add-to-cart-quick" data-product-id="<?= $product['id'] ?>" title="Add to Cart">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-content">
                        <div class="product-category">
                            <i class="fas fa-tag"></i>
                            <?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?>
                        </div>
                        <h3 class="product-title">
                            <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>

                        <div class="product-price">
                            <span class="product-price-current">
                                $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                            </span>
                            <?php if ($product['sale_price']): ?>
                                <span class="product-price-original">
                                    $<?= number_format($product['price'], 2) ?>
                                </span>
                                <span class="discount-amount">
                                    Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="product-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?= $i <= 4 ? 'filled' : '' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-count">(<?= rand(5, 25) ?>)</span>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary add-to-cart"
                                data-product-id="<?= $product['id'] ?>"
                                data-quantity="1">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="features-grid grid-cols-4">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <h3 class="feature-title">Fast Shipping</h3>
                <p class="feature-description">Free shipping on orders over $100</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">Secure Payment</h3>
                <p class="feature-description">100% secure payment processing</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-undo"></i>
                </div>
                <h3 class="feature-title">Easy Returns</h3>
                <p class="feature-description">30-day return policy</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h3 class="feature-title">24/7 Support</h3>
                <p class="feature-description">Round the clock customer support</p>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="newsletter-section">
    <div class="container">
        <div class="newsletter-content">
            <div class="newsletter-text">
                <h2 class="newsletter-title">Stay Updated</h2>
                <p class="newsletter-description">Subscribe to our newsletter for the latest products, exclusive offers, and updates.</p>
            </div>
            <form class="newsletter-form">
                <input type="email" placeholder="Enter your email address" required>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Subscribe
                </button>
            </form>
        </div>
    </div>
</section>

<style>
    /* Hero Section */
    .hero {
        padding: var(--spacing-2xl) 0;
        background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(126, 87, 194, 0.1) 0%, transparent 50%);
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
        line-height: 1.6;
    }

    .hero-actions {
        display: flex;
        gap: var(--spacing-md);
    }

    .hero-image {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .hero-visual {
        position: relative;
        width: 300px;
        height: 300px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 80px;
        color: var(--white);
        animation: float 6s ease-in-out infinite;
    }

    .floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .element {
        position: absolute;
        font-size: 24px;
        animation: float 4s ease-in-out infinite;
    }

    .element-1 {
        top: 20%;
        left: 20%;
        animation-delay: 0s;
    }

    .element-2 {
        top: 60%;
        right: 20%;
        animation-delay: 1s;
    }

    .element-3 {
        bottom: 20%;
        left: 50%;
        animation-delay: 2s;
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-20px);
        }
    }

    /* Categories Section */
    .categories-section {
        padding: var(--spacing-2xl) 0;
        background-color: var(--dark-bg);
    }

    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .category-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-align: center;
        text-decoration: none;
        color: var(--text-primary);
        transition: all var(--transition-fast);
    }

    .category-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-purple);
        color: var(--text-primary);
    }

    .category-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-2xl);
        color: var(--white);
    }

    .category-name {
        font-size: var(--font-size-xl);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    .category-description {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Featured & Latest Sections */
    .featured-section,
    .latest-section {
        padding: var(--spacing-3xl) 0;
        position: relative;
    }

    .featured-section {
        background-color: var(--dark-card);
    }

    .latest-section {
        background-color: var(--dark-bg);
    }

    .featured-section .container,
    .latest-section .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 var(--spacing-lg);
    }

    /* Section Spacing */
    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-3xl);
        position: relative;
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .section-title i {
        color: var(--primary-purple);
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Grid Container Improvements */
    .products-grid {
        display: grid;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        align-items: start;
        justify-items: stretch;
        width: 100%;
    }

    .products-grid.grid-cols-5 {
        grid-template-columns: repeat(5, 1fr);
        max-width: 1200px;
        margin: 0 auto var(--spacing-xl);
    }

    .products-grid.grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1000px;
        margin: 0 auto var(--spacing-xl);
    }

    /* Ensure all cards have equal height */
    .products-grid .product-card {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    /* Compact Product Cards for Home Page */
    .compact-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        overflow: hidden;
        transition: all var(--transition-fast);
        box-shadow: var(--shadow-md);
    }

    .compact-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .compact-card .product-image {
        aspect-ratio: 1;
        height: 200px;
        position: relative;
        overflow: hidden;
    }

    .compact-card .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-fast);
    }

    .compact-card:hover .product-image img {
        transform: scale(1.05);
    }

    .compact-card .product-content {
        padding: var(--spacing-md);
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .compact-card .product-title {
        font-size: var(--font-size-sm);
        line-height: 1.3;
        margin-bottom: var(--spacing-xs);
        min-height: 2.6em;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .compact-card .product-category {
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-xs);
        min-height: 1.2em;
    }

    .compact-card .product-price {
        margin-bottom: var(--spacing-sm);
        min-height: 1.5em;
    }

    .compact-card .product-price-current {
        font-size: var(--font-size-md);
        font-weight: 600;
    }

    .compact-card .product-price-original {
        font-size: var(--font-size-xs);
    }

    .compact-card .product-rating {
        margin-bottom: var(--spacing-sm);
        min-height: 1.2em;
    }

    .compact-card .product-actions {
        margin-top: auto;
        padding-top: var(--spacing-sm);
    }

    .compact-card .product-actions .btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        width: 100%;
        justify-content: center;
    }

    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: opacity var(--transition-fast);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    /* Features Section */
    .features-section {
        padding: var(--spacing-2xl) 0;
        background-color: var(--dark-card);
    }

    .feature-card {
        text-align: center;
        padding: var(--spacing-lg);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-xl);
        color: var(--white);
    }

    .feature-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .feature-description {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Newsletter Section */
    .newsletter-section {
        padding: var(--spacing-2xl) 0;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        color: var(--white);
    }

    .newsletter-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        align-items: center;
    }

    .newsletter-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-md);
    }

    .newsletter-description {
        font-size: var(--font-size-lg);
        opacity: 0.9;
        line-height: 1.6;
    }

    .newsletter-form {
        display: flex;
        gap: var(--spacing-sm);
    }

    .newsletter-form input {
        flex: 1;
        padding: var(--spacing-md);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--white);
    }

    .newsletter-form input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form input:focus {
        outline: none;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .newsletter-form .btn {
        background-color: var(--white);
        color: var(--primary-purple);
        border: none;
    }

    .newsletter-form .btn:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .grid-cols-5 {
            grid-template-columns: repeat(4, 1fr);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 1024px) {
        .grid-cols-5 {
            grid-template-columns: repeat(3, 1fr);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
        }

        .products-grid {
            gap: var(--spacing-lg);
        }
    }

    @media (max-width: 768px) {
        .hero-content {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .hero-title {
            font-size: var(--font-size-3xl);
        }

        .hero-actions {
            justify-content: center;
        }

        .hero-visual {
            width: 200px;
            height: 200px;
            font-size: 60px;
        }

        .categories-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .newsletter-content {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .newsletter-form {
            flex-direction: column;
        }

        /* Responsive Product Grids */
        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }

        .compact-card .product-image {
            height: 180px;
        }

        .compact-card .product-content {
            padding: var(--spacing-sm);
        }
    }

    @media (max-width: 480px) {
        .hero-actions {
            flex-direction: column;
            align-items: center;
        }

        .categories-grid {
            grid-template-columns: 1fr;
        }

        .features-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        /* Mobile Product Grids */
        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        .compact-card .product-image {
            height: 140px;
        }

        .compact-card .product-content {
            padding: var(--spacing-xs);
        }

        .compact-card .product-title {
            font-size: var(--font-size-xs);
            min-height: 2.4em;
        }

        .compact-card .product-actions .btn {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs);
        }
    }

    /* Enhanced Product Cards */
    .featured-card,
    .latest-card {
        position: relative;
        overflow: hidden;
        border: 1px solid var(--border-color);
        transition: all var(--transition-fast);
    }

    .featured-card {
        border-color: var(--primary-purple);
        box-shadow: 0 4px 20px rgba(126, 87, 194, 0.1);
    }

    .featured-card:hover {
        border-color: var(--primary-light);
        box-shadow: 0 8px 30px rgba(126, 87, 194, 0.2);
        transform: translateY(-8px);
    }

    .latest-card {
        border-color: var(--success-color);
        box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
    }

    .latest-card:hover {
        border-color: var(--success-color);
        box-shadow: 0 8px 30px rgba(16, 185, 129, 0.2);
        transform: translateY(-8px);
    }

    /* Enhanced Product Badges */
    .product-badge {
        position: absolute;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 600;
        z-index: 2;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .sale-badge {
        background: linear-gradient(135deg, var(--error-color), #dc2626);
        color: white;
        animation: pulse 2s infinite;
    }

    .featured-badge {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: auto;
    }

    .new-badge {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        animation: bounce 2s infinite;
    }

    /* Product Ratings */
    .product-rating {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
    }

    .stars {
        display: flex;
        gap: 2px;
    }

    .stars i {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
    }

    .stars i.filled {
        color: #fbbf24;
    }

    .rating-count {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
    }

    /* Enhanced Product Overlay */
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: all var(--transition-fast);
        backdrop-filter: blur(2px);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    .product-overlay .btn {
        transform: translateY(20px);
        transition: all var(--transition-fast);
    }

    .product-card:hover .product-overlay .btn {
        transform: translateY(0);
    }

    .product-overlay .btn:nth-child(1) {
        transition-delay: 0.1s;
    }

    .product-overlay .btn:nth-child(2) {
        transition-delay: 0.2s;
    }

    .product-overlay .btn:nth-child(3) {
        transition-delay: 0.3s;
    }

    /* Enhanced Product Content */
    .product-category {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
    }

    .product-category i {
        color: var(--primary-purple);
        font-size: var(--font-size-xs);
    }

    .discount-amount {
        background: var(--success-color);
        color: white;
        padding: 2px var(--spacing-xs);
        border-radius: var(--radius-xs);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    /* Enhanced View All Button */
    .view-all-btn {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        border: none;
        color: white;
        padding: var(--spacing-lg) var(--spacing-2xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        transition: all var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
    }

    .view-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
        color: white;
    }

    /* Animations */
    @keyframes pulse {

        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-5px);
        }

        60% {
            transform: translateY(-3px);
        }
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<script>
    // Enhanced Product Interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Add to Cart functionality
        document.querySelectorAll('.add-to-cart, .add-to-cart-quick').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                const quantity = this.dataset.quantity || 1;

                addToCart(productId, quantity);
            });
        });

        // Add to Wishlist functionality
        document.querySelectorAll('.add-to-wishlist').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                addToWishlist(productId);
            });
        });

        // Quick View functionality
        document.querySelectorAll('.quick-view').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                showQuickView(productId);
            });
        });

        // Staggered animation for product cards
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    });

    // Add to Cart Function
    function addToCart(productId, quantity) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        fetch('<?= UrlHelper::url('/cart/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart successfully!', 'success');
                    updateCartCount();
                } else {
                    showNotification(data.message || 'Failed to add to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to cart', 'error');
            });
    }

    // Add to Wishlist Function
    function addToWishlist(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);

        fetch('<?= UrlHelper::url('/wishlist/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to wishlist!', 'success');
                    updateWishlistCount();
                } else {
                    showNotification(data.message || 'Failed to add to wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to wishlist', 'error');
            });
    }

    // Quick View Function
    function showQuickView(productId) {
        // For now, redirect to product detail page
        // In a real implementation, this would show a modal
        window.location.href = `<?= UrlHelper::url('/product/') ?>${productId}`;
    }

    // Notification System
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ${getNotificationStyle(type)}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    function getNotificationIcon(type) {
        switch (type) {
            case 'success':
                return 'check-circle';
            case 'error':
                return 'exclamation-circle';
            case 'warning':
                return 'exclamation-triangle';
            default:
                return 'info-circle';
        }
    }

    function getNotificationStyle(type) {
        switch (type) {
            case 'success':
                return 'background: linear-gradient(135deg, #10b981, #059669);';
            case 'error':
                return 'background: linear-gradient(135deg, #ef4444, #dc2626);';
            case 'warning':
                return 'background: linear-gradient(135deg, #f59e0b, #d97706);';
            default:
                return 'background: linear-gradient(135deg, #3b82f6, #2563eb);';
        }
    }

    // Update counters (placeholder functions)
    function updateCartCount() {
        // This would typically fetch the current cart count from the server
        console.log('Cart updated');
    }

    function updateWishlistCount() {
        // This would typically fetch the current wishlist count from the server
        console.log('Wishlist updated');
    }
</script>