<?php

class WishlistController extends BaseController
{
    public function __construct($pdo)
    {
        parent::__construct($pdo);
        WishlistHelper::init($pdo);
    }

    public function index()
    {
        if (!$this->user) {
            $this->redirect('login');
        }

        try {
            // Get user's wishlist
            $wishlistItems = $this->getUserWishlist($this->user['id']);
            
            // Get wishlist statistics
            $stats = $this->getWishlistStats($this->user['id']);

            $this->render('wishlist/index', [
                'wishlistItems' => $wishlistItems,
                'stats' => $stats,
                'pageTitle' => 'My Wishlist'
            ]);
        } catch (Exception $e) {
            $this->handleException($e, 'Unable to load wishlist');
        }
    }

    public function add()
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'error' => 'Please login first']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request method']);
        }

        $productId = (int)($_POST['product_id'] ?? 0);

        if (!$productId) {
            $this->jsonResponse(['success' => false, 'error' => 'Product ID is required']);
        }

        try {
            // Check if product exists and is active
            $stmt = $this->pdo->prepare("SELECT id, name FROM products WHERE id = ? AND is_active = 1");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();

            if (!$product) {
                $this->jsonResponse(['success' => false, 'error' => 'Product not found']);
            }

            // Add to wishlist
            $result = $this->addToWishlist($this->user['id'], $productId);
            
            if ($result['success']) {
                // Get updated wishlist count
                $count = $this->getWishlistCount($this->user['id']);
                $result['wishlist_count'] = $count;
            }

            $this->jsonResponse($result);
        } catch (Exception $e) {
            $this->logError("Error adding to wishlist: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to add to wishlist']);
        }
    }

    public function remove()
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'error' => 'Please login first']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request method']);
        }

        $productId = (int)($_POST['product_id'] ?? 0);

        if (!$productId) {
            $this->jsonResponse(['success' => false, 'error' => 'Product ID is required']);
        }

        try {
            $result = $this->removeFromWishlist($this->user['id'], $productId);
            
            if ($result['success']) {
                // Get updated wishlist count
                $count = $this->getWishlistCount($this->user['id']);
                $result['wishlist_count'] = $count;
            }

            $this->jsonResponse($result);
        } catch (Exception $e) {
            $this->logError("Error removing from wishlist: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to remove from wishlist']);
        }
    }

    public function toggle()
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'error' => 'Please login first']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request method']);
        }

        $productId = (int)($_POST['product_id'] ?? 0);

        if (!$productId) {
            $this->jsonResponse(['success' => false, 'error' => 'Product ID is required']);
        }

        try {
            $isInWishlist = $this->isInWishlist($this->user['id'], $productId);
            
            if ($isInWishlist) {
                $result = $this->removeFromWishlist($this->user['id'], $productId);
                $result['action'] = 'removed';
            } else {
                $result = $this->addToWishlist($this->user['id'], $productId);
                $result['action'] = 'added';
            }
            
            if ($result['success']) {
                $count = $this->getWishlistCount($this->user['id']);
                $result['wishlist_count'] = $count;
                $result['in_wishlist'] = !$isInWishlist;
            }

            $this->jsonResponse($result);
        } catch (Exception $e) {
            $this->logError("Error toggling wishlist: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to update wishlist']);
        }
    }

    public function moveToCart()
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'error' => 'Please login first']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request method']);
        }

        try {
            $productIds = $_POST['product_ids'] ?? null;
            
            if ($productIds && is_array($productIds)) {
                $productIds = array_map('intval', $productIds);
            }

            $result = $this->moveWishlistToCart($this->user['id'], $productIds);
            $this->jsonResponse($result);
        } catch (Exception $e) {
            $this->logError("Error moving wishlist to cart: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to move items to cart']);
        }
    }

    public function clear()
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'error' => 'Please login first']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request method']);
        }

        try {
            $stmt = $this->pdo->prepare("DELETE FROM wishlist_items WHERE user_id = ?");
            
            if ($stmt->execute([$this->user['id']])) {
                $this->jsonResponse(['success' => true, 'message' => 'Wishlist cleared successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'error' => 'Failed to clear wishlist']);
            }
        } catch (Exception $e) {
            $this->logError("Error clearing wishlist: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to clear wishlist']);
        }
    }

    // Helper methods
    private function getUserWishlist($userId)
    {
        $stmt = $this->pdo->prepare("
            SELECT wi.*, p.*, c.name as category_name, pi.image_path as primary_image
            FROM wishlist_items wi
            JOIN products p ON wi.product_id = p.id
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
            WHERE wi.user_id = ? AND p.is_active = 1
            ORDER BY wi.created_at DESC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    private function getWishlistStats($userId)
    {
        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(*) as total_items,
                SUM(p.price) as total_value,
                AVG(p.price) as average_price,
                COUNT(CASE WHEN p.stock_quantity > 0 THEN 1 END) as in_stock_items,
                COUNT(CASE WHEN p.stock_quantity = 0 THEN 1 END) as out_of_stock_items
            FROM wishlist_items wi
            JOIN products p ON wi.product_id = p.id
            WHERE wi.user_id = ? AND p.is_active = 1
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }

    private function addToWishlist($userId, $productId)
    {
        // Check if already in wishlist
        $stmt = $this->pdo->prepare("SELECT id FROM wishlist_items WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$userId, $productId]);
        
        if ($stmt->fetch()) {
            return ['success' => false, 'error' => 'Product already in wishlist'];
        }

        // Add to wishlist
        $stmt = $this->pdo->prepare("INSERT INTO wishlist_items (user_id, product_id, created_at) VALUES (?, ?, NOW())");
        
        if ($stmt->execute([$userId, $productId])) {
            return ['success' => true, 'message' => 'Product added to wishlist'];
        } else {
            return ['success' => false, 'error' => 'Failed to add to wishlist'];
        }
    }

    private function removeFromWishlist($userId, $productId)
    {
        $stmt = $this->pdo->prepare("DELETE FROM wishlist_items WHERE user_id = ? AND product_id = ?");
        
        if ($stmt->execute([$userId, $productId])) {
            return ['success' => true, 'message' => 'Product removed from wishlist'];
        } else {
            return ['success' => false, 'error' => 'Failed to remove from wishlist'];
        }
    }

    private function isInWishlist($userId, $productId)
    {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM wishlist_items WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$userId, $productId]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }

    private function getWishlistCount($userId)
    {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM wishlist_items wi
            JOIN products p ON wi.product_id = p.id
            WHERE wi.user_id = ? AND p.is_active = 1
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        return $result['count'];
    }

    private function moveWishlistToCart($userId, $productIds = null)
    {
        // Get wishlist items
        if ($productIds) {
            $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
            $sql = "
                SELECT wi.product_id, p.price, p.stock_quantity
                FROM wishlist_items wi
                JOIN products p ON wi.product_id = p.id
                WHERE wi.user_id = ? AND wi.product_id IN ($placeholders) AND p.is_active = 1
            ";
            $params = array_merge([$userId], $productIds);
        } else {
            $sql = "
                SELECT wi.product_id, p.price, p.stock_quantity
                FROM wishlist_items wi
                JOIN products p ON wi.product_id = p.id
                WHERE wi.user_id = ? AND p.is_active = 1
            ";
            $params = [$userId];
        }

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        $items = $stmt->fetchAll();

        $movedCount = 0;
        $errors = [];

        foreach ($items as $item) {
            if ($item['stock_quantity'] > 0) {
                // Add to cart
                $stmt = $this->pdo->prepare("
                    INSERT INTO cart_items (user_id, product_id, quantity, created_at)
                    VALUES (?, ?, 1, NOW())
                    ON DUPLICATE KEY UPDATE quantity = quantity + 1
                ");
                
                if ($stmt->execute([$userId, $item['product_id']])) {
                    // Remove from wishlist
                    $this->removeFromWishlist($userId, $item['product_id']);
                    $movedCount++;
                }
            } else {
                $errors[] = "Product ID {$item['product_id']} is out of stock";
            }
        }

        return [
            'success' => true,
            'moved_count' => $movedCount,
            'errors' => $errors,
            'message' => "$movedCount items moved to cart"
        ];
    }
}
