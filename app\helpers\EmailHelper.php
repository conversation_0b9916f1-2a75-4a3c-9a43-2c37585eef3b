<?php

require_once ROOT_PATH . '/vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

/**
 * Email Helper Class
 * Handles email sending with templates
 */
class EmailHelper
{
    private static $pdo;

    /**
     * Initialize email helper
     */
    public static function init($pdo)
    {
        self::$pdo = $pdo;
    }

    /**
     * Send email using template
     */
    public static function sendTemplateEmail($templateName, $to, $variables = [])
    {
        try {
            // Get email template
            $stmt = self::$pdo->prepare("SELECT * FROM email_templates WHERE name = ? AND is_active = 1");
            $stmt->execute([$templateName]);
            $template = $stmt->fetch();

            if (!$template) {
                throw new Exception("Email template '$templateName' not found");
            }

            // Replace variables in subject and body
            $subject = self::replaceVariables($template['subject'], $variables);
            $body = self::replaceVariables($template['body'], $variables);

            return self::sendEmail($to, $subject, $body, true);

        } catch (Exception $e) {
            error_log("Template email error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send plain email
     */
    public static function sendEmail($to, $subject, $body, $isHtml = false)
    {
        $mail = new PHPMailer(true);

        try {
            // Server settings
            $mail->isSMTP();
            $mail->Host = SMTP_HOST;
            $mail->SMTPAuth = true;
            $mail->Username = SMTP_USERNAME;
            $mail->Password = SMTP_PASSWORD;
            $mail->SMTPSecure = SMTP_ENCRYPTION;
            $mail->Port = SMTP_PORT;

            // Recipients
            $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
            $mail->addAddress($to);

            // Content
            $mail->isHTML($isHtml);
            $mail->Subject = $subject;
            $mail->Body = $body;

            if ($isHtml) {
                $mail->AltBody = strip_tags($body);
            }

            $mail->send();
            return true;

        } catch (Exception $e) {
            error_log("Email sending error: " . $mail->ErrorInfo);
            return false;
        }
    }

    /**
     * Replace variables in template
     */
    private static function replaceVariables($text, $variables)
    {
        foreach ($variables as $key => $value) {
            $text = str_replace('{' . $key . '}', $value, $text);
        }
        return $text;
    }

    /**
     * Send order confirmation email
     */
    public static function sendOrderConfirmation($order, $user)
    {
        $variables = [
            'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
            'order_number' => $order['order_number'],
            'order_total' => '$' . number_format($order['total_amount'], 2),
            'order_date' => date('F j, Y', strtotime($order['created_at'])),
            'app_name' => APP_NAME
        ];

        return self::sendTemplateEmail('order_confirmation', $user['email'], $variables);
    }

    /**
     * Send password reset email
     */
    public static function sendPasswordReset($user, $resetToken)
    {
        $resetLink = APP_URL . '/reset-password/' . $resetToken;
        $expiresAt = date('F j, Y \a\t g:i A', strtotime('+1 hour'));

        $variables = [
            'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
            'reset_link' => $resetLink,
            'app_name' => APP_NAME,
            'expires_at' => $expiresAt
        ];

        return self::sendTemplateEmail('password_reset', $user['email'], $variables);
    }

    /**
     * Send welcome email
     */
    public static function sendWelcomeEmail($user, $verificationToken = null)
    {
        $variables = [
            'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
            'app_name' => APP_NAME
        ];

        if ($verificationToken) {
            $variables['verification_link'] = APP_URL . '/verify/' . $verificationToken;
        }

        return self::sendTemplateEmail('welcome', $user['email'], $variables);
    }

    /**
     * Send order status update email
     */
    public static function sendOrderStatusUpdate($order, $user, $newStatus)
    {
        $statusMessages = [
            'processing' => 'Your order is being processed',
            'shipped' => 'Your order has been shipped',
            'delivered' => 'Your order has been delivered',
            'cancelled' => 'Your order has been cancelled'
        ];

        $variables = [
            'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
            'order_number' => $order['order_number'],
            'status_message' => $statusMessages[$newStatus] ?? 'Order status updated',
            'tracking_number' => $order['tracking_number'] ?? 'N/A',
            'app_name' => APP_NAME
        ];

        // Create a simple email template for status updates
        $subject = "Order Update - {order_number}";
        $body = "
            <h2>Order Status Update</h2>
            <p>Dear {customer_name},</p>
            <p>{status_message}</p>
            <p><strong>Order Number:</strong> {order_number}</p>
            <p><strong>Tracking Number:</strong> {tracking_number}</p>
            <p>Thank you for shopping with {app_name}!</p>
        ";

        $subject = self::replaceVariables($subject, $variables);
        $body = self::replaceVariables($body, $variables);

        return self::sendEmail($user['email'], $subject, $body, true);
    }

    /**
     * Send low stock alert to admin
     */
    public static function sendLowStockAlert($product)
    {
        try {
            // Get admin users
            $stmt = self::$pdo->prepare("SELECT email FROM users WHERE is_admin = 1 AND is_active = 1");
            $stmt->execute();
            $admins = $stmt->fetchAll();

            $subject = "Low Stock Alert - " . $product['name'];
            $body = "
                <h2>Low Stock Alert</h2>
                <p>The following product is running low on stock:</p>
                <p><strong>Product:</strong> {$product['name']}</p>
                <p><strong>SKU:</strong> {$product['sku']}</p>
                <p><strong>Current Stock:</strong> {$product['stock_quantity']}</p>
                <p>Please restock this item soon.</p>
            ";

            $success = true;
            foreach ($admins as $admin) {
                if (!self::sendEmail($admin['email'], $subject, $body, true)) {
                    $success = false;
                }
            }

            return $success;

        } catch (Exception $e) {
            error_log("Low stock alert error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send contact form notification
     */
    public static function sendContactNotification($contactData)
    {
        try {
            // Get admin users
            $stmt = self::$pdo->prepare("SELECT email FROM users WHERE is_admin = 1 AND is_active = 1");
            $stmt->execute();
            $admins = $stmt->fetchAll();

            $subject = "New Contact Form Submission - " . $contactData['subject'];
            $body = "
                <h2>New Contact Form Submission</h2>
                <p><strong>Name:</strong> {$contactData['name']}</p>
                <p><strong>Email:</strong> {$contactData['email']}</p>
                <p><strong>Subject:</strong> {$contactData['subject']}</p>
                <p><strong>Message:</strong></p>
                <p>{$contactData['message']}</p>
                <p><strong>Submitted:</strong> " . date('F j, Y \a\t g:i A') . "</p>
            ";

            $success = true;
            foreach ($admins as $admin) {
                if (!self::sendEmail($admin['email'], $subject, $body, true)) {
                    $success = false;
                }
            }

            return $success;

        } catch (Exception $e) {
            error_log("Contact notification error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test email configuration
     */
    public static function testEmailConfig($testEmail = null)
    {
        $testEmail = $testEmail ?: SMTP_USERNAME;
        
        $subject = "Email Configuration Test - " . APP_NAME;
        $body = "
            <h2>Email Test</h2>
            <p>This is a test email to verify your email configuration is working correctly.</p>
            <p><strong>Sent from:</strong> " . APP_NAME . "</p>
            <p><strong>Sent at:</strong> " . date('F j, Y \a\t g:i A') . "</p>
        ";

        return self::sendEmail($testEmail, $subject, $body, true);
    }
}
