<?php

// Application configuration
define('APP_NAME', 'Cleanance Lab');

// Dynamic URL detection
function getBaseUrl()
{
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $scriptName = $_SERVER['SCRIPT_NAME'];
    $basePath = dirname($scriptName);

    return $protocol . '://' . $host . $basePath;
}

define('APP_URL', getBaseUrl());
define('APP_VERSION', '1.0.0');

// Security settings
define('SECRET_KEY', 'your-secret-key-change-this-in-production');
define('CSRF_TOKEN_NAME', 'csrf_token');

// Session settings
define('SESSION_NAME', 'cleanance_session');
define('SESSION_LIFETIME', 3600); // 1 hour

// Upload settings
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Pagination
define('ITEMS_PER_PAGE', 12);

// Email settings
// OVH SMTP
define('SMTP_HOST', 'ssl0.ovh.net');
define('SMTP_PORT', 465);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'inspecteur..1974');
define('SMTP_ENCRYPTION', 'ssl');

// Payment settings (for demo purposes)
define('STRIPE_PUBLIC_KEY', 'pk_test_your_stripe_public_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');
define('PAYPAL_CLIENT_ID', 'your_paypal_client_id');
define('PAYPAL_CLIENT_SECRET', 'your_paypal_client_secret');

// File paths
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', dirname(__DIR__) . '/app');
define('PUBLIC_PATH', dirname(__DIR__) . '/public');
define('UPLOAD_PATH', PUBLIC_PATH . '/uploads');

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Debug mode
define('DEBUG_MODE', true); // Set to false in production

// Timezone - Set to your local timezone
date_default_timezone_set('Africa/Tunis');
