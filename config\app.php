<?php

// Load environment configuration
require_once __DIR__ . '/env.php';
EnvLoader::load();

// Application configuration
define('APP_NAME', env('APP_NAME', 'Cleanance Lab'));
define('APP_VERSION', env('APP_VERSION', '1.0.0'));
define('APP_ENV', env('APP_ENV', 'development'));

// Dynamic URL detection
function getBaseUrl()
{
    // Check if running from CLI
    if (php_sapi_name() === 'cli') {
        return env('APP_URL', 'http://localhost');
    }

    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
    $basePath = dirname($scriptName);

    return $protocol . '://' . $host . $basePath;
}

define('APP_URL', env('APP_URL', getBaseUrl()));

// Security settings
define('SECRET_KEY', env('SECRET_KEY', bin2hex(random_bytes(32))));
define('CSRF_TOKEN_NAME', env('CSRF_TOKEN_NAME', 'csrf_token'));

// Session settings
define('SESSION_NAME', env('SESSION_NAME', 'cleanance_session'));
define('SESSION_LIFETIME', env('SESSION_LIFETIME', 3600));

// Upload settings
define('UPLOAD_MAX_SIZE', env('UPLOAD_MAX_SIZE', 5 * 1024 * 1024));
define('ALLOWED_IMAGE_TYPES', explode(',', env('ALLOWED_IMAGE_TYPES', 'jpg,jpeg,png,gif,webp')));

// Pagination
define('ITEMS_PER_PAGE', env('ITEMS_PER_PAGE', 12));

// Email settings
define('SMTP_HOST', env('SMTP_HOST', 'localhost'));
define('SMTP_PORT', env('SMTP_PORT', 587));
define('SMTP_USERNAME', env('SMTP_USERNAME', ''));
define('SMTP_PASSWORD', env('SMTP_PASSWORD', ''));
define('SMTP_ENCRYPTION', env('SMTP_ENCRYPTION', 'tls'));
define('SMTP_FROM_EMAIL', env('SMTP_FROM_EMAIL', '<EMAIL>'));
define('SMTP_FROM_NAME', env('SMTP_FROM_NAME', 'Cleanance Lab'));

// Payment settings
define('STRIPE_PUBLIC_KEY', env('STRIPE_PUBLIC_KEY', ''));
define('STRIPE_SECRET_KEY', env('STRIPE_SECRET_KEY', ''));
define('PAYPAL_CLIENT_ID', env('PAYPAL_CLIENT_ID', ''));
define('PAYPAL_CLIENT_SECRET', env('PAYPAL_CLIENT_SECRET', ''));

// Cache settings
define('CACHE_DRIVER', env('CACHE_DRIVER', 'file'));
define('CACHE_TTL', env('CACHE_TTL', 3600));

// Rate limiting
define('LOGIN_RATE_LIMIT', env('LOGIN_RATE_LIMIT', 5));
define('LOGIN_RATE_WINDOW', env('LOGIN_RATE_WINDOW', 300));

// File paths
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', dirname(__DIR__) . '/app');
define('PUBLIC_PATH', dirname(__DIR__) . '/public');
define('UPLOAD_PATH', PUBLIC_PATH . '/uploads');

// Error reporting based on environment
$debugMode = env('DEBUG_MODE', false);
define('DEBUG_MODE', $debugMode);

if ($debugMode && APP_ENV === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', ROOT_PATH . '/logs/error.log');
}

// Timezone
date_default_timezone_set(env('APP_TIMEZONE', 'UTC'));

// Security headers function
function setSecurityHeaders()
{
    // Skip headers in CLI mode
    if (php_sapi_name() === 'cli') {
        return;
    }

    // Prevent clickjacking
    header('X-Frame-Options: DENY');

    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');

    // XSS Protection
    header('X-XSS-Protection: 1; mode=block');

    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // Content Security Policy
    if (env('ENABLE_CSP', true)) {
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';");
    }

    // HTTPS redirect
    if (env('ENABLE_HTTPS_REDIRECT', false) && !isset($_SERVER['HTTPS'])) {
        $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        header("Location: $redirectURL");
        exit();
    }

    // HSTS Header
    if (env('ENABLE_HSTS', false) && isset($_SERVER['HTTPS'])) {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

// Apply security headers
setSecurityHeaders();
