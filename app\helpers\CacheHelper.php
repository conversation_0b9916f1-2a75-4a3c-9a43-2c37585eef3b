<?php

/**
 * Cache Helper Class
 * Provides caching functionality for improved performance
 */
class CacheHelper
{
    private static $pdo;
    private static $memoryCache = [];

    /**
     * Initialize cache with database connection
     */
    public static function init($pdo)
    {
        self::$pdo = $pdo;
    }

    /**
     * Get cached value
     */
    public static function get($key, $default = null)
    {
        // Check memory cache first
        if (isset(self::$memoryCache[$key])) {
            return self::$memoryCache[$key];
        }

        try {
            $stmt = self::$pdo->prepare("SELECT cache_value FROM cache WHERE cache_key = ? AND expires_at > NOW()");
            $stmt->execute([$key]);
            $result = $stmt->fetch();

            if ($result) {
                $value = unserialize($result['cache_value']);
                self::$memoryCache[$key] = $value;
                return $value;
            }
        } catch (PDOException $e) {
            error_log("Cache get error: " . $e->getMessage());
        }

        return $default;
    }

    /**
     * Set cached value
     */
    public static function set($key, $value, $ttl = null)
    {
        $ttl = $ttl ?: CACHE_TTL;
        $expiresAt = date('Y-m-d H:i:s', time() + $ttl);
        $serializedValue = serialize($value);

        // Store in memory cache
        self::$memoryCache[$key] = $value;

        try {
            $stmt = self::$pdo->prepare("
                INSERT INTO cache (cache_key, cache_value, expires_at) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE cache_value = VALUES(cache_value), expires_at = VALUES(expires_at)
            ");
            return $stmt->execute([$key, $serializedValue, $expiresAt]);
        } catch (PDOException $e) {
            error_log("Cache set error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete cached value
     */
    public static function delete($key)
    {
        unset(self::$memoryCache[$key]);

        try {
            $stmt = self::$pdo->prepare("DELETE FROM cache WHERE cache_key = ?");
            return $stmt->execute([$key]);
        } catch (PDOException $e) {
            error_log("Cache delete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear all cache
     */
    public static function clear()
    {
        self::$memoryCache = [];

        try {
            $stmt = self::$pdo->prepare("DELETE FROM cache");
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Cache clear error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean expired cache entries
     */
    public static function cleanup()
    {
        try {
            $stmt = self::$pdo->prepare("DELETE FROM cache WHERE expires_at <= NOW()");
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Cache cleanup error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Remember function - get from cache or execute callback and cache result
     */
    public static function remember($key, $callback, $ttl = null)
    {
        $value = self::get($key);
        
        if ($value === null) {
            $value = $callback();
            self::set($key, $value, $ttl);
        }
        
        return $value;
    }

    /**
     * Cache products with pagination
     */
    public static function getProducts($page = 1, $limit = null, $categoryId = null)
    {
        $limit = $limit ?: ITEMS_PER_PAGE;
        $offset = ($page - 1) * $limit;
        $cacheKey = "products_page_{$page}_limit_{$limit}_cat_" . ($categoryId ?: 'all');

        return self::remember($cacheKey, function() use ($limit, $offset, $categoryId) {
            global $pdo;
            
            $sql = "SELECT p.*, c.name as category_name, pi.image_path 
                    FROM products p 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 
                    WHERE p.is_active = 1";
            
            $params = [];
            if ($categoryId) {
                $sql .= " AND p.category_id = ?";
                $params[] = $categoryId;
            }
            
            $sql .= " ORDER BY p.created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        }, 300); // Cache for 5 minutes
    }

    /**
     * Cache categories
     */
    public static function getCategories()
    {
        return self::remember('categories_active', function() {
            global $pdo;
            $stmt = $pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name");
            $stmt->execute();
            return $stmt->fetchAll();
        }, 1800); // Cache for 30 minutes
    }

    /**
     * Cache product details
     */
    public static function getProduct($id)
    {
        return self::remember("product_detail_{$id}", function() use ($id) {
            global $pdo;
            $stmt = $pdo->prepare("
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.id = ? AND p.is_active = 1
            ");
            $stmt->execute([$id]);
            return $stmt->fetch();
        }, 600); // Cache for 10 minutes
    }

    /**
     * Invalidate product-related cache
     */
    public static function invalidateProductCache($productId = null)
    {
        if ($productId) {
            self::delete("product_detail_{$productId}");
        }
        
        // Clear all product listing cache
        try {
            $stmt = self::$pdo->prepare("DELETE FROM cache WHERE cache_key LIKE 'products_page_%'");
            $stmt->execute();
        } catch (PDOException $e) {
            error_log("Cache invalidation error: " . $e->getMessage());
        }
    }

    /**
     * Invalidate category cache
     */
    public static function invalidateCategoryCache()
    {
        self::delete('categories_active');
        self::invalidateProductCache(); // Products depend on categories
    }
}
