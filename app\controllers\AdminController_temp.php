            // Get recent orders
            $recentOrders = $this->getRecentOrders();

            // Get top products
            $topProducts = $this->getTopProducts();

            // Get sales chart data
            $salesData = $this->getSalesChartData();

            $this->render('admin/dashboard', [
                'stats' => $stats,
                'recentOrders' => $recentOrders,
                'topProducts' => $topProducts,
                'salesData' => $salesData
            ]);
        } catch (Exception $e) {
            error_log("Dashboard error: " . $e->getMessage());
            $this->render('admin/dashboard', [
                'stats' => [],
                'recentOrders' => [],
                'topProducts' => [],
                'salesData' => []
            ]);
        }
    }

    private function getDashboardStats()
    {
        $stats = [];

        // Total users
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
        $stmt->execute();
        $stats['totalUsers'] = $stmt->fetch()['count'];

        // Total products
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
        $stmt->execute();
        $stats['totalProducts'] = $stmt->fetch()['count'];

        // Total orders
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders");
        $stmt->execute();
        $stats['totalOrders'] = $stmt->fetch()['count'];

        // Total revenue
        $stmt = $this->pdo->prepare("SELECT SUM(total_amount) as total FROM orders WHERE status != 'cancelled'");
        $stmt->execute();
        $stats['totalRevenue'] = $stmt->fetch()['total'] ?? 0;

        // Today's orders
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()");
        $stmt->execute();
        $stats['todayOrders'] = $stmt->fetch()['count'];

        // Today's revenue
        $stmt = $this->pdo->prepare("SELECT SUM(total_amount) as total FROM orders WHERE DATE(created_at) = CURDATE() AND status != 'cancelled'");
        $stmt->execute();
        $stats['todayRevenue'] = $stmt->fetch()['total'] ?? 0;

        // Pending orders
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
        $stmt->execute();
        $stats['pendingOrders'] = $stmt->fetch()['count'];

        // Low stock products
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE stock_quantity <= 10 AND is_active = 1");
        $stmt->execute();
        $stats['lowStockProducts'] = $stmt->fetch()['count'];

        return $stats;
    }

    private function getRecentOrders()
    {
        $stmt = $this->pdo->prepare("
            SELECT o.*, u.first_name, u.last_name, u.email 
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            ORDER BY o.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    private function getTopProducts()
    {
        $stmt = $this->pdo->prepare("
            SELECT p.*, COUNT(oi.id) as order_count, SUM(oi.quantity) as total_sold
            FROM products p 
            LEFT JOIN order_items oi ON p.id = oi.product_id 
            WHERE p.is_active = 1 
            GROUP BY p.id 
            ORDER BY total_sold DESC 
            LIMIT 10
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    private function getSalesChartData()
    {
        $stmt = $this->pdo->prepare("
            SELECT DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders
            FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND status != 'cancelled'
            GROUP BY DATE(created_at) 
            ORDER BY date
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function products()
    {
        $page = $_GET['page'] ?? 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        try {
            $stmt = $this->pdo->prepare("
                SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                ORDER BY p.created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            $products = $stmt->fetchAll();

            // Get total count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products");
            $stmt->execute();
            $totalProducts = $stmt->fetch()['count'];
            $totalPages = ceil($totalProducts / $limit);

            $this->render('admin/products', [
                'products' => $products,
                'currentPage' => $page,
                'totalPages' => $totalPages
            ]);
        } catch (Exception $e) {
            error_log("Products error: " . $e->getMessage());
            $this->render('admin/products', [
                'products' => [],
                'currentPage' => 1,
                'totalPages' => 1
            ]);
        }
    }

    public function addProduct()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $this->sanitizeInput($_POST['name'] ?? '');
            $description = $this->sanitizeInput($_POST['description'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $salePrice = floatval($_POST['sale_price'] ?? 0);
            $categoryId = intval($_POST['category_id'] ?? 0);
            $stockQuantity = intval($_POST['stock_quantity'] ?? 0);
            $isActive = isset($_POST['is_active']);

            if (empty($name) || $price <= 0 || $categoryId <= 0) {
                $error = 'Name, price, and category are required.';
            } else {
                try {
                    $image = '';
                    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                        $image = $this->uploadFile($_FILES['image'], 'products');
                    }

                    $stmt = $this->pdo->prepare("
                        INSERT INTO products (name, description, price, sale_price, category_id, stock_quantity, image, is_active, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ");

                    if ($stmt->execute([$name, $description, $price, $salePrice, $categoryId, $stockQuantity, $image, $isActive ? 1 : 0])) {
                        $success = 'Product added successfully!';
                    } else {
                        $error = 'Failed to add product. Please try again.';
                    }
                } catch (Exception $e) {
                    error_log("Add product error: " . $e->getMessage());
                    $error = 'Failed to add product. Please try again.';
                }
            }
        }

        // Get categories for dropdown
        $stmt = $this->pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
        $stmt->execute();
        $categories = $stmt->fetchAll();

        $this->render('admin/add-product', [
            'categories' => $categories,
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function editProduct($id)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM products WHERE id = ?");
            $stmt->execute([$id]);
            $product = $stmt->fetch();

            if (!$product) {
                $this->redirect('admin/products');
                return;
            }

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $name = $this->sanitizeInput($_POST['name'] ?? '');
                $description = $this->sanitizeInput($_POST['description'] ?? '');
                $price = floatval($_POST['price'] ?? 0);
                $salePrice = floatval($_POST['sale_price'] ?? 0);
                $categoryId = intval($_POST['category_id'] ?? 0);
                $stockQuantity = intval($_POST['stock_quantity'] ?? 0);
                $isActive = isset($_POST['is_active']);

                if (empty($name) || $price <= 0 || $categoryId <= 0) {
                    $error = 'Name, price, and category are required.';
                } else {
                    $image = $product['image'];
                    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                        $image = $this->uploadFile($_FILES['image'], 'products');
                    }

                    $stmt = $this->pdo->prepare("
                        UPDATE products 
                        SET name = ?, description = ?, price = ?, sale_price = ?, category_id = ?, stock_quantity = ?, image = ?, is_active = ? 
                        WHERE id = ?
                    ");

                    if ($stmt->execute([$name, $description, $price, $salePrice, $categoryId, $stockQuantity, $image, $isActive ? 1 : 0, $id])) {
                        $success = 'Product updated successfully!';
                        $product = $this->pdo->prepare("SELECT * FROM products WHERE id = ?")->execute([$id])->fetch();
                    } else {
                        $error = 'Failed to update product. Please try again.';
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Edit product error: " . $e->getMessage());
            $error = 'Failed to update product. Please try again.';
        }

        // Get categories for dropdown
        $stmt = $this->pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
        $stmt->execute();
        $categories = $stmt->fetchAll();

        $this->render('admin/edit-product', [
            'product' => $product ?? null,
            'categories' => $categories,
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function deleteProduct($id)
    {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM products WHERE id = ?");
            $stmt->execute([$id]);
            $success = 'Product deleted successfully!';
        } catch (Exception $e) {
            error_log("Delete product error: " . $e->getMessage());
            $error = 'Failed to delete product. Please try again.';
        }

        $this->redirect('admin/products');
    }

    public function orders()
    {
        $page = $_GET['page'] ?? 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        try {
            $stmt = $this->pdo->prepare("
                SELECT o.*, u.first_name, u.last_name, u.email 
                FROM orders o 
                JOIN users u ON o.user_id = u.id 
                ORDER BY o.created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            $orders = $stmt->fetchAll();

            // Get total count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders");
            $stmt->execute();
            $totalOrders = $stmt->fetch()['count'];
            $totalPages = ceil($totalOrders / $limit);

            $this->render('admin/orders', [
                'orders' => $orders,
                'currentPage' => $page,
                'totalPages' => $totalPages
            ]);
        } catch (Exception $e) {
            error_log("Orders error: " . $e->getMessage());
            $this->render('admin/orders', [
                'orders' => [],
                'currentPage' => 1,
                'totalPages' => 1
            ]);
        }
    }

    public function orderDetail($id)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT o.*, u.first_name, u.last_name, u.email 
                FROM orders o 
                JOIN users u ON o.user_id = u.id 
                WHERE o.id = ?
            ");
            $stmt->execute([$id]);
            $order = $stmt->fetch();

            if (!$order) {
                $this->redirect('admin/orders');
                return;
            }

            // Get order items
            $stmt = $this->pdo->prepare("
                SELECT oi.*, p.name, p.image 
                FROM order_items oi 
                JOIN products p ON oi.product_id = p.id 
                WHERE oi.order_id = ?
            ");
            $stmt->execute([$id]);
            $orderItems = $stmt->fetchAll();

            $this->render('admin/order-detail', [
                'order' => $order,
                'orderItems' => $orderItems
            ]);
        } catch (Exception $e) {
            error_log("Order detail error: " . $e->getMessage());
            $this->redirect('admin/orders');
        }
    }

    public function updateOrderStatus($id)
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $status = $this->sanitizeInput($_POST['status'] ?? '');

            try {
                $stmt = $this->pdo->prepare("UPDATE orders SET status = ? WHERE id = ?");
                $stmt->execute([$status, $id]);
                $success = 'Order status updated successfully!';
            } catch (Exception $e) {
                error_log("Update order status error: " . $e->getMessage());
                $error = 'Failed to update order status. Please try again.';
            }
        }

        $this->redirect('admin/orders/' . $id);
    }

    public function users()
    {
        $page = $_GET['page'] ?? 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM users 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            $users = $stmt->fetchAll();

            // Get total count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM users");
            $stmt->execute();
            $totalUsers = $stmt->fetch()['count'];
            $totalPages = ceil($totalUsers / $limit);

            $this->render('admin/users', [
                'users' => $users,
                'currentPage' => $page,
                'totalPages' => $totalPages
            ]);
        } catch (Exception $e) {
            error_log("Users error: " . $e->getMessage());
            $this->render('admin/users', [
                'users' => [],
                'currentPage' => 1,
                'totalPages' => 1
            ]);
        }
    }

    public function userDetail($id)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$id]);
            $user = $stmt->fetch();

            if (!$user) {
                $this->redirect('admin/users');
                return;
            }

            // Get user orders
            $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC");
            $stmt->execute([$id]);
            $orders = $stmt->fetchAll();

            $this->render('admin/user-detail', [
                'user' => $user,
                'orders' => $orders
            ]);
        } catch (Exception $e) {
            error_log("User detail error: " . $e->getMessage());
            $this->redirect('admin/users');
        }
    }

    public function toggleUserStatus($id)
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $stmt = $this->pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$id]);
                $success = 'User status updated successfully!';
            } catch (Exception $e) {
                error_log("Toggle user status error: " . $e->getMessage());
                $error = 'Failed to update user status. Please try again.';
            }
        }

        $this->redirect('admin/users/' . $id);
    }

    public function categories()
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM categories ORDER BY name");
            $stmt->execute();
            $categories = $stmt->fetchAll();

            $this->render('admin/categories', ['categories' => $categories]);
        } catch (Exception $e) {
            error_log("Categories error: " . $e->getMessage());
            $this->render('admin/categories', ['categories' => []]);
        }
    }

    public function addCategory()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $this->sanitizeInput($_POST['name'] ?? '');
            $description = $this->sanitizeInput($_POST['description'] ?? '');
            $isActive = isset($_POST['is_active']);

            if (empty($name)) {
                $error = 'Category name is required.';
            } else {
                try {
                    $stmt = $this->pdo->prepare("
                        INSERT INTO categories (name, description, is_active, created_at) 
                        VALUES (?, ?, ?, NOW())
                    ");

                    if ($stmt->execute([$name, $description, $isActive ? 1 : 0])) {
                        $success = 'Category added successfully!';
                    } else {
                        $error = 'Failed to add category. Please try again.';
                    }
                } catch (Exception $e) {
                    error_log("Add category error: " . $e->getMessage());
                    $error = 'Failed to add category. Please try again.';
                }
            }
        }

        $this->render('admin/add-category', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function editCategory($id)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM categories WHERE id = ?");
            $stmt->execute([$id]);
            $category = $stmt->fetch();

            if (!$category) {
                $this->redirect('admin/categories');
                return;
            }

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $name = $this->sanitizeInput($_POST['name'] ?? '');
                $description = $this->sanitizeInput($_POST['description'] ?? '');
                $isActive = isset($_POST['is_active']);

                if (empty($name)) {
                    $error = 'Category name is required.';
                } else {
                    $stmt = $this->pdo->prepare("
                        UPDATE categories 
                        SET name = ?, description = ?, is_active = ? 
                        WHERE id = ?
                    ");

                    if ($stmt->execute([$name, $description, $isActive ? 1 : 0, $id])) {
                        $success = 'Category updated successfully!';
                        $category = $this->pdo->prepare("SELECT * FROM categories WHERE id = ?")->execute([$id])->fetch();
                    } else {
                        $error = 'Failed to update category. Please try again.';
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Edit category error: " . $e->getMessage());
            $error = 'Failed to update category. Please try again.';
        }

        $this->render('admin/edit-category', [
            'category' => $category ?? null,
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function deleteCategory($id)
    {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM categories WHERE id = ?");
            $stmt->execute([$id]);
            $success = 'Category deleted successfully!';
        } catch (Exception $e) {
            error_log("Delete category error: " . $e->getMessage());
            $error = 'Failed to delete category. Please try again.';
        }

        $this->redirect('admin/categories');
    }

    public function salesReport()
    {
        $startDate = $_GET['start_date'] ?? date('Y-m-01');
        $endDate = $_GET['end_date'] ?? date('Y-m-d');

        try {
            $stmt = $this->pdo->prepare("
                SELECT DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders
                FROM orders 
                WHERE created_at BETWEEN ? AND ? AND status != 'cancelled'
                GROUP BY DATE(created_at) 
                ORDER BY date
            ");
            $stmt->execute([$startDate, $endDate . ' 23:59:59']);
            $salesData = $stmt->fetchAll();

            $this->render('admin/sales-report', [
                'salesData' => $salesData,
                'startDate' => $startDate,
                'endDate' => $endDate
            ]);
        } catch (Exception $e) {
            error_log("Sales report error: " . $e->getMessage());
            $this->render('admin/sales-report', [
                'salesData' => [],
                'startDate' => $startDate,
                'endDate' => $endDate
            ]);
        }
    }

    public function productsReport()
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT p.*, c.name as category_name, COUNT(oi.id) as order_count, SUM(oi.quantity) as total_sold
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN order_items oi ON p.id = oi.product_id 
                WHERE p.is_active = 1 
                GROUP BY p.id 
                ORDER BY total_sold DESC
            ");
            $stmt->execute();
            $products = $stmt->fetchAll();

            $this->render('admin/products-report', ['products' => $products]);
        } catch (Exception $e) {
            error_log("Products report error: " . $e->getMessage());
            $this->render('admin/products-report', ['products' => []]);
        }
    }

    public function usersReport()
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT u.*, COUNT(o.id) as order_count, SUM(o.total_amount) as total_spent
                FROM users u 
                LEFT JOIN orders o ON u.id = o.user_id AND o.status != 'cancelled'
                WHERE u.is_active = 1 
                GROUP BY u.id 
                ORDER BY total_spent DESC
            ");
            $stmt->execute();
            $users = $stmt->fetchAll();

            $this->render('admin/users-report', ['users' => $users]);
        } catch (Exception $e) {
            error_log("Users report error: " . $e->getMessage());
            $this->render('admin/users-report', ['users' => []]);
        }
    }

    public function settings()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $siteName = $this->sanitizeInput($_POST['site_name'] ?? '');
            $siteDescription = $this->sanitizeInput($_POST['site_description'] ?? '');
            $contactEmail = $this->sanitizeInput($_POST['contact_email'] ?? '');
            $currency = $this->sanitizeInput($_POST['currency'] ?? 'USD');

            try {
                // Update settings in database (you'd need a settings table)
                $success = 'Settings updated successfully!';
            } catch (Exception $e) {
                error_log("Settings error: " . $e->getMessage());
                $error = 'Failed to update settings. Please try again.';
            }
        }

        $this->render('admin/settings', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function analytics()
    {
        try {
            $days = intval($_GET['days'] ?? 30);

            // Get metrics
            $metrics = $this->getAnalyticsMetrics($days);

            // Get top products
            $topProducts = $this->getTopProductsForAnalytics($days);

            // Get top categories
            $topCategories = $this->getTopCategoriesForAnalytics($days);

            // Get recent orders
            $recentOrders = $this->getRecentOrdersForAnalytics($days);

            $this->render('admin/analytics', [
                'metrics' => $metrics,
                'top_products' => $topProducts,
                'top_categories' => $topCategories,
                'recent_orders' => $recentOrders,
                'days' => $days
            ]);
        } catch (Exception $e) {
            error_log("Analytics error: " . $e->getMessage());
            $this->render('admin/analytics', [
                'metrics' => [],
                'top_products' => [],
                'top_categories' => [],
                'recent_orders' => [],
                'days' => $days
            ]);
        }
    }

    private function getAnalyticsMetrics($days)
    {
        $metrics = [];

        // Total orders
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        $metrics['total_orders'] = $stmt->fetch()['count'];

        // Previous period orders
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days * 2, $days]);
        $prevOrders = $stmt->fetch()['count'];
        $metrics['orders_change'] = $prevOrders > 0 ? (($metrics['total_orders'] - $prevOrders) / $prevOrders) * 100 : 0;

        // Total revenue
        $stmt = $this->pdo->prepare("
            SELECT SUM(total_amount) as total FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND status != 'cancelled'
        ");
        $stmt->execute([$days]);
        $metrics['total_revenue'] = $stmt->fetch()['total'] ?? 0;

        // Previous period revenue
        $stmt = $this->pdo->prepare("
            SELECT SUM(total_amount) as total FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY) AND status != 'cancelled'
        ");
        $stmt->execute([$days * 2, $days]);
        $prevRevenue = $stmt->fetch()['total'] ?? 0;
        $metrics['revenue_change'] = $prevRevenue > 0 ? (($metrics['total_revenue'] - $prevRevenue) / $prevRevenue) * 100 : 0;

        // Total customers
        $stmt = $this->pdo->prepare("
            SELECT COUNT(DISTINCT user_id) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        $metrics['total_customers'] = $stmt->fetch()['count'];

        // Previous period customers
        $stmt = $this->pdo->prepare("
            SELECT COUNT(DISTINCT user_id) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days * 2, $days]);
        $prevCustomers = $stmt->fetch()['count'];
        $metrics['customers_change'] = $prevCustomers > 0 ? (($metrics['total_customers'] - $prevCustomers) / $prevCustomers) * 100 : 0;

        // Total products
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
        $stmt->execute();
        $metrics['total_products'] = $stmt->fetch()['count'];

        // Average order value
        $stmt = $this->pdo->prepare("
            SELECT AVG(total_amount) as avg FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND status != 'cancelled'
        ");
        $stmt->execute([$days]);
        $metrics['avg_order_value'] = $stmt->fetch()['avg'] ?? 0;

        // Top customer
        $stmt = $this->pdo->prepare("
            SELECT u.first_name, u.last_name, SUM(o.total_amount) as total_spent
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND o.status != 'cancelled'
            GROUP BY o.user_id 
            ORDER BY total_spent DESC 
            LIMIT 1
        ");
        $stmt->execute([$days]);
        $topCustomer = $stmt->fetch();
        $metrics['top_customer'] = $topCustomer ? $topCustomer['first_name'] . ' ' . $topCustomer['last_name'] : 'N/A';

        return $metrics;
    }

    private function getTopProductsForAnalytics($days)
    {
        $stmt = $this->pdo->prepare("
            SELECT p.*, c.name as category_name, 
                   COUNT(oi.id) as sales_count, 
                   SUM(oi.quantity * oi.price) as revenue
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN order_items oi ON p.id = oi.product_id 
            LEFT JOIN orders o ON oi.order_id = o.id 
            WHERE p.is_active = 1 
            AND (o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) OR o.id IS NULL)
            GROUP BY p.id 
            ORDER BY revenue DESC 
            LIMIT 10
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }

    private function getTopCategoriesForAnalytics($days)
    {
        $stmt = $this->pdo->prepare("
            SELECT c.*, 
                   COUNT(oi.id) as sales_count, 
                   SUM(oi.quantity * oi.price) as revenue,
                   COUNT(DISTINCT p.id) as product_count
            FROM categories c 
            LEFT JOIN products p ON c.id = p.category_id 
            LEFT JOIN order_items oi ON p.id = oi.product_id 
            LEFT JOIN orders o ON oi.order_id = o.id 
            WHERE c.is_active = 1 
            AND (o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) OR o.id IS NULL)
            GROUP BY c.id 
            ORDER BY revenue DESC 
            LIMIT 10
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }

    private function getRecentOrdersForAnalytics($days)
    {
        $stmt = $this->pdo->prepare("
            SELECT o.*, u.first_name, u.last_name 
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ORDER BY o.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }

    public function exportOrders()
    {
        $status = $_GET['status'] ?? '';
        $date = $_GET['date'] ?? '';
        $startDate = $_GET['start_date'] ?? '';
        $endDate = $_GET['end_date'] ?? '';

        try {
            $where = [];
            $params = [];

            if ($status) {
                $where[] = "o.status = ?";
                $params[] = $status;
            }

            if ($date) {
                switch ($date) {
                    case 'today':
                        $where[] = "DATE(o.created_at) = CURDATE()";
                        break;
                    case 'week':
                        $where[] = "o.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                        break;
                    case 'month':
                        $where[] = "o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                        break;
                    case 'year':
                        $where[] = "o.created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)";
                        break;
                }
            }

            if ($startDate && $endDate) {
                $where[] = "o.created_at BETWEEN ? AND ?";
                $params[] = $startDate . ' 00:00:00';
                $params[] = $endDate . ' 23:59:59';
            }

            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

            $stmt = $this->pdo->prepare("
                SELECT o.*, u.first_name, u.last_name, u.email 
                FROM orders o 
                JOIN users u ON o.user_id = u.id 
                $whereClause
                ORDER BY o.created_at DESC
            ");
            $stmt->execute($params);
            $orders = $stmt->fetchAll();

            // Generate CSV
            $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            $output = fopen('php://output', 'w');
            fputcsv($output, ['Order ID', 'Customer', 'Email', 'Total', 'Status', 'Payment Method', 'Date']);

            foreach ($orders as $order) {
                fputcsv($output, [
                    $order['id'],
                    $order['first_name'] . ' ' . $order['last_name'],
                    $order['email'],
                    $order['total_amount'],
                    $order['status'],
                    $order['payment_method'],
                    $order['created_at']
                ]);
            }

            fclose($output);
            exit;
        } catch (Exception $e) {
            error_log("Export orders error: " . $e->getMessage());
            header('HTTP/1.1 500 Internal Server Error');
            echo 'Export failed';
        }
    }

    public function exportUsers()
    {
        $role = $_GET['role'] ?? '';
        $status = $_GET['status'] ?? '';
        $date = $_GET['date'] ?? '';

        try {
            $where = [];
            $params = [];

            if ($role) {
                $where[] = "u.is_admin = ?";
                $params[] = $role === 'admin' ? 1 : 0;
            }

            if ($status) {
                $where[] = "u.is_active = ?";
                $params[] = $status;
            }

            if ($date) {
                switch ($date) {
                    case 'today':
                        $where[] = "DATE(u.created_at) = CURDATE()";
                        break;
                    case 'week':
                        $where[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                        break;
                    case 'month':
                        $where[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                        break;
                    case 'year':
                        $where[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)";
                        break;
                }
            }

            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

            $stmt = $this->pdo->prepare("
                SELECT u.*, 
                       COUNT(o.id) as order_count,
                       SUM(o.total_amount) as total_spent
                FROM users u 
                LEFT JOIN orders o ON u.id = o.user_id 
                $whereClause
                GROUP BY u.id 
                ORDER BY u.created_at DESC
            ");
            $stmt->execute($params);
            $users = $stmt->fetchAll();

            // Generate CSV
            $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            $output = fopen('php://output', 'w');
            fputcsv($output, ['ID', 'Name', 'Email', 'Role', 'Orders', 'Total Spent', 'Status', 'Joined']);

            foreach ($users as $user) {
                fputcsv($output, [
                    $user['id'],
                    $user['first_name'] . ' ' . $user['last_name'],
                    $user['email'],
                    $user['is_admin'] ? 'Admin' : 'User',
                    $user['order_count'],
                    $user['total_spent'] ?? 0,
                    $user['is_active'] ? 'Active' : 'Inactive',
                    $user['created_at']
                ]);
            }

            fclose($output);
            exit;
        } catch (Exception $e) {
            error_log("Export users error: " . $e->getMessage());
            header('HTTP/1.1 500 Internal Server Error');
            echo 'Export failed';
        }
    }

    public function toggleProductStatus($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = $input['status'] ?? false;

            $stmt = $this->pdo->prepare("UPDATE products SET is_active = ? WHERE id = ?");
            $result = $stmt->execute([$status ? 1 : 0, $id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Toggle product status error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function toggleCategoryStatus($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = $input['status'] ?? false;

            $stmt = $this->pdo->prepare("UPDATE categories SET is_active = ? WHERE id = ?");
            $result = $stmt->execute([$status ? 1 : 0, $id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Toggle category status error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function toggleUserRole($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $role = $input['role'] ?? 'user';

            $stmt = $this->pdo->prepare("UPDATE users SET is_admin = ? WHERE id = ?");
            $result = $stmt->execute([$role === 'admin' ? 1 : 0, $id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Toggle user role error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function cancelOrder($id)
    {
        try {
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'cancelled' WHERE id = ?");
            $result = $stmt->execute([$id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Cancel order error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function updateOrderStatus($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = $input['status'] ?? '';
            $note = $input['note'] ?? '';

            if (empty($status)) {
                throw new Exception('Status is required');
            }

            $stmt = $this->pdo->prepare("UPDATE orders SET status = ? WHERE id = ?");
            $result = $stmt->execute([$status, $id]);

            // Add status note if provided
            if ($result && !empty($note)) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO order_notes (order_id, note, created_at) 
                    VALUES (?, ?, NOW())
                ");
                $stmt->execute([$id, $note]);
            }

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Update order status error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function exportAnalytics()
    {
        $days = intval($_GET['days'] ?? 30);

        try {
            $metrics = $this->getAnalyticsMetrics($days);
            $topProducts = $this->getTopProductsForAnalytics($days);
            $topCategories = $this->getTopCategoriesForAnalytics($days);

            $filename = 'analytics_export_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            $output = fopen('php://output', 'w');

            // Metrics section
            fputcsv($output, ['Analytics Report - Last ' . $days . ' Days']);
            fputcsv($output, []);
            fputcsv($output, ['Metric', 'Value', 'Change (%)']);
            fputcsv($output, ['Total Orders', $metrics['total_orders'], $metrics['orders_change']]);
            fputcsv($output, ['Total Revenue', '$' . number_format($metrics['total_revenue'], 2), $metrics['revenue_change']]);
            fputcsv($output, ['Total Customers', $metrics['total_customers'], $metrics['customers_change']]);
            fputcsv($output, ['Average Order Value', '$' . number_format($metrics['avg_order_value'], 2), '']);

            fputcsv($output, []);
            fputcsv($output, ['Top Products']);
            fputcsv($output, ['Product', 'Category', 'Sales Count', 'Revenue']);
            foreach ($topProducts as $product) {
                fputcsv($output, [
                    $product['name'],
                    $product['category_name'] ?? 'Uncategorized',
                    $product['sales_count'],
                    '$' . number_format($product['revenue'] ?? 0, 2)
                ]);
            }

            fputcsv($output, []);
            fputcsv($output, ['Top Categories']);
            fputcsv($output, ['Category', 'Sales Count', 'Revenue', 'Products']);
            foreach ($topCategories as $category) {
                fputcsv($output, [
                    $category['name'],
                    $category['sales_count'],
                    '$' . number_format($category['revenue'] ?? 0, 2),
                    $category['product_count']
                ]);
            }

            fclose($output);
            exit;
        } catch (Exception $e) {
            error_log("Export analytics error: " . $e->getMessage());
            header('HTTP/1.1 500 Internal Server Error');
            echo 'Export failed';
        }
    }
}
