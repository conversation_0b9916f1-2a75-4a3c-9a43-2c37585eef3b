<div class="container mt-5">
    <div class="row">
        <!-- Checkout Form -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>Checkout
                    </h4>
                </div>
                <div class="card-body">
                    <form action="/checkout" method="POST" id="checkoutForm">
                        <!-- Billing Information -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-user me-2"></i>Billing Information
                            </h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="billing_first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="billing_first_name" name="billing_first_name"
                                        value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="billing_last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="billing_last_name" name="billing_last_name"
                                        value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="billing_email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="billing_email" name="billing_email"
                                    value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="billing_phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="billing_phone" name="billing_phone"
                                    value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                            </div>
                            <div class="mb-3">
                                <label for="billing_address" class="form-label">Address *</label>
                                <input type="text" class="form-control" id="billing_address" name="billing_address" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="billing_city" class="form-label">City *</label>
                                    <input type="text" class="form-control" id="billing_city" name="billing_city" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="billing_state" class="form-label">State/Province *</label>
                                    <input type="text" class="form-control" id="billing_state" name="billing_state" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="billing_postal_code" class="form-label">Postal Code *</label>
                                    <input type="text" class="form-control" id="billing_postal_code" name="billing_postal_code" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="billing_country" class="form-label">Country *</label>
                                    <input type="text" class="form-control" id="billing_country" name="billing_country" required>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Information -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-shipping-fast me-2"></i>Shipping Information
                                </h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="same_as_billing" checked>
                                    <label class="form-check-label" for="same_as_billing">
                                        Same as billing address
                                    </label>
                                </div>
                            </div>
                            <div id="shipping-fields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="shipping_first_name" name="shipping_first_name">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_last_name" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="shipping_last_name" name="shipping_last_name">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="shipping_address" class="form-label">Address *</label>
                                    <input type="text" class="form-control" id="shipping_address" name="shipping_address">
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_city" class="form-label">City *</label>
                                        <input type="text" class="form-control" id="shipping_city" name="shipping_city">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_state" class="form-label">State/Province *</label>
                                        <input type="text" class="form-control" id="shipping_state" name="shipping_state">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_postal_code" class="form-label">Postal Code *</label>
                                        <input type="text" class="form-control" id="shipping_postal_code" name="shipping_postal_code">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_country" class="form-label">Country *</label>
                                        <input type="text" class="form-control" id="shipping_country" name="shipping_country">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-credit-card me-2"></i>Payment Information
                            </h5>
                            <div class="mb-3">
                                <label for="card_number" class="form-label">Card Number *</label>
                                <input type="text" class="form-control" id="card_number" name="card_number"
                                    placeholder="1234 5678 9012 3456" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="card_expiry" class="form-label">Expiry Date *</label>
                                    <input type="text" class="form-control" id="card_expiry" name="card_expiry"
                                        placeholder="MM/YY" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="card_cvv" class="form-label">CVV *</label>
                                    <input type="text" class="form-control" id="card_cvv" name="card_cvv"
                                        placeholder="123" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="card_name" class="form-label">Name on Card *</label>
                                <input type="text" class="form-control" id="card_name" name="card_name" required>
                            </div>
                        </div>

                        <!-- Order Notes -->
                        <div class="mb-4">
                            <label for="order_notes" class="form-label">Order Notes (Optional)</label>
                            <textarea class="form-control" id="order_notes" name="order_notes" rows="3"
                                placeholder="Any special instructions or notes for your order..."></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-lock me-2"></i>Place Order
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Order Summary
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($cartItems)): ?>
                        <?php foreach ($cartItems as $item): ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <img src="<?php echo htmlspecialchars($item['image_path']); ?>"
                                        alt="<?php echo htmlspecialchars($item['name']); ?>"
                                        class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($item['name']); ?></h6>
                                        <small class="text-muted">Qty: <?php echo $item['quantity']; ?></small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold">$<?php echo number_format($item['price'] * $item['quantity'], 2); ?></div>
                                    <?php if ($item['sale_price']): ?>
                                        <small class="text-muted text-decoration-line-through">
                                            $<?php echo number_format($item['original_price'] * $item['quantity'], 2); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <hr>

                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>$<?php echo number_format($subtotal, 2); ?></span>
                        </div>

                        <?php if (isset($discount) && $discount > 0): ?>
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>Discount:</span>
                                <span>-$<?php echo number_format($discount, 2); ?></span>
                            </div>
                        <?php endif; ?>

                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span>$<?php echo number_format($shipping, 2); ?></span>
                        </div>

                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span>$<?php echo number_format($tax, 2); ?></span>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>Total:</span>
                            <span>$<?php echo number_format($total, 2); ?></span>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart text-muted mb-3" style="font-size: 3rem;"></i>
                            <h6 class="text-muted">Your cart is empty</h6>
                            <a href="/products" class="btn btn-primary">Continue Shopping</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle shipping fields
    document.getElementById('same_as_billing').addEventListener('change', function() {
        const shippingFields = document.getElementById('shipping-fields');
        const shippingInputs = shippingFields.querySelectorAll('input');

        if (this.checked) {
            shippingFields.style.display = 'none';
            shippingInputs.forEach(input => input.removeAttribute('required'));
        } else {
            shippingFields.style.display = 'block';
            shippingInputs.forEach(input => input.setAttribute('required', 'required'));
        }
    });

    // Copy billing to shipping
    document.getElementById('same_as_billing').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('shipping_first_name').value = document.getElementById('billing_first_name').value;
            document.getElementById('shipping_last_name').value = document.getElementById('billing_last_name').value;
            document.getElementById('shipping_address').value = document.getElementById('billing_address').value;
            document.getElementById('shipping_city').value = document.getElementById('billing_city').value;
            document.getElementById('shipping_state').value = document.getElementById('billing_state').value;
            document.getElementById('shipping_postal_code').value = document.getElementById('billing_postal_code').value;
            document.getElementById('shipping_country').value = document.getElementById('billing_country').value;
        }
    });

    // Form validation
    document.getElementById('checkoutForm').addEventListener('submit', function(e) {
        const cardNumber = document.getElementById('card_number').value.replace(/\s/g, '');
        const cardExpiry = document.getElementById('card_expiry').value;
        const cardCvv = document.getElementById('card_cvv').value;

        // Basic card validation
        if (cardNumber.length < 13 || cardNumber.length > 19) {
            e.preventDefault();
            alert('Please enter a valid card number.');
            return;
        }

        if (!/^\d{2}\/\d{2}$/.test(cardExpiry)) {
            e.preventDefault();
            alert('Please enter expiry date in MM/YY format.');
            return;
        }

        if (cardCvv.length < 3 || cardCvv.length > 4) {
            e.preventDefault();
            alert('Please enter a valid CVV.');
            return;
        }

        // Disable submit button to prevent double submission
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });
</script>

<style>
    .card {
        border: none;
        border-radius: 15px;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid #dee2e6;
    }

    .btn {
        border-radius: 8px;
        font-weight: 500;
    }

    .form-control {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
    }

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }
</style>