<section class="admin-section">
    <div class="admin-header">
        <h1>Discount Codes</h1>
        <a href="/admin/discount-codes/add" class="btn btn-primary">Add Discount Code</a>
    </div>
    <div class="admin-table-wrapper">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Code</th>
                    <th>Type</th>
                    <th>Value</th>
                    <th>Min Order</th>
                    <th>Max Discount</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($discountCodes)): foreach ($discountCodes as $code): ?>
                <tr>
                    <td><?= $code['id'] ?></td>
                    <td><?= htmlspecialchars($code['code']) ?></td>
                    <td><?= ucfirst($code['discount_type']) ?></td>
                    <td><?= $code['discount_value'] ?></td>
                    <td><?= $code['minimum_order_amount'] ?></td>
                    <td><?= $code['maximum_discount'] ?></td>
                    <td><?= $code['is_active'] ? 'Active' : 'Inactive' ?></td>
                    <td>
                        <a href="/admin/discount-codes/edit/<?= $code['id'] ?>" class="btn btn-sm">Edit</a>
                        <form action="/admin/discount-codes/delete/<?= $code['id'] ?>" method="POST" style="display:inline;">
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Delete this code?')">Delete</button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; else: ?>
                <tr><td colspan="8" style="text-align:center; color:var(--text-secondary);">No discount codes found.</td></tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</section>
<style>
    .admin-section {
        padding: 40px 0;
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
    }

    .admin-table-wrapper {
        overflow-x: auto;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--dark-card);
    }

    .admin-table th,
    .admin-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--dark-surface);
        color: var(--primary-purple);
        font-weight: 600;
    }

    .btn-sm {
        padding: 4px 12px;
        font-size: 0.95em;
    }

    .btn-danger {
        background: #e53935;
        color: #fff;
    }

    .btn-danger:hover {
        background: #b71c1c;
    }
</style>