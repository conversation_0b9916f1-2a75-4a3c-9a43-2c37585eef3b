# Cleanance Lab - Modern E-commerce Website

A complete, modern e-commerce platform built with PHP, featuring a beautiful dark mode design with purple accents.

## Features

### User Management

- User registration, login, logout
- Password reset functionality
- User profile management
- Secure authentication with password hashing

### Product Management

- Product catalog with categories
- Advanced filtering and search
- Product detail pages with images and descriptions
- Product ratings and reviews system

### Shopping Experience

- Shopping cart with quantity management
- Wishlist and favorites
- Discount code system
- Checkout process with address input
- Order tracking and history

### Admin Dashboard

- Product management (CRUD operations)
- Category management
- Order management
- User management
- Analytics and reports

### Technical Features

- Responsive mobile-first design
- Dark mode toggle ("Sombre Mode")
- SEO-friendly routing
- CSRF/XSS protection
- Email notifications
- Modern UI with smooth animations

## Installation

### Prerequisites

- PHP 8.0 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- Composer (for dependencies)

### Setup Instructions

1. **Clone or download the project**

   ```bash
   git clone <repository-url>
   cd cleanance-lab
   ```

2. **Configure your web server**

   - Point your web server document root to the `public` folder
   - Ensure mod_rewrite is enabled for Apache

3. **Database Setup**

   ```sql
   CREATE DATABASE cleanance_lab;
   CREATE USER 'cleanance_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON cleanance_lab.* TO 'cleanance_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **Configure Database Connection**

   - Edit `config/database.php`
   - Update database credentials

5. **Import Database Schema**

   ```bash
   mysql -u cleanance_user -p cleanance_lab < database/schema.sql
   ```

6. **Set Permissions**

   ```bash
   chmod 755 public/uploads/
   chmod 755 public/uploads/products/
   chmod 755 public/uploads/users/
   ```

7. **Access the Application**
   - Navigate to your domain in a web browser
   - Register a new admin account
   - Access admin panel at `/admin`

## Project Structure

```
cleanance-lab/
├── app/
│   ├── controllers/
│   ├── models/
│   ├── views/
│   └── helpers/
├── config/
├── database/
├── public/
│   ├── assets/
│   ├── uploads/
│   └── index.php
├── routes/
└── README.md
```

## Color Palette

- **Primary Purple**: #7E57C2
- **White**: #FFFFFF
- **Dark Background**: #1a1a1a
- **Card Background**: #2d2d2d
- **Text**: #e0e0e0

## Technologies Used

- **Backend**: PHP 8.0+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Database**: MySQL
- **Styling**: Custom CSS with Google Fonts
- **Icons**: Font Awesome
- **Animations**: CSS Transitions and Keyframes

## Security Features

- Password hashing with bcrypt
- CSRF token protection
- XSS prevention
- SQL injection prevention
- Input validation and sanitization
- Secure session management

## License

This project is open source and available under the MIT License.
