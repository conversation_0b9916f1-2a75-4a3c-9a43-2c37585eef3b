<section class="admin-section">
    <h1>Contact Requests</h1>
    <div class="admin-table-wrapper">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Subject</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($requests)): foreach ($requests as $req): ?>
                        <tr>
                            <td><?= $req['id'] ?></td>
                            <td><?= htmlspecialchars($req['name']) ?></td>
                            <td><?= htmlspecialchars($req['email']) ?></td>
                            <td><?= htmlspecialchars($req['subject']) ?></td>
                            <td><?= ucfirst($req['status']) ?></td>
                            <td><?= $req['created_at'] ?></td>
                            <td>
                                <a href="/admin/contact-requests/view/<?= $req['id'] ?>" class="btn btn-sm">View</a>
                                <?php if ($req['status'] !== 'archived'): ?>
                                    <form action="/admin/contact-requests/archive/<?= $req['id'] ?>" method="POST" style="display:inline;">
                                        <button class="btn btn-sm" onclick="return confirm('Archive this request?')">Archive</button>
                                    </form>
                                <?php endif; ?>
                                <form action="/admin/contact-requests/delete/<?= $req['id'] ?>" method="POST" style="display:inline;">
                                    <button class="btn btn-sm btn-danger" onclick="return confirm('Delete this request?')">Delete</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach;
                else: ?>
                    <tr>
                        <td colspan="7" style="text-align:center; color:var(--text-secondary);">No contact requests found.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</section>
<style>
    .admin-section {
        padding: 40px 0;
    }

    .admin-table-wrapper {
        overflow-x: auto;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--dark-card);
    }

    .admin-table th,
    .admin-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--dark-surface);
        color: var(--primary-purple);
        font-weight: 600;
    }

    .btn-sm {
        padding: 4px 12px;
        font-size: 0.95em;
    }

    .btn-danger {
        background: #e53935;
        color: #fff;
    }

    .btn-danger:hover {
        background: #b71c1c;
    }
</style>