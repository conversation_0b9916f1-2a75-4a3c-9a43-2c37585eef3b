<div class="admin-container">
    <div class="admin-header">
        <h1>Product Management</h1>
        <a href="<?= UrlHelper::url('/admin/products/add') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Product
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="admin-filters">
        <div class="filter-row">
            <div class="search-box">
                <input type="text" id="productSearch" placeholder="Search products..." class="form-input">
                <i class="fas fa-search"></i>
            </div>

            <div class="filter-group">
                <select id="categoryFilter" class="form-select">
                    <option value="">All Categories</option>
                    <?php foreach ($categories ?? [] as $category): ?>
                        <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                    <?php endforeach; ?>
                </select>

                <select id="statusFilter" class="form-select">
                    <option value="">All Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>

                <select id="stockFilter" class="form-select">
                    <option value="">All Stock</option>
                    <option value="in_stock">In Stock</option>
                    <option value="low_stock">Low Stock</option>
                    <option value="out_of_stock">Out of Stock</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="bulk-actions">
        <div class="bulk-select">
            <input type="checkbox" id="selectAll">
            <label for="selectAll">Select All</label>
        </div>

        <div class="bulk-buttons">
            <button class="btn btn-secondary" id="bulkActivate" disabled>
                <i class="fas fa-check"></i> Activate
            </button>
            <button class="btn btn-secondary" id="bulkDeactivate" disabled>
                <i class="fas fa-times"></i> Deactivate
            </button>
            <button class="btn btn-danger" id="bulkDelete" disabled>
                <i class="fas fa-trash"></i> Delete
            </button>
        </div>
    </div>

    <!-- Products Table -->
    <div class="admin-table-container">
        <table class="admin-table">
            <thead>
                <tr>
                    <th width="50">ID</th>
                    <th width="80">Image</th>
                    <th>Name</th>
                    <th>Category</th>
                    <th>Price</th>
                    <th>Stock</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th width="150">Actions</th>
                </tr>
            </thead>
            <tbody id="productsTableBody">
                <?php if (!empty($products)): ?>
                    <?php foreach ($products as $product): ?>
                        <tr data-product-id="<?= $product['id'] ?>">
                            <td><?= $product['id'] ?></td>
                            <td>
                                <div class="product-image">
                                    <?php if (!empty($product['primary_image'])): ?>
                                        <img src="<?= UrlHelper::url('/uploads/products/' . $product['primary_image']) ?>"
                                            alt="<?= htmlspecialchars($product['name']) ?>">
                                    <?php else: ?>
                                        <div class="no-image">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="product-info">
                                    <strong><?= htmlspecialchars($product['name']) ?></strong>
                                    <small><?= htmlspecialchars(substr($product['description'], 0, 50)) ?>...</small>
                                </div>
                            </td>
                            <td><?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?></td>
                            <td>
                                <div class="price-info">
                                    <?php if ($product['sale_price']): ?>
                                        <span class="sale-price">$<?= number_format($product['sale_price'], 2) ?></span>
                                        <span class="original-price">$<?= number_format($product['price'], 2) ?></span>
                                    <?php else: ?>
                                        <span class="price">$<?= number_format($product['price'], 2) ?></span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="stock-badge <?= $product['stock_quantity'] > 10 ? 'in-stock' : ($product['stock_quantity'] > 0 ? 'low-stock' : 'out-of-stock') ?>">
                                    <?= $product['stock_quantity'] ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge <?= $product['is_active'] ? 'active' : 'inactive' ?>">
                                    <?= $product['is_active'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td><?= date('M j, Y', strtotime($product['created_at'])) ?></td>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?= UrlHelper::url('/admin/products/edit/' . $product['id']) ?>"
                                        class="btn btn-sm btn-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-secondary toggle-status"
                                        data-id="<?= $product['id'] ?>"
                                        data-status="<?= $product['is_active'] ?>"
                                        title="<?= $product['is_active'] ? 'Deactivate' : 'Activate' ?>">
                                        <i class="fas fa-<?= $product['is_active'] ? 'times' : 'check' ?>"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-product"
                                        data-id="<?= $product['id'] ?>"
                                        title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="9" class="no-data">
                            <div class="empty-state">
                                <i class="fas fa-box-open"></i>
                                <h3>No Products Found</h3>
                                <p>Get started by adding your first product.</p>
                                <a href="<?= UrlHelper::url('/admin/products/add') ?>" class="btn btn-primary">
                                    Add Product
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if (isset($pagination) && $pagination['totalPages'] > 1): ?>
        <div class="pagination">
            <?php if ($pagination['currentPage'] > 1): ?>
                <a href="?page=<?= $pagination['currentPage'] - 1 ?>" class="page-link">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            <?php endif; ?>

            <?php for ($i = max(1, $pagination['currentPage'] - 2); $i <= min($pagination['totalPages'], $pagination['currentPage'] + 2); $i++): ?>
                <a href="?page=<?= $i ?>" class="page-link <?= $i == $pagination['currentPage'] ? 'active' : '' ?>">
                    <?= $i ?>
                </a>
            <?php endfor; ?>

            <?php if ($pagination['currentPage'] < $pagination['totalPages']): ?>
                <a href="?page=<?= $pagination['currentPage'] + 1 ?>" class="page-link">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<style>
    .admin-container {
        padding: 2rem;
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .admin-header h1 {
        color: var(--text-primary);
        margin: 0;
    }

    .admin-filters {
        background: var(--card-bg);
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .search-box {
        position: relative;
        flex: 1;
        min-width: 300px;
    }

    .search-box input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
    }

    .search-box i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }

    .filter-group {
        display: flex;
        gap: 0.5rem;
    }

    .form-select {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
        min-width: 120px;
    }

    .bulk-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: var(--card-bg);
        border-radius: 8px;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .bulk-select {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .bulk-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .admin-table-container {
        background: var(--card-bg);
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
    }

    .admin-table th,
    .admin-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--bg-secondary);
        font-weight: 600;
        color: var(--text-primary);
    }

    .product-image {
        width: 50px;
        height: 50px;
        border-radius: 6px;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        width: 100%;
        height: 100%;
        background: var(--bg-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-secondary);
    }

    .product-info strong {
        display: block;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .product-info small {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .price-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .sale-price {
        color: #e74c3c;
        font-weight: 600;
    }

    .original-price {
        color: var(--text-secondary);
        text-decoration: line-through;
        font-size: 0.875rem;
    }

    .stock-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .stock-badge.in-stock {
        background: #d4edda;
        color: #155724;
    }

    .stock-badge.low-stock {
        background: #fff3cd;
        color: #856404;
    }

    .stock-badge.out-of-stock {
        background: #f8d7da;
        color: #721c24;
    }

    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .action-buttons {
        display: flex;
        gap: 0.25rem;
    }

    .btn-sm {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-state h3 {
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .empty-state p {
        margin-bottom: 1.5rem;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 2rem;
    }

    .page-link {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        color: var(--text-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .page-link:hover {
        background: var(--bg-secondary);
    }

    .page-link.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    @media (max-width: 768px) {
        .admin-container {
            padding: 1rem;
        }

        .admin-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .filter-row {
            flex-direction: column;
            align-items: stretch;
        }

        .search-box {
            min-width: auto;
        }

        .filter-group {
            flex-wrap: wrap;
        }

        .bulk-actions {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .bulk-buttons {
            justify-content: center;
        }

        .admin-table {
            font-size: 0.875rem;
        }

        .admin-table th,
        .admin-table td {
            padding: 0.5rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('productSearch');
        const categoryFilter = document.getElementById('categoryFilter');
        const statusFilter = document.getElementById('statusFilter');
        const stockFilter = document.getElementById('stockFilter');

        function filterProducts() {
            const searchTerm = searchInput.value.toLowerCase();
            const category = categoryFilter.value;
            const status = statusFilter.value;
            const stock = stockFilter.value;

            const rows = document.querySelectorAll('#productsTableBody tr');

            rows.forEach(row => {
                const name = row.querySelector('.product-info strong').textContent.toLowerCase();
                const categoryCell = row.querySelector('td:nth-child(4)').textContent;
                const statusCell = row.querySelector('.status-badge').textContent;
                const stockCell = row.querySelector('.stock-badge');

                let show = true;

                // Search filter
                if (searchTerm && !name.includes(searchTerm)) {
                    show = false;
                }

                // Category filter
                if (category && categoryCell !== category) {
                    show = false;
                }

                // Status filter
                if (status) {
                    const isActive = statusCell === 'Active';
                    if ((status === '1' && !isActive) || (status === '0' && isActive)) {
                        show = false;
                    }
                }

                // Stock filter
                if (stock) {
                    const stockClass = stockCell.className;
                    if (stock === 'in_stock' && !stockClass.includes('in-stock')) {
                        show = false;
                    } else if (stock === 'low_stock' && !stockClass.includes('low-stock')) {
                        show = false;
                    } else if (stock === 'out_of_stock' && !stockClass.includes('out-of-stock')) {
                        show = false;
                    }
                }

                row.style.display = show ? '' : 'none';
            });
        }

        searchInput.addEventListener('input', filterProducts);
        categoryFilter.addEventListener('change', filterProducts);
        statusFilter.addEventListener('change', filterProducts);
        stockFilter.addEventListener('change', filterProducts);

        // Bulk actions
        const selectAll = document.getElementById('selectAll');
        const bulkButtons = document.querySelectorAll('.bulk-buttons button');

        function updateBulkButtons() {
            const checkedBoxes = document.querySelectorAll('#productsTableBody input[type="checkbox"]:checked');
            const hasSelection = checkedBoxes.length > 0;

            bulkButtons.forEach(btn => {
                btn.disabled = !hasSelection;
            });
        }

        // Toggle status
        document.querySelectorAll('.toggle-status').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.id;
                const currentStatus = this.dataset.status === '1';
                const newStatus = !currentStatus;

                if (confirm(`Are you sure you want to ${newStatus ? 'activate' : 'deactivate'} this product?`)) {
                    fetch(`/admin/products/${productId}/toggle-status`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                status: newStatus
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('Failed to update product status');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred');
                        });
                }
            });
        });

        // Delete product
        document.querySelectorAll('.delete-product').forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.id;

                if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
                    fetch(`/admin/products/${productId}/delete`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('Failed to delete product');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred');
                        });
                }
            });
        });
    });
</script>