<?php

require_once __DIR__ . '/BaseController.php';

class ErrorController extends BaseController
{

    public function __construct($pdo)
    {
        parent::__construct($pdo);
    }

    public function notFound()
    {
        http_response_code(404);
        $this->renderErrorPage('errors/404');
    }

    public function serverError()
    {
        http_response_code(500);
        $this->renderErrorPage('errors/500');
    }

    public function unauthorized()
    {
        http_response_code(401);
        $this->renderErrorPage('errors/401');
    }

    public function forbidden()
    {
        http_response_code(403);
        $this->renderErrorPage('errors/403');
    }

    private function renderErrorPage($view)
    {
        try {
            // Include the error page directly without layout
            $viewFile = APP_ROOT . '/app/views/' . $view . '.php';
            if (file_exists($viewFile)) {
                require_once $viewFile;
            } else {
                throw new Exception("Error page not found: $viewFile");
            }
        } catch (Exception $e) {
            // Log the error
            error_log("Error page rendering error: " . $e->getMessage());

            // Show a simple error page
            http_response_code(500);
            echo "<h1>Internal Server Error</h1>";
            echo "<p>Sorry, something went wrong. Please try again later.</p>";
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            exit;
        }
    }
}
