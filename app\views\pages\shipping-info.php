<div class="shipping-info">
    <div class="container">
        <!-- <PERSON> Header -->
        <div class="page-header">
            <h1>Shipping Information</h1>
            <p>Everything you need to know about our shipping options and delivery process</p>
        </div>

        <!-- Shipping Options -->
        <div class="shipping-options">
            <h2>Shipping Options</h2>
            <div class="options-grid">
                <div class="option-card">
                    <div class="option-header">
                        <i class="fas fa-truck"></i>
                        <h3>Standard Shipping</h3>
                        <div class="price">$5.99</div>
                    </div>
                    <div class="option-details">
                        <p>3-5 business days</p>
                        <ul>
                            <li>Free on orders over $50</li>
                            <li>Tracking included</li>
                            <li>Signature not required</li>
                        </ul>
                    </div>
                </div>

                <div class="option-card featured">
                    <div class="option-header">
                        <i class="fas fa-rocket"></i>
                        <h3>Express Shipping</h3>
                        <div class="price">$12.99</div>
                    </div>
                    <div class="option-details">
                        <p>1-2 business days</p>
                        <ul>
                            <li>Priority handling</li>
                            <li>Real-time tracking</li>
                            <li>Delivery confirmation</li>
                        </ul>
                    </div>
                </div>

                <div class="option-card">
                    <div class="option-header">
                        <i class="fas fa-globe"></i>
                        <h3>International</h3>
                        <div class="price">$24.99</div>
                    </div>
                    <div class="option-details">
                        <p>7-14 business days</p>
                        <ul>
                            <li>Available to most countries</li>
                            <li>Customs documentation</li>
                            <li>Tracking included</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Information -->
        <div class="delivery-info">
            <h2>Delivery Information</h2>
            <div class="info-grid">
                <div class="info-card">
                    <i class="fas fa-clock"></i>
                    <h3>Processing Time</h3>
                    <p>Orders are typically processed and shipped within 24 hours of placement during business days.</p>
                </div>

                <div class="info-card">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>Delivery Areas</h3>
                    <p>We ship to all 50 US states, Canada, and most international destinations. Some restrictions may apply.</p>
                </div>

                <div class="info-card">
                    <i class="fas fa-calendar"></i>
                    <h3>Business Days</h3>
                    <p>Shipping times are calculated using business days (Monday-Friday, excluding holidays).</p>
                </div>

                <div class="info-card">
                    <i class="fas fa-box"></i>
                    <h3>Package Protection</h3>
                    <p>All packages are carefully packaged to ensure safe delivery. Insurance is included on all orders.</p>
                </div>
            </div>
        </div>

        <!-- Tracking Information -->
        <div class="tracking-info">
            <h2>Order Tracking</h2>
            <div class="tracking-content">
                <div class="tracking-steps">
                    <div class="step">
                        <div class="step-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="step-content">
                            <h3>Order Placed</h3>
                            <p>Your order is received and confirmed</p>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="step-content">
                            <h3>Processing</h3>
                            <p>Your order is being prepared for shipment</p>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <div class="step-content">
                            <h3>Shipped</h3>
                            <p>Your package is on its way to you</p>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="step-content">
                            <h3>Delivered</h3>
                            <p>Your package has been delivered</p>
                        </div>
                    </div>
                </div>

                <div class="tracking-tips">
                    <h3>Tracking Tips</h3>
                    <ul>
                        <li>You'll receive a tracking number via email once your order ships</li>
                        <li>Track your package in real-time through your account</li>
                        <li>Delivery notifications are sent via email and SMS</li>
                        <li>Contact us if you have any delivery concerns</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Shipping Policies -->
        <div class="shipping-policies">
            <h2>Shipping Policies</h2>
            <div class="policies-grid">
                <div class="policy-card">
                    <h3>Free Shipping</h3>
                    <p>Free standard shipping on all orders over $50. This applies to domestic orders only.</p>
                </div>

                <div class="policy-card">
                    <h3>Holiday Shipping</h3>
                    <p>During peak holiday seasons, processing times may be extended. We'll notify you of any delays.</p>
                </div>

                <div class="policy-card">
                    <h3>Address Accuracy</h3>
                    <p>Please ensure your shipping address is correct. We're not responsible for packages sent to incorrect addresses.</p>
                </div>

                <div class="policy-card">
                    <h3>Delivery Attempts</h3>
                    <p>Carriers will attempt delivery 3 times. After that, packages are returned to our facility.</p>
                </div>

                <div class="policy-card">
                    <h3>Signature Required</h3>
                    <p>Orders over $200 require signature confirmation for security purposes.</p>
                </div>

                <div class="policy-card">
                    <h3>Weather Delays</h3>
                    <p>Severe weather conditions may cause delivery delays. We'll keep you updated on any issues.</p>
                </div>
            </div>
        </div>

        <!-- International Shipping -->
        <div class="international-shipping">
            <h2>International Shipping</h2>
            <div class="international-content">
                <div class="international-info">
                    <h3>Available Countries</h3>
                    <p>We currently ship to the following regions:</p>
                    <ul>
                        <li>United States (all 50 states)</li>
                        <li>Canada</li>
                        <li>United Kingdom</li>
                        <li>European Union</li>
                        <li>Australia</li>
                        <li>New Zealand</li>
                        <li>Japan</li>
                        <li>South Korea</li>
                        <li>And many more...</li>
                    </ul>
                </div>

                <div class="international-details">
                    <h3>Important Information</h3>
                    <div class="detail-item">
                        <h4>Customs & Duties</h4>
                        <p>International customers are responsible for any customs duties, taxes, or fees imposed by their country.</p>
                    </div>

                    <div class="detail-item">
                        <h4>Delivery Times</h4>
                        <p>International delivery typically takes 7-14 business days, depending on the destination and customs processing.</p>
                    </div>

                    <div class="detail-item">
                        <h4>Tracking</h4>
                        <p>All international packages include tracking information. You'll receive updates via email.</p>
                    </div>

                    <div class="detail-item">
                        <h4>Restrictions</h4>
                        <p>Some products may be restricted in certain countries. We'll notify you if your order cannot be shipped.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="contact-section">
            <div class="contact-card">
                <h2>Questions About Shipping?</h2>
                <p>Our customer service team is here to help with any shipping-related questions.</p>
                <div class="contact-options">
                    <a href="<?= UrlHelper::url('/contact') ?>" class="btn btn-primary">
                        <i class="fas fa-envelope"></i>
                        Contact Us
                    </a>
                    <a href="<?= UrlHelper::url('/help-center') ?>" class="btn btn-outline">
                        <i class="fas fa-question-circle"></i>
                        Help Center
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .shipping-info {
        padding: var(--spacing-xl) 0;
        background-color: var(--dark-surface);
        min-height: 100vh;
    }

    .page-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .page-header h1 {
        font-size: var(--font-size-4xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-header p {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Section Headers */
    .shipping-info h2 {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xl);
        text-align: center;
    }

    /* Shipping Options */
    .shipping-options {
        margin-bottom: var(--spacing-3xl);
    }

    .options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    .option-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        transition: all var(--transition-fast);
        position: relative;
    }

    .option-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .option-card.featured {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 1px var(--primary-purple);
    }

    .option-card.featured::before {
        content: 'Most Popular';
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-full);
        font-size: var(--font-size-sm);
        font-weight: 600;
    }

    .option-header {
        text-align: center;
        margin-bottom: var(--spacing-lg);
    }

    .option-header i {
        font-size: var(--font-size-4xl);
        color: var(--primary-purple);
        margin-bottom: var(--spacing-md);
    }

    .option-header h3 {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .price {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--primary-purple);
    }

    .option-details p {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-md);
        text-align: center;
    }

    .option-details ul {
        list-style: none;
        padding: 0;
    }

    .option-details li {
        color: var(--text-secondary);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        padding-left: var(--spacing-lg);
    }

    .option-details li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: var(--success-color);
        font-weight: bold;
    }

    .option-details li:last-child {
        border-bottom: none;
    }

    /* Delivery Information */
    .delivery-info {
        margin-bottom: var(--spacing-3xl);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .info-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
        transition: all var(--transition-fast);
    }

    .info-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .info-card i {
        font-size: var(--font-size-3xl);
        color: var(--primary-purple);
        margin-bottom: var(--spacing-md);
    }

    .info-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .info-card p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Tracking Information */
    .tracking-info {
        margin-bottom: var(--spacing-3xl);
    }

    .tracking-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--spacing-2xl);
    }

    .tracking-steps {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .step {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        transition: all var(--transition-fast);
    }

    .step:hover {
        transform: translateX(4px);
        box-shadow: var(--shadow-lg);
    }

    .step-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .step-icon i {
        font-size: var(--font-size-xl);
        color: white;
    }

    .step-content h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .step-content p {
        color: var(--text-secondary);
        margin: 0;
    }

    .tracking-tips {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .tracking-tips h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .tracking-tips ul {
        list-style: none;
        padding: 0;
    }

    .tracking-tips li {
        color: var(--text-secondary);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        padding-left: var(--spacing-lg);
    }

    .tracking-tips li::before {
        content: '•';
        position: absolute;
        left: 0;
        color: var(--primary-purple);
        font-weight: bold;
    }

    .tracking-tips li:last-child {
        border-bottom: none;
    }

    /* Shipping Policies */
    .shipping-policies {
        margin-bottom: var(--spacing-3xl);
    }

    .policies-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    .policy-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        transition: all var(--transition-fast);
    }

    .policy-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .policy-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .policy-card p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* International Shipping */
    .international-shipping {
        margin-bottom: var(--spacing-3xl);
    }

    .international-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
    }

    .international-info h3,
    .international-details h3 {
        font-size: var(--font-size-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .international-info p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);
    }

    .international-info ul {
        list-style: none;
        padding: 0;
    }

    .international-info li {
        color: var(--text-secondary);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        padding-left: var(--spacing-lg);
    }

    .international-info li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: var(--success-color);
        font-weight: bold;
    }

    .international-info li:last-child {
        border-bottom: none;
    }

    .detail-item {
        margin-bottom: var(--spacing-lg);
    }

    .detail-item h4 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .detail-item p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Contact Section */
    .contact-section {
        text-align: center;
    }

    .contact-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        max-width: 600px;
        margin: 0 auto;
    }

    .contact-card h2 {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .contact-card p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
    }

    .contact-options {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background-color: var(--dark-surface);
        border-color: var(--primary-purple);
        color: var(--primary-purple);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {

        .tracking-content,
        .international-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }
    }

    @media (max-width: 768px) {
        .shipping-info {
            padding: var(--spacing-lg) 0;
        }

        .page-header h1 {
            font-size: var(--font-size-3xl);
        }

        .options-grid,
        .info-grid,
        .policies-grid {
            grid-template-columns: 1fr;
        }

        .contact-options {
            flex-direction: column;
        }

        .step {
            flex-direction: column;
            text-align: center;
        }
    }

    /* Light Mode Support */
    .light-mode .shipping-info {
        background-color: var(--light-surface);
    }

    .light-mode .option-card,
    .light-mode .info-card,
    .light-mode .step,
    .light-mode .tracking-tips,
    .light-mode .policy-card,
    .light-mode .contact-card {
        background-color: var(--light-card);
        border-color: var(--light-border);
    }

    .light-mode .btn-outline {
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .btn-outline:hover {
        background-color: var(--light-surface);
    }
</style>