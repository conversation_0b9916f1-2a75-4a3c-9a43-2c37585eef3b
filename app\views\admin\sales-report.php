<section class="admin-section">
    <h1>Sales Report</h1>
    <form class="admin-form" method="GET" style="margin-bottom:24px;">
        <label>Date Range:
            <input type="date" name="start_date"> to <input type="date" name="end_date">
        </label>
        <button type="submit" class="btn btn-primary">Filter</button>
    </form>
    <div class="admin-table-wrapper">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Order #</th>
                    <th>Date</th>
                    <th>Customer</th>
                    <th>Total</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($sales)): foreach ($sales as $sale): ?>
                <tr>
                    <td><?= $sale['order_number'] ?></td>
                    <td><?= $sale['created_at'] ?></td>
                    <td><?= htmlspecialchars($sale['customer_name']) ?></td>
                    <td>$<?= number_format($sale['total_amount'],2) ?></td>
                    <td><?= ucfirst($sale['status']) ?></td>
                </tr>
                <?php endforeach; else: ?>
                <tr><td colspan="5" style="text-align:center; color:var(--text-secondary);">No sales found.</td></tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    <div class="admin-summary">
        <strong>Total Revenue:</strong> $<?= number_format($totalRevenue ?? 0,2) ?>
    </div>
</section>
<style>
.admin-section { padding: 40px 0; }
.admin-form { display: flex; gap: 18px; align-items: center; background: var(--dark-card); padding: 16px 24px; border-radius: 12px; margin-bottom: 24px; }
.admin-table-wrapper { overflow-x: auto; }
.admin-table { width: 100%; border-collapse: collapse; background: var(--dark-card); }
.admin-table th, .admin-table td { padding: 12px 16px; border-bottom: 1px solid var(--border-color); }
.admin-table th { background: var(--dark-surface); color: var(--primary-purple); font-weight: 600; }
.admin-summary { margin-top: 24px; color: var(--primary-purple); font-size: 1.2em; }
</style> 