<?php

class UrlHelper
{
    /**
     * Generate a URL for the application
     */
    public static function url($path = '')
    {
        $scriptName = $_SERVER['SCRIPT_NAME'];
        $basePath = dirname($scriptName);
        
        // Ensure base path doesn't end with /
        $basePath = rtrim($basePath, '/');
        
        // Ensure path starts with /
        $path = '/' . ltrim($path, '/');
        
        return $basePath . $path;
    }
    
    /**
     * Generate an asset URL
     */
    public static function asset($path)
    {
        return self::url('/assets/' . ltrim($path, '/'));
    }
    
    /**
     * Generate a CSS asset URL
     */
    public static function css($filename)
    {
        return self::asset('css/' . $filename);
    }
    
    /**
     * Generate a JS asset URL
     */
    public static function js($filename)
    {
        return self::asset('js/' . $filename);
    }
    
    /**
     * Generate an image asset URL
     */
    public static function image($filename)
    {
        return self::asset('images/' . $filename);
    }
    
    /**
     * Get the current URL
     */
    public static function current()
    {
        return $_SERVER['REQUEST_URI'];
    }
    
    /**
     * Get the base URL
     */
    public static function base()
    {
        $scriptName = $_SERVER['SCRIPT_NAME'];
        $basePath = dirname($scriptName);
        return rtrim($basePath, '/');
    }
} 