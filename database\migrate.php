<?php

/**
 * Database Migration Runner
 * Run this script to apply database migrations
 */

require_once dirname(__DIR__) . '/config/app.php';

try {
    // Database connection
    $dbConfig = require_once dirname(__DIR__) . '/config/database.php';
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);

    echo "Connected to database successfully.\n";

    // Create migrations table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS migrations (
            id INT PRIMARY KEY AUTO_INCREMENT,
            migration VARCHAR(255) NOT NULL UNIQUE,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");

    // Get list of executed migrations
    $stmt = $pdo->query("SELECT migration FROM migrations");
    $executedMigrations = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Get list of migration files
    $migrationFiles = glob(__DIR__ . '/migrations/*.sql');
    sort($migrationFiles);

    $newMigrations = 0;

    foreach ($migrationFiles as $file) {
        $migrationName = basename($file, '.sql');

        if (!in_array($migrationName, $executedMigrations)) {
            echo "Executing migration: $migrationName\n";

            $sql = file_get_contents($file);

            // Split by semicolon and execute each statement
            $statements = array_filter(array_map('trim', explode(';', $sql)));

            try {
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^--/', $statement)) {
                        $pdo->exec($statement);
                    }
                }

                // Record migration as executed
                $stmt = $pdo->prepare("INSERT INTO migrations (migration) VALUES (?)");
                $stmt->execute([$migrationName]);

                echo "✓ Migration $migrationName executed successfully\n";
                $newMigrations++;
            } catch (Exception $e) {
                echo "✗ Migration $migrationName failed: " . $e->getMessage() . "\n";
                break;
            }
        } else {
            echo "- Migration $migrationName already executed\n";
        }
    }

    if ($newMigrations > 0) {
        echo "\n✓ $newMigrations new migrations executed successfully!\n";
    } else {
        echo "\n- No new migrations to execute.\n";
    }
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nMigration process completed.\n";
