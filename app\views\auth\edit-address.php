<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Address
                    </h4>
                </div>
                <div class="card-body p-4">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <form action="/asma/public/account/addresses/edit/<?php echo $address['id']; ?>" method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text"
                                    class="form-control"
                                    id="first_name"
                                    name="first_name"
                                    value="<?php echo htmlspecialchars($address['first_name']); ?>"
                                    required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text"
                                    class="form-control"
                                    id="last_name"
                                    name="last_name"
                                    value="<?php echo htmlspecialchars($address['last_name']); ?>"
                                    required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address_type" class="form-label">Address Type *</label>
                            <select class="form-select" id="address_type" name="address_type" required>
                                <option value="">Select address type</option>
                                <option value="Home" <?php echo $address['address_type'] === 'Home' ? 'selected' : ''; ?>>Home</option>
                                <option value="Work" <?php echo $address['address_type'] === 'Work' ? 'selected' : ''; ?>>Work</option>
                                <option value="Other" <?php echo $address['address_type'] === 'Other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="address_line1" class="form-label">Address Line 1 *</label>
                            <input type="text"
                                class="form-control"
                                id="address_line1"
                                name="address_line1"
                                value="<?php echo htmlspecialchars($address['address_line1']); ?>"
                                required>
                        </div>

                        <div class="mb-3">
                            <label for="address_line2" class="form-label">Address Line 2</label>
                            <input type="text"
                                class="form-control"
                                id="address_line2"
                                name="address_line2"
                                value="<?php echo htmlspecialchars($address['address_line2']); ?>">
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">City *</label>
                                <input type="text"
                                    class="form-control"
                                    id="city"
                                    name="city"
                                    value="<?php echo htmlspecialchars($address['city']); ?>"
                                    required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="state" class="form-label">State/Province *</label>
                                <input type="text"
                                    class="form-control"
                                    id="state"
                                    name="state"
                                    value="<?php echo htmlspecialchars($address['state']); ?>"
                                    required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="postal_code" class="form-label">Postal Code *</label>
                                <input type="text"
                                    class="form-control"
                                    id="postal_code"
                                    name="postal_code"
                                    value="<?php echo htmlspecialchars($address['postal_code']); ?>"
                                    required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Country *</label>
                                <input type="text"
                                    class="form-control"
                                    id="country"
                                    name="country"
                                    value="<?php echo htmlspecialchars($address['country']); ?>"
                                    required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel"
                                class="form-control"
                                id="phone"
                                name="phone"
                                value="<?php echo htmlspecialchars($address['phone']); ?>">
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input"
                                type="checkbox"
                                id="is_default"
                                name="is_default"
                                <?php echo $address['is_default'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_default">
                                Set as default address
                            </label>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Address
                            </button>
                            <a href="/asma/public/account/addresses" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border: none;
        border-radius: 15px;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: none;
    }

    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
    }

    .form-control,
    .form-select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
</style>