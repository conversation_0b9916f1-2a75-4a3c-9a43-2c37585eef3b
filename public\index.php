<?php
session_start();

// Define constants
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', __DIR__);

// Load configuration
require_once APP_ROOT . '/config/app.php';

// Autoloader
spl_autoload_register(function ($class) {
    $paths = [
        APP_ROOT . '/app/controllers/',
        APP_ROOT . '/app/models/',
        APP_ROOT . '/app/helpers/'
    ];

    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Initialize database connection
try {
    $dbConfig = require_once APP_ROOT . '/config/database.php';
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Load routes
$routes = require_once APP_ROOT . '/routes/web.php';

// Get request method and URI
$method = $_SERVER['REQUEST_METHOD'];
$request = $_SERVER['REQUEST_URI'];

// Extract the path from the URL
$parsedUrl = parse_url($request);
$path = $parsedUrl['path'];

// Dynamically detect the base path
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = dirname($scriptName);

// Remove the base path from the request path
if ($basePath !== '/' && strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// Ensure path starts with /
$path = '/' . ltrim($path, '/');

// If path is just /, set it to the root route
if ($path === '/') {
    $path = '/';
}

// Find matching route
$matchedRoute = null;
$params = [];

foreach ($routes as $route => $handler) {
    $routeParts = explode('|', $route);
    $routeMethod = $routeParts[0];
    $routePath = $routeParts[1];

    if ($routeMethod === $method) {
        // Convert route parameters to regex
        $pattern = preg_replace('/\(\[([^\]]+)\]\+\)/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $path, $matches)) {
            $matchedRoute = $handler;
            $params = array_slice($matches, 1);
            break;
        }
    }
}

if ($matchedRoute) {
    // Parse controller and action
    $parts = explode('@', $matchedRoute);
    $controller = $parts[0];
    $action = $parts[1];

    // Check if controller exists
    if (!class_exists($controller)) {
        http_response_code(404);
        require_once APP_ROOT . '/app/views/errors/404.php';
        exit;
    }

    // Create controller instance and call action
    try {
        $controllerInstance = new $controller($pdo);
        if (method_exists($controllerInstance, $action)) {
            call_user_func_array([$controllerInstance, $action], $params);
        } else {
            http_response_code(404);
            require_once APP_ROOT . '/app/views/errors/404.php';
        }
    } catch (Exception $e) {
        http_response_code(500);
        require_once APP_ROOT . '/app/views/errors/500.php';
    }
} else {
    // No route found
    http_response_code(404);
    require_once APP_ROOT . '/app/views/errors/404.php';
}
