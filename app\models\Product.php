<?php

/**
 * Product Model
 * Handles all product-related database operations
 */

class Product
{
    private $db;

    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Get all products with pagination and filters
     */
    public function getAll($filters = [], $limit = 12, $offset = 0)
    {
        $where = [];
        $params = [];

        if (!empty($filters['category_id'])) {
            $where[] = "p.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        if (!empty($filters['search'])) {
            $where[] = "(p.name LIKE :search OR p.description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($filters['min_price'])) {
            $where[] = "p.price >= :min_price";
            $params['min_price'] = $filters['min_price'];
        }

        if (!empty($filters['max_price'])) {
            $where[] = "p.price <= :max_price";
            $params['max_price'] = $filters['max_price'];
        }

        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

        $sql = "SELECT p.*, c.name as category_name, 
                       (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id) as avg_rating,
                       (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id) as review_count
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                {$whereClause}
                ORDER BY p.created_at DESC 
                LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get featured products
     */
    public function getFeatured($limit = 8)
    {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id) as avg_rating,
                       (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id) as review_count
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.featured = 1 AND p.stock_quantity > 0
                ORDER BY p.created_at DESC 
                LIMIT :limit";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get product by ID with full details
     */
    public function findById($id)
    {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id) as avg_rating,
                       (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id) as review_count
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.id = :id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute(['id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get product images
     */
    public function getImages($productId)
    {
        $sql = "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY sort_order ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['product_id' => $productId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get product reviews
     */
    public function getReviews($productId, $limit = 10, $offset = 0)
    {
        $sql = "SELECT r.*, u.name as user_name 
                FROM product_reviews r 
                LEFT JOIN users u ON r.user_id = u.id 
                WHERE r.product_id = :product_id 
                ORDER BY r.created_at DESC 
                LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':product_id', $productId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Add product review
     */
    public function addReview($data)
    {
        $sql = "INSERT INTO product_reviews (product_id, user_id, rating, comment, created_at) 
                VALUES (:product_id, :user_id, :rating, :comment, NOW())";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'product_id' => $data['product_id'],
            'user_id' => $data['user_id'],
            'rating' => $data['rating'],
            'comment' => $data['comment']
        ]);
    }

    /**
     * Get related products
     */
    public function getRelated($productId, $categoryId, $limit = 4)
    {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id) as avg_rating
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.category_id = :category_id AND p.id != :product_id AND p.stock_quantity > 0
                ORDER BY p.created_at DESC 
                LIMIT :limit";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':category_id', $categoryId, PDO::PARAM_INT);
        $stmt->bindValue(':product_id', $productId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Search products
     */
    public function search($query, $limit = 20)
    {
        $sql = "SELECT p.*, c.name as category_name,
                       (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id) as avg_rating
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE (p.name LIKE :query OR p.description LIKE :query OR c.name LIKE :query)
                AND p.stock_quantity > 0
                ORDER BY p.created_at DESC 
                LIMIT :limit";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':query', '%' . $query . '%');
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Count products with filters
     */
    public function count($filters = [])
    {
        $where = [];
        $params = [];

        if (!empty($filters['category_id'])) {
            $where[] = "category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }

        if (!empty($filters['search'])) {
            $where[] = "(name LIKE :search OR description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

        $sql = "SELECT COUNT(*) FROM products {$whereClause}";
        $stmt = $this->db->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        $stmt->execute();
        return $stmt->fetchColumn();
    }

    /**
     * Update stock quantity
     */
    public function updateStock($id, $quantity)
    {
        $sql = "UPDATE products SET stock_quantity = stock_quantity - :quantity WHERE id = :id AND stock_quantity >= :quantity";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'id' => $id,
            'quantity' => $quantity
        ]);
    }
}
