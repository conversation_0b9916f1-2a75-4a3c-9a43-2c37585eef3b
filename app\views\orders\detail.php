<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <!-- Order Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-shopping-bag me-2"></i>Order #<?php echo $order['id']; ?>
                        </h4>
                        <div class="d-flex gap-2">
                            <a href="/asma/public/orders" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>Back to Orders
                            </a>
                            <button class="btn btn-outline-light btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>Print
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1">
                                <strong>Order Date:</strong>
                                <?php echo date('F d, Y \a\t g:i A', strtotime($order['created_at'])); ?>
                            </p>
                            <p class="mb-1">
                                <strong>Status:</strong>
                                <span class="badge bg-<?php echo getStatusColor($order['status']); ?>">
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                            </p>
                            <?php if ($order['tracking_number']): ?>
                                <p class="mb-1">
                                    <strong>Tracking Number:</strong>
                                    <a href="#" class="text-decoration-none"><?php echo htmlspecialchars($order['tracking_number']); ?></a>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <p class="mb-1">
                                <strong>Payment Method:</strong>
                                <?php echo htmlspecialchars($order['payment_method']); ?>
                            </p>
                            <p class="mb-1">
                                <strong>Payment Status:</strong>
                                <span class="badge bg-<?php echo $order['payment_status'] === 'paid' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($order['payment_status']); ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Order Items -->
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-box me-2"></i>Order Items
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($orderItems as $item): ?>
                                <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                    <img src="<?php echo htmlspecialchars($item['image_path']); ?>"
                                        alt="<?php echo htmlspecialchars($item['name']); ?>"
                                        class="rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($item['name']); ?></h6>
                                        <p class="text-muted mb-1">SKU: <?php echo htmlspecialchars($item['sku']); ?></p>
                                        <p class="mb-0">Quantity: <?php echo $item['quantity']; ?></p>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold">$<?php echo number_format($item['price'] * $item['quantity'], 2); ?></div>
                                        <small class="text-muted">$<?php echo number_format($item['price'], 2); ?> each</small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Order Timeline -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>Order Timeline
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <?php foreach ($orderHistory as $history): ?>
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-<?php echo getStatusColor($history['status']); ?>"></div>
                                        <div class="timeline-content">
                                            <h6 class="mb-1"><?php echo ucfirst($history['status']); ?></h6>
                                            <p class="text-muted mb-0">
                                                <?php echo date('F d, Y \a\t g:i A', strtotime($history['created_at'])); ?>
                                            </p>
                                            <?php if ($history['notes']): ?>
                                                <p class="mb-0 mt-1"><?php echo htmlspecialchars($history['notes']); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <!-- Billing & Shipping -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>Addresses
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="text-primary">Billing Address</h6>
                                <p class="mb-1"><?php echo htmlspecialchars($order['billing_first_name'] . ' ' . $order['billing_last_name']); ?></p>
                                <p class="mb-1"><?php echo htmlspecialchars($order['billing_address']); ?></p>
                                <p class="mb-1">
                                    <?php echo htmlspecialchars($order['billing_city'] . ', ' . $order['billing_state'] . ' ' . $order['billing_postal_code']); ?>
                                </p>
                                <p class="mb-0"><?php echo htmlspecialchars($order['billing_country']); ?></p>
                            </div>

                            <hr>

                            <div>
                                <h6 class="text-primary">Shipping Address</h6>
                                <p class="mb-1"><?php echo htmlspecialchars($order['shipping_first_name'] . ' ' . $order['shipping_last_name']); ?></p>
                                <p class="mb-1"><?php echo htmlspecialchars($order['shipping_address']); ?></p>
                                <p class="mb-1">
                                    <?php echo htmlspecialchars($order['shipping_city'] . ', ' . $order['shipping_state'] . ' ' . $order['shipping_postal_code']); ?>
                                </p>
                                <p class="mb-0"><?php echo htmlspecialchars($order['shipping_country']); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>Order Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span>$<?php echo number_format($order['subtotal'], 2); ?></span>
                            </div>

                            <?php if ($order['discount_amount'] > 0): ?>
                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>Discount:</span>
                                    <span>-$<?php echo number_format($order['discount_amount'], 2); ?></span>
                                </div>
                            <?php endif; ?>

                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span>$<?php echo number_format($order['shipping_cost'], 2); ?></span>
                            </div>

                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax:</span>
                                <span>$<?php echo number_format($order['tax_amount'], 2); ?></span>
                            </div>

                            <hr>

                            <div class="d-flex justify-content-between fw-bold fs-5">
                                <span>Total:</span>
                                <span>$<?php echo number_format($order['total_amount'], 2); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <?php if ($order['status'] === 'pending' || $order['status'] === 'processing'): ?>
                        <div class="card shadow-sm mt-4">
                            <div class="card-body">
                                <h6 class="mb-3">Order Actions</h6>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-outline-danger" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                        <i class="fas fa-times me-2"></i>Cancel Order
                                    </button>
                                    <a href="/asma/public/contact" class="btn btn-outline-primary">
                                        <i class="fas fa-question-circle me-2"></i>Need Help?
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function getStatusColor($status)
{
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'processing':
            return 'info';
        case 'shipped':
            return 'primary';
        case 'delivered':
            return 'success';
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}
?>

<script>
    function cancelOrder(orderId) {
        if (confirm('Are you sure you want to cancel this order? This action cannot be undone.')) {
            fetch(`/asma/public/order/cancel/${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to cancel order: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while cancelling the order.');
                });
        }
    }
</script>

<style>
    .card {
        border: none;
        border-radius: 15px;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid #dee2e6;
    }

    .btn {
        border-radius: 8px;
        font-weight: 500;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.5em 0.75em;
    }

    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-left: 10px;
    }

    @media print {

        .btn,
        .card-header {
            display: none !important;
        }
    }
</style>