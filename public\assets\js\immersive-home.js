// Immersive Homepage JavaScript
// Three.js + GSAP + Interactive Elements

class ImmersiveHomepage {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.particles = [];
        this.mouse = { x: 0, y: 0 };
        this.isLoading = true;
        
        this.init();
    }

    init() {
        this.initLoading();
        this.initThreeJS();
        this.initGSAP();
        this.initEventListeners();
        this.animate();
    }

    initLoading() {
        const loadingScreen = document.getElementById('loadingScreen');
        const progressBar = document.querySelector('.loading-progress');
        
        // Simulate loading progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                
                // Hide loading screen
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                    this.isLoading = false;
                    this.startAnimations();
                }, 500);
            }
            progressBar.style.width = progress + '%';
        }, 100);
    }

    initThreeJS() {
        // Scene setup
        this.scene = new THREE.Scene();
        
        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.z = 5;
        
        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({ 
            alpha: true,
            antialias: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x000000, 0);
        
        const canvas = document.getElementById('three-canvas');
        canvas.appendChild(this.renderer.domElement);
        
        // Create floating particles
        this.createParticles();
        
        // Create floating geometric shapes
        this.createGeometricShapes();
        
        // Add ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
        this.scene.add(ambientLight);
        
        // Add directional light
        const directionalLight = new THREE.DirectionalLight(0x7E57C2, 1);
        directionalLight.position.set(5, 5, 5);
        this.scene.add(directionalLight);
    }

    createParticles() {
        const particleCount = 100;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount * 3; i += 3) {
            // Position
            positions[i] = (Math.random() - 0.5) * 20;
            positions[i + 1] = (Math.random() - 0.5) * 20;
            positions[i + 2] = (Math.random() - 0.5) * 20;
            
            // Color
            colors[i] = Math.random() * 0.5 + 0.5; // R
            colors[i + 1] = Math.random() * 0.3 + 0.7; // G
            colors[i + 2] = Math.random() * 0.5 + 0.5; // B
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });
        
        const particles = new THREE.Points(geometry, material);
        this.scene.add(particles);
        this.particles.push(particles);
    }

    createGeometricShapes() {
        // Create floating spheres
        for (let i = 0; i < 5; i++) {
            const geometry = new THREE.SphereGeometry(0.2, 16, 16);
            const material = new THREE.MeshPhongMaterial({
                color: 0x7E57C2,
                transparent: true,
                opacity: 0.3,
                wireframe: true
            });
            
            const sphere = new THREE.Mesh(geometry, material);
            sphere.position.set(
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10
            );
            
            this.scene.add(sphere);
            this.particles.push(sphere);
        }
        
        // Create floating cubes
        for (let i = 0; i < 3; i++) {
            const geometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
            const material = new THREE.MeshPhongMaterial({
                color: 0x2196F3,
                transparent: true,
                opacity: 0.2,
                wireframe: true
            });
            
            const cube = new THREE.Mesh(geometry, material);
            cube.position.set(
                (Math.random() - 0.5) * 8,
                (Math.random() - 0.5) * 8,
                (Math.random() - 0.5) * 8
            );
            
            this.scene.add(cube);
            this.particles.push(cube);
        }
    }

    initGSAP() {
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);
        
        // Hero animations
        gsap.timeline()
            .from('.title-line', {
                y: 100,
                opacity: 0,
                duration: 1,
                stagger: 0.2,
                ease: 'power3.out',
                delay: 0.5
            })
            .from('.hero-subtitle', {
                y: 50,
                opacity: 0,
                duration: 1,
                ease: 'power3.out'
            }, '-=0.5')
            .from('.hero-actions', {
                y: 50,
                opacity: 0,
                duration: 1,
                ease: 'power3.out'
            }, '-=0.5')
            .from('.floating-product', {
                y: 100,
                opacity: 0,
                duration: 1,
                stagger: 0.2,
                ease: 'power3.out'
            }, '-=0.8');
        
        // Section animations
        gsap.utils.toArray('.section-header').forEach(header => {
            gsap.from(header, {
                scrollTrigger: {
                    trigger: header,
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                },
                y: 50,
                opacity: 0,
                duration: 1,
                ease: 'power3.out'
            });
        });
        
        // Product card animations
        gsap.utils.toArray('.showcase-item').forEach((item, index) => {
            gsap.from(item, {
                scrollTrigger: {
                    trigger: item,
                    start: 'top 85%',
                    end: 'bottom 15%',
                    toggleActions: 'play none none reverse'
                },
                y: 100,
                opacity: 0,
                duration: 1,
                delay: index * 0.1,
                ease: 'power3.out'
            });
        });
        
        // Category card animations
        gsap.utils.toArray('.category-card').forEach((card, index) => {
            gsap.from(card, {
                scrollTrigger: {
                    trigger: card,
                    start: 'top 85%',
                    end: 'bottom 15%',
                    toggleActions: 'play none none reverse'
                },
                y: 50,
                opacity: 0,
                duration: 0.8,
                delay: index * 0.1,
                ease: 'power3.out'
            });
        });
        
        // Latest products animations
        gsap.utils.toArray('.latest-item').forEach((item, index) => {
            gsap.from(item, {
                scrollTrigger: {
                    trigger: item,
                    start: 'top 85%',
                    end: 'bottom 15%',
                    toggleActions: 'play none none reverse'
                },
                scale: 0.8,
                opacity: 0,
                duration: 0.6,
                delay: index * 0.05,
                ease: 'power3.out'
            });
        });
        
        // Parallax effect for hero section
        gsap.to('.hero-section', {
            scrollTrigger: {
                trigger: '.hero-section',
                start: 'top top',
                end: 'bottom top',
                scrub: true
            },
            y: -100,
            ease: 'none'
        });
    }

    startAnimations() {
        // Floating products animation
        gsap.to('.floating-product', {
            y: -20,
            duration: 3,
            stagger: 0.5,
            repeat: -1,
            yoyo: true,
            ease: 'power2.inOut'
        });
        
        // CTA button glow effect
        gsap.to('.cta-button .button-glow', {
            x: '100%',
            duration: 2,
            repeat: -1,
            ease: 'power2.inOut'
        });
    }

    initEventListeners() {
        // Menu toggle
        const menuToggle = document.getElementById('menuToggle');
        const fullscreenMenu = document.getElementById('fullscreenMenu');
        
        menuToggle.addEventListener('click', () => {
            menuToggle.classList.toggle('active');
            fullscreenMenu.classList.toggle('active');
            
            if (fullscreenMenu.classList.contains('active')) {
                gsap.from('.menu-link', {
                    y: 50,
                    opacity: 0,
                    duration: 0.8,
                    stagger: 0.1,
                    ease: 'power3.out'
                });
            }
        });
        
        // Close menu when clicking on links
        document.querySelectorAll('.menu-link').forEach(link => {
            link.addEventListener('click', () => {
                menuToggle.classList.remove('active');
                fullscreenMenu.classList.remove('active');
            });
        });
        
        // Mouse move for 3D effect
        document.addEventListener('mousemove', (e) => {
            this.mouse.x = (e.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(e.clientY / window.innerHeight) * 2 + 1;
        });
        
        // Window resize
        window.addEventListener('resize', () => {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.immersive-header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });
        
        // Add to cart functionality
        document.querySelectorAll('.add-to-cart-btn, .quick-add').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const productId = btn.dataset.productId;
                this.addToCart(productId);
            });
        });
        
        // Product card 3D hover effect
        document.querySelectorAll('.product-card-3d').forEach(card => {
            card.addEventListener('mouseenter', () => {
                gsap.to(card, {
                    scale: 1.05,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });
            
            card.addEventListener('mouseleave', () => {
                gsap.to(card, {
                    scale: 1,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });
        });
        
        // Category card hover effects
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                gsap.to(card.querySelector('.category-icon'), {
                    scale: 1.2,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });
            
            card.addEventListener('mouseleave', () => {
                gsap.to(card.querySelector('.category-icon'), {
                    scale: 1,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });
        });
    }

    addToCart(productId) {
        // Create a floating notification
        const notification = document.createElement('div');
        notification.className = 'cart-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-check"></i>
                <span>Added to cart!</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate notification
        gsap.fromTo(notification, 
            {
                y: 100,
                opacity: 0,
                scale: 0.8
            },
            {
                y: 0,
                opacity: 1,
                scale: 1,
                duration: 0.5,
                ease: 'power3.out',
                onComplete: () => {
                    gsap.to(notification, {
                        y: -50,
                        opacity: 0,
                        duration: 0.5,
                        delay: 2,
                        ease: 'power3.in',
                        onComplete: () => {
                            document.body.removeChild(notification);
                        }
                    });
                }
            }
        );
        
        // Here you would typically make an AJAX call to add the product to cart
        console.log('Adding product to cart:', productId);
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (!this.isLoading) {
            // Rotate particles
            this.particles.forEach(particle => {
                if (particle.rotation) {
                    particle.rotation.x += 0.001;
                    particle.rotation.y += 0.002;
                }
            });
            
            // Mouse interaction with camera
            this.camera.position.x += (this.mouse.x * 0.5 - this.camera.position.x) * 0.05;
            this.camera.position.y += (this.mouse.y * 0.5 - this.camera.position.y) * 0.05;
            this.camera.lookAt(0, 0, 0);
        }
        
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ImmersiveHomepage();
});

// Add notification styles
const style = document.createElement('style');
style.textContent = `
    .cart-notification {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        background: var(--accent-purple);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(126, 87, 194, 0.3);
        z-index: 10000;
        font-weight: 600;
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .notification-content i {
        color: #4CAF50;
    }
`;
document.head.appendChild(style); 