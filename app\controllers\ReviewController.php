<?php

class ReviewController extends BaseController
{
    public function __construct($pdo)
    {
        parent::__construct($pdo);
        ReviewHelper::init($pdo);
    }

    public function index()
    {
        $productId = (int)($_GET['product_id'] ?? 0);
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = 10;
        $offset = ($page - 1) * $limit;

        if (!$productId) {
            $this->jsonResponse(['success' => false, 'error' => 'Product ID is required']);
        }

        try {
            // Get reviews
            $reviews = $this->getProductReviews($productId, $limit, $offset);
            
            // Get review statistics
            $stats = $this->getProductReviewStats($productId);
            
            // Get total count for pagination
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM product_reviews WHERE product_id = ? AND is_approved = 1");
            $stmt->execute([$productId]);
            $totalCount = $stmt->fetch()['count'];
            
            $totalPages = ceil($totalCount / $limit);

            $this->jsonResponse([
                'success' => true,
                'reviews' => $reviews,
                'stats' => $stats,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_count' => $totalCount,
                    'has_next' => $page < $totalPages,
                    'has_prev' => $page > 1
                ]
            ]);
        } catch (Exception $e) {
            $this->logError("Error fetching reviews: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to load reviews']);
        }
    }

    public function add()
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'error' => 'Please login to add a review']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request method']);
        }

        // Validate CSRF token
        $csrfToken = $_POST['csrf_token'] ?? '';
        if (!$this->validateCSRFToken($csrfToken)) {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request']);
        }

        $productId = (int)($_POST['product_id'] ?? 0);
        $rating = (int)($_POST['rating'] ?? 0);
        $title = $this->sanitizeInput($_POST['title'] ?? '');
        $comment = $this->sanitizeInput($_POST['comment'] ?? '');

        // Validation
        $errors = [];
        
        if (!$productId) {
            $errors[] = 'Product ID is required';
        }
        
        if ($rating < 1 || $rating > 5) {
            $errors[] = 'Rating must be between 1 and 5';
        }
        
        if (empty($title)) {
            $errors[] = 'Review title is required';
        }
        
        if (empty($comment)) {
            $errors[] = 'Review comment is required';
        }
        
        if (strlen($title) > 255) {
            $errors[] = 'Review title is too long';
        }
        
        if (strlen($comment) > 2000) {
            $errors[] = 'Review comment is too long';
        }

        if (!empty($errors)) {
            $this->jsonResponse(['success' => false, 'errors' => $errors]);
        }

        try {
            // Check if product exists
            $stmt = $this->pdo->prepare("SELECT id, name FROM products WHERE id = ? AND is_active = 1");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();

            if (!$product) {
                $this->jsonResponse(['success' => false, 'error' => 'Product not found']);
            }

            // Check if user can review (has purchased the product)
            $canReview = $this->canUserReview($productId, $this->user['id']);
            if (!$canReview['can_review']) {
                $this->jsonResponse(['success' => false, 'error' => $canReview['reason']]);
            }

            // Add the review
            $result = $this->addReview($productId, $this->user['id'], $rating, $title, $comment);
            $this->jsonResponse($result);

        } catch (Exception $e) {
            $this->logError("Error adding review: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to add review']);
        }
    }

    public function vote()
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'error' => 'Please login to vote']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid request method']);
        }

        $reviewId = (int)($_POST['review_id'] ?? 0);
        $isHelpful = (bool)($_POST['is_helpful'] ?? false);

        if (!$reviewId) {
            $this->jsonResponse(['success' => false, 'error' => 'Review ID is required']);
        }

        try {
            // Check if review exists
            $stmt = $this->pdo->prepare("SELECT id FROM product_reviews WHERE id = ? AND is_approved = 1");
            $stmt->execute([$reviewId]);
            
            if (!$stmt->fetch()) {
                $this->jsonResponse(['success' => false, 'error' => 'Review not found']);
            }

            $result = $this->voteOnReview($reviewId, $this->user['id'], $isHelpful);
            $this->jsonResponse($result);

        } catch (Exception $e) {
            $this->logError("Error voting on review: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to record vote']);
        }
    }

    public function helpful()
    {
        $productId = (int)($_GET['product_id'] ?? 0);
        $limit = (int)($_GET['limit'] ?? 3);

        if (!$productId) {
            $this->jsonResponse(['success' => false, 'error' => 'Product ID is required']);
        }

        try {
            $reviews = $this->getHelpfulReviews($productId, $limit);
            $this->jsonResponse(['success' => true, 'reviews' => $reviews]);
        } catch (Exception $e) {
            $this->logError("Error fetching helpful reviews: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'error' => 'Failed to load helpful reviews']);
        }
    }

    // Helper methods
    private function getProductReviews($productId, $limit, $offset)
    {
        $stmt = $this->pdo->prepare("
            SELECT r.*, u.first_name, u.last_name, u.username,
                   COALESCE(helpful_votes, 0) as helpful_count,
                   COALESCE(total_votes, 0) as total_votes
            FROM product_reviews r
            LEFT JOIN users u ON r.user_id = u.id
            LEFT JOIN (
                SELECT review_id, 
                       SUM(CASE WHEN is_helpful = 1 THEN 1 ELSE 0 END) as helpful_votes,
                       COUNT(*) as total_votes
                FROM review_votes
                GROUP BY review_id
            ) rv ON r.id = rv.review_id
            WHERE r.product_id = ? AND r.is_approved = 1
            ORDER BY r.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$productId, $limit, $offset]);
        return $stmt->fetchAll();
    }

    private function getProductReviewStats($productId)
    {
        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(*) as total_reviews,
                AVG(rating) as average_rating,
                SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
                SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
                SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
                SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
                SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
            FROM product_reviews 
            WHERE product_id = ? AND is_approved = 1
        ");
        $stmt->execute([$productId]);
        $stats = $stmt->fetch();

        if ($stats['total_reviews'] > 0) {
            $stats['rating_distribution'] = [
                5 => round(($stats['five_star'] / $stats['total_reviews']) * 100),
                4 => round(($stats['four_star'] / $stats['total_reviews']) * 100),
                3 => round(($stats['three_star'] / $stats['total_reviews']) * 100),
                2 => round(($stats['two_star'] / $stats['total_reviews']) * 100),
                1 => round(($stats['one_star'] / $stats['total_reviews']) * 100)
            ];
        } else {
            $stats['rating_distribution'] = [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0];
        }

        return $stats;
    }

    private function canUserReview($productId, $userId)
    {
        // Check if user has purchased this product
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE oi.product_id = ? AND o.user_id = ? AND o.status IN ('completed', 'delivered')
        ");
        $stmt->execute([$productId, $userId]);
        $result = $stmt->fetch();

        if ($result['count'] == 0) {
            return ['can_review' => false, 'reason' => 'You must purchase this product to review it'];
        }

        // Check if user already reviewed
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM product_reviews WHERE product_id = ? AND user_id = ?");
        $stmt->execute([$productId, $userId]);
        $result = $stmt->fetch();

        if ($result['count'] > 0) {
            return ['can_review' => false, 'reason' => 'You have already reviewed this product'];
        }

        return ['can_review' => true];
    }

    private function addReview($productId, $userId, $rating, $title, $comment)
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO product_reviews (product_id, user_id, rating, title, comment, is_approved, created_at)
                VALUES (?, ?, ?, ?, ?, 1, NOW())
            ");
            
            if ($stmt->execute([$productId, $userId, $rating, $title, $comment])) {
                // Update product average rating
                $this->updateProductRating($productId);
                
                return ['success' => true, 'message' => 'Review added successfully'];
            } else {
                return ['success' => false, 'error' => 'Failed to add review'];
            }
        } catch (PDOException $e) {
            throw $e;
        }
    }

    private function updateProductRating($productId)
    {
        $stmt = $this->pdo->prepare("
            UPDATE products 
            SET average_rating = (
                SELECT AVG(rating) 
                FROM product_reviews 
                WHERE product_id = ? AND is_approved = 1
            )
            WHERE id = ?
        ");
        $stmt->execute([$productId, $productId]);
    }

    private function voteOnReview($reviewId, $userId, $isHelpful)
    {
        // Check if user already voted
        $stmt = $this->pdo->prepare("SELECT id FROM review_votes WHERE review_id = ? AND user_id = ?");
        $stmt->execute([$reviewId, $userId]);
        
        if ($stmt->fetch()) {
            // Update existing vote
            $stmt = $this->pdo->prepare("
                UPDATE review_votes 
                SET is_helpful = ?, updated_at = NOW()
                WHERE review_id = ? AND user_id = ?
            ");
            $stmt->execute([$isHelpful, $reviewId, $userId]);
        } else {
            // Add new vote
            $stmt = $this->pdo->prepare("
                INSERT INTO review_votes (review_id, user_id, is_helpful, created_at)
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$reviewId, $userId, $isHelpful]);
        }

        return ['success' => true, 'message' => 'Vote recorded successfully'];
    }

    private function getHelpfulReviews($productId, $limit)
    {
        $stmt = $this->pdo->prepare("
            SELECT r.*, u.first_name, u.last_name, u.username,
                   COALESCE(helpful_votes, 0) as helpful_count
            FROM product_reviews r
            LEFT JOIN users u ON r.user_id = u.id
            LEFT JOIN (
                SELECT review_id, COUNT(*) as helpful_votes
                FROM review_votes
                WHERE is_helpful = 1
                GROUP BY review_id
            ) rv ON r.id = rv.review_id
            WHERE r.product_id = ? AND r.is_approved = 1
            ORDER BY helpful_count DESC, r.rating DESC
            LIMIT ?
        ");
        $stmt->execute([$productId, $limit]);
        return $stmt->fetchAll();
    }
}
