<div class="admin-container">
    <div class="admin-header">
        <h1>Analytics & Reports</h1>
        <div class="header-actions">
            <select id="dateRange" class="form-select">
                <option value="7">Last 7 Days</option>
                <option value="30" selected>Last 30 Days</option>
                <option value="90">Last 90 Days</option>
                <option value="365">Last Year</option>
            </select>
            <button class="btn btn-secondary" id="exportReport">
                <i class="fas fa-download"></i> Export Report
            </button>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="metric-content">
                <h3><?= number_format($metrics['total_orders'] ?? 0) ?></h3>
                <p>Total Orders</p>
                <span class="metric-change <?= ($metrics['orders_change'] ?? 0) >= 0 ? 'positive' : 'negative' ?>">
                    <i class="fas fa-<?= ($metrics['orders_change'] ?? 0) >= 0 ? 'arrow-up' : 'arrow-down' ?>"></i>
                    <?= abs($metrics['orders_change'] ?? 0) ?>%
                </span>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="metric-content">
                <h3>$<?= number_format($metrics['total_revenue'] ?? 0, 2) ?></h3>
                <p>Total Revenue</p>
                <span class="metric-change <?= ($metrics['revenue_change'] ?? 0) >= 0 ? 'positive' : 'negative' ?>">
                    <i class="fas fa-<?= ($metrics['revenue_change'] ?? 0) >= 0 ? 'arrow-up' : 'arrow-down' ?>"></i>
                    <?= abs($metrics['revenue_change'] ?? 0) ?>%
                </span>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="metric-content">
                <h3><?= number_format($metrics['total_customers'] ?? 0) ?></h3>
                <p>Total Customers</p>
                <span class="metric-change <?= ($metrics['customers_change'] ?? 0) >= 0 ? 'positive' : 'negative' ?>">
                    <i class="fas fa-<?= ($metrics['customers_change'] ?? 0) >= 0 ? 'arrow-up' : 'arrow-down' ?>"></i>
                    <?= abs($metrics['customers_change'] ?? 0) ?>%
                </span>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-icon">
                <i class="fas fa-box"></i>
            </div>
            <div class="metric-content">
                <h3><?= number_format($metrics['total_products'] ?? 0) ?></h3>
                <p>Total Products</p>
                <span class="metric-change <?= ($metrics['products_change'] ?? 0) >= 0 ? 'positive' : 'negative' ?>">
                    <i class="fas fa-<?= ($metrics['products_change'] ?? 0) >= 0 ? 'arrow-up' : 'arrow-down' ?>"></i>
                    <?= abs($metrics['products_change'] ?? 0) ?>%
                </span>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-grid">
        <!-- Revenue Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <h3>Revenue Trend</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm btn-secondary" data-chart="revenue" data-period="daily">Daily</button>
                    <button class="btn btn-sm btn-primary" data-chart="revenue" data-period="weekly">Weekly</button>
                    <button class="btn btn-sm btn-secondary" data-chart="revenue" data-period="monthly">Monthly</button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- Orders Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <h3>Orders Trend</h3>
                <div class="chart-controls">
                    <button class="btn btn-sm btn-secondary" data-chart="orders" data-period="daily">Daily</button>
                    <button class="btn btn-sm btn-primary" data-chart="orders" data-period="weekly">Weekly</button>
                    <button class="btn btn-sm btn-secondary" data-chart="orders" data-period="monthly">Monthly</button>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="ordersChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Reports -->
    <div class="reports-grid">
        <!-- Top Products -->
        <div class="report-card">
            <div class="report-header">
                <h3>Top Selling Products</h3>
                <a href="<?= UrlHelper::url('/admin/products') ?>" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="report-content">
                <?php if (!empty($top_products)): ?>
                    <?php foreach ($top_products as $product): ?>
                        <div class="report-item">
                            <div class="item-info">
                                <div class="item-image">
                                    <?php if ($product['image']): ?>
                                        <img src="<?= UrlHelper::asset('uploads/products/' . $product['image']) ?>" 
                                             alt="<?= htmlspecialchars($product['name']) ?>">
                                    <?php else: ?>
                                        <div class="no-image">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="item-details">
                                    <strong><?= htmlspecialchars($product['name']) ?></strong>
                                    <small><?= $product['category_name'] ?? 'Uncategorized' ?></small>
                                </div>
                            </div>
                            <div class="item-stats">
                                <span class="sales-count"><?= $product['sales_count'] ?> sold</span>
                                <span class="revenue">$<?= number_format($product['revenue'], 2) ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-report">
                        <i class="fas fa-chart-bar"></i>
                        <p>No sales data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Top Categories -->
        <div class="report-card">
            <div class="report-header">
                <h3>Top Categories</h3>
                <a href="<?= UrlHelper::url('/admin/categories') ?>" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="report-content">
                <?php if (!empty($top_categories)): ?>
                    <?php foreach ($top_categories as $category): ?>
                        <div class="report-item">
                            <div class="item-info">
                                <div class="category-icon">
                                    <i class="fas fa-tag"></i>
                                </div>
                                <div class="item-details">
                                    <strong><?= htmlspecialchars($category['name']) ?></strong>
                                    <small><?= $category['product_count'] ?> products</small>
                                </div>
                            </div>
                            <div class="item-stats">
                                <span class="sales-count"><?= $category['sales_count'] ?> sold</span>
                                <span class="revenue">$<?= number_format($category['revenue'], 2) ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-report">
                        <i class="fas fa-tags"></i>
                        <p>No category data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="report-card">
            <div class="report-header">
                <h3>Recent Orders</h3>
                <a href="<?= UrlHelper::url('/admin/orders') ?>" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="report-content">
                <?php if (!empty($recent_orders)): ?>
                    <?php foreach ($recent_orders as $order): ?>
                        <div class="report-item">
                            <div class="item-info">
                                <div class="order-icon">
                                    <i class="fas fa-shopping-bag"></i>
                                </div>
                                <div class="item-details">
                                    <strong>Order #<?= $order['id'] ?></strong>
                                    <small><?= htmlspecialchars($order['customer_name']) ?></small>
                                </div>
                            </div>
                            <div class="item-stats">
                                <span class="order-status status-<?= $order['status'] ?>"><?= ucfirst($order['status']) ?></span>
                                <span class="revenue">$<?= number_format($order['total_amount'], 2) ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-report">
                        <i class="fas fa-shopping-cart"></i>
                        <p>No recent orders</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Customer Insights -->
        <div class="report-card">
            <div class="report-header">
                <h3>Customer Insights</h3>
            </div>
            <div class="report-content">
                <div class="insight-item">
                    <div class="insight-label">Average Order Value</div>
                    <div class="insight-value">$<?= number_format($metrics['avg_order_value'] ?? 0, 2) ?></div>
                </div>
                <div class="insight-item">
                    <div class="insight-label">Customer Retention Rate</div>
                    <div class="insight-value"><?= number_format($metrics['retention_rate'] ?? 0, 1) ?>%</div>
                </div>
                <div class="insight-item">
                    <div class="insight-label">Conversion Rate</div>
                    <div class="insight-value"><?= number_format($metrics['conversion_rate'] ?? 0, 2) ?>%</div>
                </div>
                <div class="insight-item">
                    <div class="insight-label">Top Customer</div>
                    <div class="insight-value"><?= htmlspecialchars($metrics['top_customer'] ?? 'N/A') ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    padding: 2rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    color: var(--text-primary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.metric-content h3 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.metric-content p {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.metric-change {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.negative {
    color: #dc3545;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chart-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-container {
    padding: 1.5rem;
    height: 300px;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.report-card {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.report-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.report-content {
    padding: 1.5rem;
}

.report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.report-item:last-child {
    border-bottom: none;
}

.item-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.item-image {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
}

.category-icon,
.order-icon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item-details strong {
    display: block;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.item-details small {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.item-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.sales-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.revenue {
    font-weight: 600;
    color: var(--text-primary);
}

.order-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-shipped {
    background: #d1ecf1;
    color: #0c5460;
}

.status-delivered {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.empty-report {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.empty-report i {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.insight-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.insight-item:last-child {
    border-bottom: none;
}

.insight-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.insight-value {
    font-weight: 600;
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .admin-container {
        padding: 1rem;
    }
    
    .admin-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .chart-controls {
        justify-content: center;
    }
    
    .report-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const ordersCtx = document.getElementById('ordersChart').getContext('2d');
    
    // Sample data - replace with actual data from backend
    const revenueData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Revenue',
            data: [12000, 19000, 15000, 25000, 22000, 30000],
            borderColor: '#7E57C2',
            backgroundColor: 'rgba(126, 87, 194, 0.1)',
            tension: 0.4
        }]
    };
    
    const ordersData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Orders',
            data: [65, 59, 80, 81, 56, 55],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
        }]
    };
    
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: revenueData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#666'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#666'
                    }
                }
            }
        }
    });
    
    const ordersChart = new Chart(ordersCtx, {
        type: 'line',
        data: ordersData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#666'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#666'
                    }
                }
            }
        }
    });
    
    // Chart period controls
    document.querySelectorAll('.chart-controls button').forEach(btn => {
        btn.addEventListener('click', function() {
            const chartType = this.dataset.chart;
            const period = this.dataset.period;
            
            // Update active button
            this.parentElement.querySelectorAll('button').forEach(b => {
                b.classList.remove('btn-primary');
                b.classList.add('btn-secondary');
            });
            this.classList.remove('btn-secondary');
            this.classList.add('btn-primary');
            
            // Update chart data based on period
            updateChartData(chartType, period);
        });
    });
    
    function updateChartData(chartType, period) {
        // This would typically make an AJAX call to get new data
        console.log(`Updating ${chartType} chart for ${period} period`);
        
        // For demo purposes, we'll just log the action
        // In a real implementation, you would:
        // 1. Make an AJAX call to /admin/analytics/chart-data
        // 2. Update the chart with new data
        // 3. Re-render the chart
    }
    
    // Date range change
    document.getElementById('dateRange').addEventListener('change', function() {
        const days = this.value;
        console.log(`Loading analytics for last ${days} days`);
        
        // This would typically reload the page with new date range
        // or make AJAX calls to update all metrics and charts
        window.location.href = `/admin/analytics?days=${days}`;
    });
    
    // Export report
    document.getElementById('exportReport').addEventListener('click', function() {
        const dateRange = document.getElementById('dateRange').value;
        window.open(`/admin/analytics/export?days=${dateRange}`, '_blank');
    });
});
</script> 