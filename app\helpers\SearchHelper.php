<?php

/**
 * Search Helper Class
 * Provides search functionality for products
 */
class SearchHelper
{
    private static $pdo;

    /**
     * Initialize search helper with database connection
     */
    public static function init($pdo)
    {
        self::$pdo = $pdo;
    }

    /**
     * Search products with various filters
     */
    public static function searchProducts($query = '', $filters = [])
    {
        $whereConditions = ['p.is_active = 1'];
        $params = [];

        // Text search
        if (!empty($query)) {
            $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
            $searchTerm = "%{$query}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Category filter
        if (!empty($filters['category_id'])) {
            $whereConditions[] = "p.category_id = ?";
            $params[] = $filters['category_id'];
        }

        // Price range filter
        if (!empty($filters['min_price'])) {
            $whereConditions[] = "p.price >= ?";
            $params[] = $filters['min_price'];
        }

        if (!empty($filters['max_price'])) {
            $whereConditions[] = "p.price <= ?";
            $params[] = $filters['max_price'];
        }

        // In stock filter
        if (!empty($filters['in_stock'])) {
            $whereConditions[] = "p.stock_quantity > 0";
        }

        // Featured filter
        if (!empty($filters['featured'])) {
            $whereConditions[] = "p.is_featured = 1";
        }

        // Build the query
        $sql = "
            SELECT p.*, c.name as category_name, pi.image_path as primary_image,
                   AVG(pr.rating) as avg_rating, COUNT(pr.id) as review_count
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
            LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.is_approved = 1
            WHERE " . implode(' AND ', $whereConditions) . "
            GROUP BY p.id
        ";

        // Sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'DESC';
        
        switch ($sortBy) {
            case 'price_low':
                $sql .= " ORDER BY p.price ASC";
                break;
            case 'price_high':
                $sql .= " ORDER BY p.price DESC";
                break;
            case 'name':
                $sql .= " ORDER BY p.name ASC";
                break;
            case 'rating':
                $sql .= " ORDER BY avg_rating DESC, review_count DESC";
                break;
            case 'popularity':
                $sql .= " ORDER BY p.is_featured DESC, review_count DESC";
                break;
            default:
                $sql .= " ORDER BY p.created_at DESC";
        }

        // Pagination
        $page = max(1, (int)($filters['page'] ?? 1));
        $limit = (int)($filters['limit'] ?? ITEMS_PER_PAGE);
        $offset = ($page - 1) * $limit;

        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        try {
            $stmt = self::$pdo->prepare($sql);
            $stmt->execute($params);
            $products = $stmt->fetchAll();

            // Get total count for pagination
            $countSql = "
                SELECT COUNT(DISTINCT p.id) as total
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE " . implode(' AND ', $whereConditions);

            $countParams = array_slice($params, 0, -2); // Remove limit and offset
            $countStmt = self::$pdo->prepare($countSql);
            $countStmt->execute($countParams);
            $totalCount = $countStmt->fetch()['total'];

            return [
                'products' => $products,
                'total' => $totalCount,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalCount / $limit)
            ];

        } catch (PDOException $e) {
            error_log("Search error: " . $e->getMessage());
            return [
                'products' => [],
                'total' => 0,
                'page' => 1,
                'limit' => $limit,
                'total_pages' => 0
            ];
        }
    }

    /**
     * Get search suggestions
     */
    public static function getSearchSuggestions($query, $limit = 10)
    {
        if (strlen($query) < 2) {
            return [];
        }

        $cacheKey = "search_suggestions_" . md5($query);
        
        return CacheHelper::remember($cacheKey, function() use ($query, $limit) {
            $searchTerm = "%{$query}%";
            
            $stmt = self::$pdo->prepare("
                SELECT DISTINCT p.name, p.slug, p.id
                FROM products p 
                WHERE p.is_active = 1 
                AND (p.name LIKE ? OR p.short_description LIKE ?)
                ORDER BY 
                    CASE WHEN p.name LIKE ? THEN 1 ELSE 2 END,
                    p.name ASC
                LIMIT ?
            ");
            
            $stmt->execute([$searchTerm, $searchTerm, "{$query}%", $limit]);
            return $stmt->fetchAll();
        }, 300); // Cache for 5 minutes
    }

    /**
     * Get popular search terms
     */
    public static function getPopularSearchTerms($limit = 10)
    {
        return CacheHelper::remember('popular_search_terms', function() use ($limit) {
            // This would typically come from a search_logs table
            // For now, return some common terms based on product names
            $stmt = self::$pdo->prepare("
                SELECT p.name, COUNT(*) as frequency
                FROM products p 
                WHERE p.is_active = 1 
                GROUP BY p.name
                ORDER BY frequency DESC
                LIMIT ?
            ");
            
            $stmt->execute([$limit]);
            return $stmt->fetchAll();
        }, 3600); // Cache for 1 hour
    }

    /**
     * Get filter options for search
     */
    public static function getFilterOptions()
    {
        return CacheHelper::remember('search_filter_options', function() {
            // Get categories
            $categories = CacheHelper::getCategories();
            
            // Get price range
            $stmt = self::$pdo->prepare("
                SELECT MIN(price) as min_price, MAX(price) as max_price 
                FROM products 
                WHERE is_active = 1
            ");
            $stmt->execute();
            $priceRange = $stmt->fetch();
            
            return [
                'categories' => $categories,
                'price_range' => $priceRange,
                'sort_options' => [
                    'created_at' => 'Newest First',
                    'price_low' => 'Price: Low to High',
                    'price_high' => 'Price: High to Low',
                    'name' => 'Name A-Z',
                    'rating' => 'Highest Rated',
                    'popularity' => 'Most Popular'
                ]
            ];
        }, 1800); // Cache for 30 minutes
    }

    /**
     * Log search query for analytics
     */
    public static function logSearch($query, $results_count = 0, $user_id = null)
    {
        try {
            // This would typically go to a search_logs table
            // For now, just log to file
            $logEntry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'query' => $query,
                'results_count' => $results_count,
                'user_id' => $user_id,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ];

            $logFile = ROOT_PATH . '/logs/search.log';
            file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            error_log("Search logging error: " . $e->getMessage());
        }
    }
}
