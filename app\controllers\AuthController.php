<?php

class AuthController extends BaseController
{

    public function login()
    {
        if ($this->user) {
            // Redirect to home page instead of dashboard for regular users
            $this->redirect('home');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $this->sanitizeInput($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $remember = isset($_POST['remember']);

            if (empty($email) || empty($password)) {
                $error = 'Email and password are required.';
            } else {
                try {
                    $stmt = $this->pdo->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1");
                    $stmt->execute([$email]);
                    $user = $stmt->fetch();

                    if ($user && password_verify($password, $user['password'])) {
                        $_SESSION['user_id'] = $user['id'];

                        if ($remember) {
                            $token = bin2hex(random_bytes(32));
                            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/');

                            // Store remember token in database (you'd need to add this column)
                            // $stmt = $this->pdo->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                            // $stmt->execute([$token, $user['id']]);
                        }

                        // Merge session cart with database cart
                        if (isset($_SESSION['cart'])) {
                            foreach ($_SESSION['cart'] as $productId => $quantity) {
                                $stmt = $this->pdo->prepare("
                                    INSERT INTO cart_items (user_id, product_id, quantity) 
                                    VALUES (?, ?, ?) 
                                    ON DUPLICATE KEY UPDATE quantity = quantity + ?
                                ");
                                $stmt->execute([$user['id'], $productId, $quantity, $quantity]);
                            }
                            unset($_SESSION['cart']);
                        }

                        // Redirect based on user role
                        if ($user['is_admin']) {
                            $this->redirect('admin/dashboard');
                        } else {
                            $this->redirect('home');
                        }
                    } else {
                        $error = 'Invalid email or password.';
                    }
                } catch (PDOException $e) {
                    error_log("Database error in login: " . $e->getMessage());
                    $error = 'Login failed. Please try again.';
                }
            }
        }

        $this->render('auth/login', [
            'error' => $error ?? null,
            'email' => $email ?? ''
        ]);
    }

    public function register()
    {
        if ($this->user) {
            $this->redirect('home');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $this->sanitizeInput($_POST['username'] ?? '');
            $email = $this->sanitizeInput($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            $firstName = $this->sanitizeInput($_POST['first_name'] ?? '');
            $lastName = $this->sanitizeInput($_POST['last_name'] ?? '');

            // Validation
            if (empty($username) || empty($email) || empty($password) || empty($firstName) || empty($lastName)) {
                $error = 'All fields are required.';
            } elseif (!$this->validateEmail($email)) {
                $error = 'Please enter a valid email address.';
            } elseif (strlen($password) < 6) {
                $error = 'Password must be at least 6 characters long.';
            } elseif ($password !== $confirmPassword) {
                $error = 'Passwords do not match.';
            } else {
                try {
                    // Check if username or email already exists
                    $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = ? OR email = ?");
                    $stmt->execute([$username, $email]);
                    $exists = $stmt->fetch()['count'] > 0;

                    if ($exists) {
                        $error = 'Username or email already exists.';
                    } else {
                        // Create user
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $verificationToken = bin2hex(random_bytes(32));

                        $stmt = $this->pdo->prepare("
                            INSERT INTO users (username, email, password, first_name, last_name, verification_token, created_at) 
                            VALUES (?, ?, ?, ?, ?, ?, NOW())
                        ");

                        if ($stmt->execute([$username, $email, $hashedPassword, $firstName, $lastName, $verificationToken])) {
                            $userId = $this->pdo->lastInsertId();

                            // Send welcome/verification email using template
                            $verifyLink = APP_URL . "/verify/" . $verificationToken;
                            $this->sendTemplateEmail($email, 'welcome', [
                                'customer_name' => $firstName . ' ' . $lastName,
                                'first_name' => $firstName,
                                'last_name' => $lastName,
                                'username' => $username,
                                'email' => $email,
                                'verify_link' => $verifyLink,
                                'app_name' => APP_NAME
                            ]);

                            $success = 'Registration successful! Please check your email to verify your account.';
                        } else {
                            $error = 'Registration failed. Please try again.';
                        }
                    }
                } catch (PDOException $e) {
                    error_log("Database error in register: " . $e->getMessage());
                    $error = 'Registration failed. Please try again.';
                }
            }
        }

        $this->render('auth/register', [
            'error' => $error ?? null,
            'success' => $success ?? null,
            'formData' => [
                'username' => $username ?? '',
                'email' => $email ?? '',
                'first_name' => $firstName ?? '',
                'last_name' => $lastName ?? ''
            ]
        ]);
    }

    public function logout()
    {
        session_destroy();
        setcookie('remember_token', '', time() - 3600, '/');
        $this->redirect('home');
    }

    public function verify($token)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT id, email FROM users WHERE verification_token = ? AND email_verified = 0");
            $stmt->execute([$token]);
            $user = $stmt->fetch();

            if ($user) {
                $stmt = $this->pdo->prepare("UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?");
                $stmt->execute([$user['id']]);

                $message = 'Email verified successfully! You can now log in.';
                $type = 'success';
            } else {
                $message = 'Invalid or expired verification token.';
                $type = 'error';
            }
        } catch (PDOException $e) {
            error_log("Database error in verify: " . $e->getMessage());
            $message = 'Verification failed. Please try again.';
            $type = 'error';
        }

        $this->render('auth/verify', [
            'message' => $message,
            'type' => $type
        ]);
    }

    public function forgotPassword()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $this->sanitizeInput($_POST['email'] ?? '');

            if (empty($email)) {
                $error = 'Email is required.';
            } elseif (!$this->validateEmail($email)) {
                $error = 'Please enter a valid email address.';
            } else {
                try {
                    $stmt = $this->pdo->prepare("SELECT id, first_name FROM users WHERE email = ? AND is_active = 1");
                    $stmt->execute([$email]);
                    $user = $stmt->fetch();

                    if ($user) {
                        $resetToken = bin2hex(random_bytes(32));
                        $expires = date('Y-m-d H:i:s', time() + 3600); // Add 1 hour (3600 seconds)
                        $expiresFormatted = date('F j, Y \a\t g:i A', time() + 3600);

                        $stmt = $this->pdo->prepare("UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?");
                        $stmt->execute([$resetToken, $expires, $user['id']]);

                        $resetLink = APP_URL . "/reset-password/" . $resetToken;
                        $templateVars = [
                            'customer_name' => $user['first_name'],
                            'reset_link' => $resetLink,
                            'app_name' => APP_NAME,
                            'expires_at' => $expiresFormatted
                        ];
                        if ($this->sendTemplateEmail($email, 'password_reset', $templateVars)) {
                            $success = 'Password reset instructions have been sent to your email. The link will expire on ' . $expiresFormatted . '.';
                        } else {
                            $error = 'Failed to send reset email. Please try again.';
                        }
                    } else {
                        // Don't reveal if email exists or not for security
                        $success = 'If the email exists, password reset instructions have been sent.';
                    }
                } catch (PDOException $e) {
                    error_log("Database error in forgotPassword: " . $e->getMessage());
                    $error = 'Failed to process request. Please try again.';
                }
            }
        }

        $this->render('auth/forgot-password', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function resetPassword($token)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT id, email FROM users WHERE reset_token = ? AND reset_token_expires > NOW()");
            $stmt->execute([$token]);
            $user = $stmt->fetch();

            if (!$user) {
                $error = 'Invalid or expired reset token.';
                $this->render('auth/reset-password', ['error' => $error]);
                return;
            }

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $password = $_POST['password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';

                if (empty($password)) {
                    $error = 'Password is required.';
                } elseif (strlen($password) < 6) {
                    $error = 'Password must be at least 6 characters long.';
                } elseif ($password !== $confirmPassword) {
                    $error = 'Passwords do not match.';
                } else {
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $this->pdo->prepare("UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?");

                    if ($stmt->execute([$hashedPassword, $user['id']])) {
                        $success = 'Password reset successfully! You can now log in with your new password.';
                    } else {
                        $error = 'Password reset failed. Please try again.';
                    }
                }
            }
        } catch (PDOException $e) {
            error_log("Database error in resetPassword: " . $e->getMessage());
            $error = 'Password reset failed. Please try again.';
        }

        $this->render('auth/reset-password', [
            'error' => $error ?? null,
            'success' => $success ?? null,
            'token' => $token
        ]);
    }

    public function profile()
    {
        $this->requireAuth();
        $this->render('auth/profile', []);
    }

    public function updateProfile()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $firstName = $this->sanitizeInput($_POST['first_name'] ?? '');
            $lastName = $this->sanitizeInput($_POST['last_name'] ?? '');
            $email = $this->sanitizeInput($_POST['email'] ?? '');

            if (empty($firstName) || empty($lastName) || empty($email)) {
                $error = 'All fields are required.';
            } elseif (!$this->validateEmail($email)) {
                $error = 'Please enter a valid email address.';
            } else {
                try {
                    // Check if email is already taken by another user
                    $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM users WHERE email = ? AND id != ?");
                    $stmt->execute([$email, $this->user['id']]);
                    $exists = $stmt->fetch()['count'] > 0;

                    if ($exists) {
                        $error = 'Email is already taken.';
                    } else {
                        $stmt = $this->pdo->prepare("UPDATE users SET first_name = ?, last_name = ?, email = ? WHERE id = ?");
                        if ($stmt->execute([$firstName, $lastName, $email, $this->user['id']])) {
                            $success = 'Profile updated successfully!';
                            // Refresh user data
                            $this->user = $this->getCurrentUser();
                        } else {
                            $error = 'Profile update failed. Please try again.';
                        }
                    }
                } catch (PDOException $e) {
                    error_log("Database error in updateProfile: " . $e->getMessage());
                    $error = 'Profile update failed. Please try again.';
                }
            }
        }

        $this->render('auth/profile', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function changePassword()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';

            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                $error = 'All fields are required.';
            } elseif (!password_verify($currentPassword, $this->user['password'])) {
                $error = 'Current password is incorrect.';
            } elseif (strlen($newPassword) < 6) {
                $error = 'New password must be at least 6 characters long.';
            } elseif ($newPassword !== $confirmPassword) {
                $error = 'New passwords do not match.';
            } else {
                try {
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    $stmt = $this->pdo->prepare("UPDATE users SET password = ? WHERE id = ?");

                    if ($stmt->execute([$hashedPassword, $this->user['id']])) {
                        $success = 'Password changed successfully!';
                    } else {
                        $error = 'Password change failed. Please try again.';
                    }
                } catch (PDOException $e) {
                    error_log("Database error in changePassword: " . $e->getMessage());
                    $error = 'Password change failed. Please try again.';
                }
            }
        }

        $this->render('auth/change-password', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function account()
    {
        $this->requireAuth();
        $this->render('auth/account', []);
    }

    public function addresses()
    {
        $this->requireAuth();

        try {
            $stmt = $this->pdo->prepare("SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC");
            $stmt->execute([$this->user['id']]);
            $addresses = $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Database error in addresses: " . $e->getMessage());
            $addresses = [];
        }

        $this->render('auth/addresses', ['addresses' => $addresses]);
    }

    public function addAddress()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $address = $this->sanitizeInput($_POST['address'] ?? '');
            $city = $this->sanitizeInput($_POST['city'] ?? '');
            $state = $this->sanitizeInput($_POST['state'] ?? '');
            $zipCode = $this->sanitizeInput($_POST['zip_code'] ?? '');
            $country = $this->sanitizeInput($_POST['country'] ?? '');
            $isDefault = isset($_POST['is_default']);

            if (empty($address) || empty($city) || empty($state) || empty($zipCode) || empty($country)) {
                $error = 'All fields are required.';
            } else {
                try {
                    if ($isDefault) {
                        // Remove default from other addresses
                        $stmt = $this->pdo->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?");
                        $stmt->execute([$this->user['id']]);
                    }

                    $stmt = $this->pdo->prepare("
                        INSERT INTO user_addresses (user_id, address, city, state, zip_code, country, is_default, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                    ");

                    if ($stmt->execute([$this->user['id'], $address, $city, $state, $zipCode, $country, $isDefault ? 1 : 0])) {
                        $success = 'Address added successfully!';
                    } else {
                        $error = 'Failed to add address. Please try again.';
                    }
                } catch (PDOException $e) {
                    error_log("Database error in addAddress: " . $e->getMessage());
                    $error = 'Failed to add address. Please try again.';
                }
            }
        }

        $this->redirect('account/addresses');
    }

    public function editAddress($id)
    {
        $this->requireAuth();

        try {
            $stmt = $this->pdo->prepare("SELECT * FROM user_addresses WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $this->user['id']]);
            $address = $stmt->fetch();

            if (!$address) {
                $this->redirect('account/addresses');
                return;
            }

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $addressText = $this->sanitizeInput($_POST['address'] ?? '');
                $city = $this->sanitizeInput($_POST['city'] ?? '');
                $state = $this->sanitizeInput($_POST['state'] ?? '');
                $zipCode = $this->sanitizeInput($_POST['zip_code'] ?? '');
                $country = $this->sanitizeInput($_POST['country'] ?? '');
                $isDefault = isset($_POST['is_default']);

                if (empty($addressText) || empty($city) || empty($state) || empty($zipCode) || empty($country)) {
                    $error = 'All fields are required.';
                } else {
                    if ($isDefault) {
                        // Remove default from other addresses
                        $stmt = $this->pdo->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?");
                        $stmt->execute([$this->user['id']]);
                    }

                    $stmt = $this->pdo->prepare("
                        UPDATE user_addresses 
                        SET address = ?, city = ?, state = ?, zip_code = ?, country = ?, is_default = ? 
                        WHERE id = ? AND user_id = ?
                    ");

                    if ($stmt->execute([$addressText, $city, $state, $zipCode, $country, $isDefault ? 1 : 0, $id, $this->user['id']])) {
                        $success = 'Address updated successfully!';
                    } else {
                        $error = 'Failed to update address. Please try again.';
                    }
                }
            }
        } catch (PDOException $e) {
            error_log("Database error in editAddress: " . $e->getMessage());
            $error = 'Failed to update address. Please try again.';
        }

        $this->render('auth/edit-address', [
            'address' => $address ?? null,
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function deleteAddress($id)
    {
        $this->requireAuth();

        try {
            $stmt = $this->pdo->prepare("DELETE FROM user_addresses WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $this->user['id']]);
        } catch (PDOException $e) {
            error_log("Database error in deleteAddress: " . $e->getMessage());
        }

        $this->redirect('account/addresses');
    }

    public function settings()
    {
        $this->requireAuth();
        $this->render('auth/settings', []);
    }

    public function updateSettings()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $emailNotifications = isset($_POST['email_notifications']);
            $marketingEmails = isset($_POST['marketing_emails']);
            $language = $this->sanitizeInput($_POST['language'] ?? 'en');
            $timezone = $this->sanitizeInput($_POST['timezone'] ?? 'UTC');

            try {
                $stmt = $this->pdo->prepare("
                    UPDATE users 
                    SET email_notifications = ?, marketing_emails = ?, language = ?, timezone = ? 
                    WHERE id = ?
                ");

                if ($stmt->execute([$emailNotifications ? 1 : 0, $marketingEmails ? 1 : 0, $language, $timezone, $this->user['id']])) {
                    $success = 'Settings updated successfully!';
                    // Refresh user data
                    $this->user = $this->getCurrentUser();
                } else {
                    $error = 'Failed to update settings. Please try again.';
                }
            } catch (PDOException $e) {
                error_log("Database error in updateSettings: " . $e->getMessage());
                $error = 'Failed to update settings. Please try again.';
            }
        }

        $this->render('auth/settings', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function verifyEmail($token)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT id, email FROM users WHERE verification_token = ? AND email_verified = 0");
            $stmt->execute([$token]);
            $user = $stmt->fetch();

            if ($user) {
                $stmt = $this->pdo->prepare("UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?");
                $stmt->execute([$user['id']]);

                $message = 'Email verified successfully! You can now log in.';
                $type = 'success';
            } else {
                $message = 'Invalid or expired verification token.';
                $type = 'error';
            }
        } catch (PDOException $e) {
            error_log("Database error in verifyEmail: " . $e->getMessage());
            $message = 'Verification failed. Please try again.';
            $type = 'error';
        }

        $this->render('auth/verify', [
            'message' => $message,
            'type' => $type
        ]);
    }

    public function resendVerification()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if ($this->user['email_verified']) {
                $error = 'Your email is already verified.';
            } else {
                try {
                    $verificationToken = bin2hex(random_bytes(32));
                    $stmt = $this->pdo->prepare("UPDATE users SET verification_token = ? WHERE id = ?");
                    $stmt->execute([$verificationToken, $this->user['id']]);

                    $verificationLink = APP_URL . "/verify/" . $verificationToken;
                    $body = "Hello {$this->user['first_name']},\n\nPlease verify your email by clicking the link below:\n\n$verificationLink\n\nBest regards,\n" . APP_NAME . " Team";

                    if ($this->sendEmail($this->user['email'], 'Email Verification', $body)) {
                        $success = 'Verification email sent successfully!';
                    } else {
                        $error = 'Failed to send verification email. Please try again.';
                    }
                } catch (PDOException $e) {
                    error_log("Database error in resendVerification: " . $e->getMessage());
                    $error = 'Failed to send verification email. Please try again.';
                }
            }
        }

        $this->render('auth/resend-verification', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }
}
