<div class="container mt-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>My Account
                    </h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/asma/public/account" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="/asma/public/profile" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-edit me-2"></i>Profile Settings
                    </a>
                    <a href="/asma/public/orders" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>My Orders
                    </a>
                    <a href="/asma/public/account/addresses" class="list-group-item list-group-item-action">
                        <i class="fas fa-map-marker-alt me-2"></i>Addresses
                    </a>
                    <a href="/asma/public/wishlist" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i>Wishlist
                    </a>
                    <a href="/asma/public/change-password" class="list-group-item list-group-item-action">
                        <i class="fas fa-key me-2"></i>Change Password
                    </a>
                    <a href="/asma/public/account/settings" class="list-group-item list-group-item-action active">
                        <i class="fas fa-cog me-2"></i>Account Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Dashboard Navigation -->
            <nav class="dashboard-nav">
                <ul class="dashboard-nav-list">
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/account" class="dashboard-nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/orders" class="dashboard-nav-link">
                            <i class="fas fa-shopping-bag"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/addresses" class="dashboard-nav-link">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Addresses</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/settings" class="dashboard-nav-link active">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/wishlist" class="dashboard-nav-link">
                            <i class="fas fa-heart"></i>
                            <span>Wishlist</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Account Settings</h1>
                <p class="page-subtitle">Manage your preferences, notifications, and account security.</p>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($success)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Settings Content -->
            <div class="row">
                <!-- Account Settings -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-cog me-2"></i>
                                Account Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="settings-section mb-4">
                                <h6 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    Personal Information
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-semibold">First Name</label>
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['first_name']); ?>" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-semibold">Last Name</label>
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['last_name']); ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Email Address</label>
                                    <input type="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Phone Number</label>
                                    <input type="tel" class="form-control" value="<?php echo htmlspecialchars($user['phone'] ?? 'Not provided'); ?>" readonly>
                                </div>
                                <a href="/asma/public/account" class="btn btn-primary">
                                    <i class="fas fa-edit me-2"></i>Edit Profile
                                </a>
                            </div>

                            <hr class="my-4">

                            <div class="settings-section">
                                <h6 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Security Settings
                                </h6>
                                <div class="security-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Password</h6>
                                            <small class="text-muted">Last changed: <?php echo date('M j, Y', strtotime($user['updated_at'])); ?></small>
                                        </div>
                                        <a href="/asma/public/change-password" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-key me-1"></i>Change
                                        </a>
                                    </div>
                                </div>
                                <div class="security-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Two-Factor Authentication</h6>
                                            <small class="text-muted">Add an extra layer of security</small>
                                        </div>
                                        <button class="btn btn-outline-secondary btn-sm" disabled>
                                            <i class="fas fa-mobile-alt me-1"></i>Coming Soon
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>
                                Preferences
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="settings-section mb-4">
                                <h6 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-bell me-2"></i>
                                    Email Notifications
                                </h6>
                                <div class="notification-item mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="orderUpdates" checked>
                                        <label class="form-check-label" for="orderUpdates">
                                            <strong>Order Updates</strong>
                                            <br><small class="text-muted">Get notified about order status changes</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="notification-item mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="promotions" checked>
                                        <label class="form-check-label" for="promotions">
                                            <strong>Promotions & Offers</strong>
                                            <br><small class="text-muted">Receive special offers and discounts</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="notification-item mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="newsletter">
                                        <label class="form-check-label" for="newsletter">
                                            <strong>Newsletter</strong>
                                            <br><small class="text-muted">Stay updated with latest products and news</small>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <hr class="my-4">

                            <div class="settings-section">
                                <h6 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-globe me-2"></i>
                                    Language & Region
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-semibold">Language</label>
                                        <select class="form-select">
                                            <option value="en" selected>English</option>
                                            <option value="fr">Français</option>
                                            <option value="es">Español</option>
                                            <option value="de">Deutsch</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-semibold">Currency</label>
                                        <select class="form-select">
                                            <option value="USD" selected>USD ($)</option>
                                            <option value="EUR">EUR (€)</option>
                                            <option value="GBP">GBP (£)</option>
                                            <option value="CAD">CAD (C$)</option>
                                        </select>
                                    </div>
                                </div>
                                <button class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Preferences
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy & Data -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-shield me-2"></i>
                                Privacy & Data
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="settings-section mb-4">
                                <h6 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-database me-2"></i>
                                    Data Management
                                </h6>
                                <div class="data-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Download My Data</h6>
                                            <small class="text-muted">Get a copy of your personal data</small>
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-download me-1"></i>Download
                                        </button>
                                    </div>
                                </div>
                                <div class="data-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Delete Account</h6>
                                            <small class="text-muted">Permanently delete your account and data</small>
                                        </div>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <hr class="my-4">

                            <div class="settings-section">
                                <h6 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-eye me-2"></i>
                                    Privacy Settings
                                </h6>
                                <div class="privacy-item mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="profileVisibility" checked>
                                        <label class="form-check-label" for="profileVisibility">
                                            <strong>Public Profile</strong>
                                            <br><small class="text-muted">Allow others to see your public profile</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="privacy-item mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="activitySharing">
                                        <label class="form-check-label" for="activitySharing">
                                            <strong>Activity Sharing</strong>
                                            <br><small class="text-muted">Share your activity with friends</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Connected Accounts -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-link me-2"></i>
                                Connected Accounts
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="settings-section">
                                <h6 class="fw-bold text-primary mb-3">
                                    <i class="fab fa-google me-2"></i>
                                    Social Accounts
                                </h6>
                                <div class="connected-account mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="account-icon me-3">
                                                <i class="fab fa-google text-danger"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">Google</h6>
                                                <small class="text-muted">Not connected</small>
                                            </div>
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>Connect
                                        </button>
                                    </div>
                                </div>
                                <div class="connected-account mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="account-icon me-3">
                                                <i class="fab fa-facebook text-primary"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">Facebook</h6>
                                                <small class="text-muted">Not connected</small>
                                            </div>
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>Connect
                                        </button>
                                    </div>
                                </div>
                                <div class="connected-account">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="account-icon me-3">
                                                <i class="fab fa-apple text-dark"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">Apple</h6>
                                                <small class="text-muted">Not connected</small>
                                            </div>
                                        </div>
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>Connect
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function deactivateAccount() {
        if (confirm('Are you sure you want to deactivate your account? You can reactivate it later by logging in.')) {
            // Implement deactivation logic
            window.location.href = '/account/deactivate';
        }
    }

    function deleteAccount() {
        if (confirm('Are you absolutely sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.')) {
            if (confirm('This is your final warning. Are you sure you want to permanently delete your account?')) {
                // Implement deletion logic
                window.location.href = '/account/delete';
            }
        }
    }

    function exportData() {
        // Implement data export logic
        window.location.href = '/account/export-data';
    }
</script>

<style>
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid #dee2e6;
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        color: white;
    }

    .list-group-item {
        border: none;
        border-radius: 0;
        padding: 15px 20px;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
        border-left-color: #0d6efd;
        transform: translateX(5px);
    }

    .list-group-item.active {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border-color: #0d6efd;
        border-left-color: #fff;
        box-shadow: 0 2px 10px rgba(13, 110, 253, 0.3);
    }

    .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 10px 20px;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border: none;
    }

    .btn-outline-primary {
        border-color: #0d6efd;
        color: #0d6efd;
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border-color: #0d6efd;
        color: white;
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        border-color: #6c757d;
        color: white;
    }

    .btn-outline-danger {
        border-color: #dc3545;
        color: #dc3545;
    }

    .btn-outline-danger:hover {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border-color: #dc3545;
        color: white;
    }

    .settings-section {
        padding: 20px 0;
    }

    .settings-section:first-child {
        padding-top: 0;
    }

    .settings-section h6 {
        color: #495057;
        font-size: 1.1rem;
        margin-bottom: 20px;
    }

    .security-item,
    .data-item,
    .connected-account {
        padding: 15px;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .security-item:hover,
    .data-item:hover,
    .connected-account:hover {
        background: linear-gradient(135deg, #e9ecef, #dee2e6);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .notification-item,
    .privacy-item {
        padding: 15px;
        border-radius: 10px;
        background: #f8f9fa;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .notification-item:hover,
    .privacy-item:hover {
        background: #e9ecef;
        transform: translateY(-1px);
    }

    .form-check-input {
        width: 3em;
        height: 1.5em;
        margin-top: 0.25em;
        vertical-align: top;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        border: 1px solid rgba(0, 0, 0, 0.25);
        appearance: none;
        color-adjust: exact;
        border-radius: 2em;
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 255, 255, 0.25%29'/%3e%3c/svg%3e");
    }

    .form-check-input:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .form-control,
    .form-select {
        border-radius: 10px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        transform: translateY(-1px);
    }

    .form-control[readonly] {
        background-color: #f8f9fa;
        opacity: 1;
    }

    .account-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .text-primary {
        color: #0d6efd !important;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .fw-bold {
        font-weight: 600 !important;
    }

    .fw-semibold {
        font-weight: 500 !important;
    }

    .page-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }

    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0;
    }

    .page-subtitle {
        color: #6c757d;
        margin-bottom: 0;
    }

    hr {
        border: none;
        height: 1px;
        background: linear-gradient(90deg, transparent, #dee2e6, transparent);
        margin: 2rem 0;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 8px;
    }

    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
</style>