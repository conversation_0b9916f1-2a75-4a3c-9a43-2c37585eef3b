<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Modern e-commerce platform with the latest products' ?>">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= UrlHelper::css('style.css') ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= UrlHelper::asset('images/favicon.ico') ?>">
    <link rel="icon" type="image/jpeg" href="/asma/public/assets/img/logo.jpg">
</head>

<body class="dark-mode">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo">
                    <a href="<?= UrlHelper::url('/') ?>">
                        <i class="fas fa-flask"></i>
                        <span>Cleanance Lab</span>
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="<?= UrlHelper::url('/') ?>" class="nav-link">Home</a></li>
                        <li class="dropdown">
                            <a href="<?= UrlHelper::url('/products') ?>" class="nav-link">Products <i class="fas fa-chevron-down"></i></a>
                            <ul class="dropdown-menu">
                                <?php foreach ($categories ?? [] as $category): ?>
                                    <li><a href="<?= UrlHelper::url('/products/' . $category['slug']) ?>"><?= htmlspecialchars($category['name']) ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </li>
                        <li><a href="<?= UrlHelper::url('/about') ?>" class="nav-link">About</a></li>
                        <li><a href="<?= UrlHelper::url('/contact') ?>" class="nav-link">Contact</a></li>
                    </ul>
                </nav>

                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Search -->
                    <div class="search-container">
                        <form action="<?= UrlHelper::url('/products') ?>" method="GET" class="search-form">
                            <input type="text" name="search" placeholder="Search products..." class="search-input">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>

                    <!-- Dark Mode Toggle -->
                    <button class="theme-toggle" id="themeToggle" title="Sombre Mode">
                        <i class="fas fa-moon"></i>
                    </button>

                    <!-- Wishlist -->
                    <?php if (isset($user)): ?>
                        <a href="<?= UrlHelper::url('/wishlist') ?>" class="header-icon" title="Wishlist">
                            <i class="fas fa-heart"></i>
                            <span class="badge" id="wishlistCount">0</span>
                        </a>
                    <?php endif; ?>

                    <!-- Cart -->
                    <a href="<?= UrlHelper::url('/cart') ?>" class="header-icon" title="Cart">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount"><?= count($cart ?? []) ?></span>
                    </a>

                    <!-- User Menu -->
                    <?php if (isset($user)): ?>
                        <div class="user-menu">
                            <button class="user-toggle">
                                <i class="fas fa-user"></i>
                                <span><?= htmlspecialchars($user['first_name']) ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <ul class="user-dropdown">
                                <li><a href="<?= UrlHelper::url('/profile') ?>"><i class="fas fa-user-circle"></i> Profile</a></li>
                                <li><a href="<?= UrlHelper::url('/orders') ?>"><i class="fas fa-box"></i> Orders</a></li>
                                <li><a href="<?= UrlHelper::url('/wishlist') ?>"><i class="fas fa-heart"></i> Wishlist</a></li>
                                <?php if ($user['is_admin']): ?>
                                    <li><a href="<?= UrlHelper::url('/admin') ?>"><i class="fas fa-cog"></i> Admin</a></li>
                                <?php endif; ?>
                                <li><a href="<?= UrlHelper::url('/logout') ?>"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="auth-buttons">
                            <a href="<?= UrlHelper::url('/login') ?>" class="btn btn-outline">Login</a>
                            <a href="<?= UrlHelper::url('/register') ?>" class="btn btn-primary">Register</a>
                        </div>
                    <?php endif; ?>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <div class="mobile-menu-content">
            <nav class="mobile-nav">
                <ul>
                    <li><a href="<?= UrlHelper::url('/') ?>">Home</a></li>
                    <li><a href="<?= UrlHelper::url('/products') ?>">Products</a></li>
                    <li><a href="<?= UrlHelper::url('/about') ?>">About</a></li>
                    <li><a href="<?= UrlHelper::url('/contact') ?>">Contact</a></li>
                    <?php if (isset($user)): ?>
                        <li><a href="<?= UrlHelper::url('/profile') ?>">Profile</a></li>
                        <li><a href="<?= UrlHelper::url('/orders') ?>">Orders</a></li>
                        <li><a href="<?= UrlHelper::url('/wishlist') ?>">Wishlist</a></li>
                        <?php if ($user['is_admin']): ?>
                            <li><a href="<?= UrlHelper::url('/admin') ?>">Admin</a></li>
                        <?php endif; ?>
                        <li><a href="<?= UrlHelper::url('/logout') ?>">Logout</a></li>
                    <?php else: ?>
                        <li><a href="<?= UrlHelper::url('/login') ?>">Login</a></li>
                        <li><a href="<?= UrlHelper::url('/register') ?>">Register</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main">
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= htmlspecialchars($_SESSION['success']) ?>
                <button class="alert-close">&times;</button>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-error">
                <?= htmlspecialchars($_SESSION['error']) ?>
                <button class="alert-close">&times;</button>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <?= $content ?>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Cleanance Lab</h3>
                    <p>Your trusted source for quality products and exceptional service.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="<?= UrlHelper::url('/') ?>">Home</a></li>
                        <li><a href="<?= UrlHelper::url('/products') ?>">Products</a></li>
                        <li><a href="<?= UrlHelper::url('/about') ?>">About Us</a></li>
                        <li><a href="<?= UrlHelper::url('/contact') ?>">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="<?= UrlHelper::url('/help-center') ?>">Help Center</a></li>
                        <li><a href="<?= UrlHelper::url('/shipping-info') ?>">Shipping Info</a></li>
                        <li><a href="<?= UrlHelper::url('/returns') ?>">Returns</a></li>
                        <li><a href="<?= UrlHelper::url('/privacy-policy') ?>">Privacy Policy</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <p><i class="fas fa-map-marker-alt"></i> 123 Commerce St, City, Country</p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> Cleanance Lab. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Custom JavaScript -->
    <script src="<?= UrlHelper::js('main.js') ?>"></script>
</body>

</html>