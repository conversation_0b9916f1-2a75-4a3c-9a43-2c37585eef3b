<!-- About Hero Section -->
<section class="about-hero">
    <div class="container">
        <div class="about-hero-content">
            <div class="about-hero-text">
                <h1 class="about-title">About Cleanance Lab</h1>
                <p class="about-subtitle">Your trusted partner in quality products and exceptional service since 2020</p>
            </div>
            <div class="about-hero-image">
                <div class="about-visual">
                    <i class="fas fa-flask"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our Story Section -->
<section class="our-story">
    <div class="container">
        <div class="story-content">
            <div class="story-text">
                <h2>Our Story</h2>
                <p>Cleanance Lab was founded with a simple mission: to provide high-quality products that enhance your daily life. What started as a small local shop has grown into a trusted online destination for customers worldwide.</p>
                <p>We believe that everyone deserves access to well-designed, durable products that make life easier and more enjoyable. That's why we carefully curate our selection, working directly with manufacturers to ensure quality and fair pricing.</p>
            </div>
            <div class="story-stats">
                <div class="stat-item">
                    <div class="stat-number">10K+</div>
                    <div class="stat-label">Happy Customers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Products</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Countries Served</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">4.8</div>
                    <div class="stat-label">Average Rating</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our Values Section -->
<section class="our-values">
    <div class="container">
        <div class="section-header">
            <h2>Our Values</h2>
            <p>The principles that guide everything we do</p>
        </div>
        
        <div class="values-grid">
            <div class="value-card">
                <div class="value-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h3>Quality First</h3>
                <p>We never compromise on quality. Every product in our collection meets our high standards for durability, design, and performance.</p>
            </div>
            
            <div class="value-card">
                <div class="value-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h3>Customer Focus</h3>
                <p>Your satisfaction is our priority. We're here to help you find the perfect products and provide exceptional service every step of the way.</p>
            </div>
            
            <div class="value-card">
                <div class="value-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <h3>Sustainability</h3>
                <p>We're committed to environmental responsibility, offering eco-friendly products and sustainable packaging options.</p>
            </div>
            
            <div class="value-card">
                <div class="value-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <h3>Integrity</h3>
                <p>We believe in honest, transparent business practices. What you see is what you get, with no hidden fees or surprises.</p>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="team-section">
    <div class="container">
        <div class="section-header">
            <h2>Meet Our Team</h2>
            <p>The passionate people behind Cleanance Lab</p>
        </div>
        
        <div class="team-grid">
            <div class="team-member">
                <div class="member-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h3>Sarah Johnson</h3>
                <p class="member-role">Founder & CEO</p>
                <p class="member-bio">With over 15 years of experience in e-commerce, Sarah leads our mission to provide exceptional products and service.</p>
            </div>
            
            <div class="team-member">
                <div class="member-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h3>Michael Chen</h3>
                <p class="member-role">Head of Product</p>
                <p class="member-bio">Michael ensures every product meets our quality standards and delivers value to our customers.</p>
            </div>
            
            <div class="team-member">
                <div class="member-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h3>Emily Rodriguez</h3>
                <p class="member-role">Customer Success</p>
                <p class="member-bio">Emily and her team are dedicated to providing outstanding customer support and ensuring your satisfaction.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="about-cta">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Experience Cleanance Lab?</h2>
            <p>Join thousands of satisfied customers who trust us for their daily needs.</p>
            <div class="cta-actions">
                <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-shopping-bag"></i>
                    Shop Now
                </a>
                <a href="<?= UrlHelper::url('/contact') ?>" class="btn btn-outline btn-lg">
                    <i class="fas fa-envelope"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>

<style>
    .about-hero {
        background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
        padding: 120px 0 80px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .about-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(126, 87, 194, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .about-hero-content {
        max-width: 900px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    .about-title {
        font-size: clamp(2.5rem, 5vw, 3.5rem);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 24px;
        line-height: 1.2;
    }

    .about-subtitle {
        font-size: clamp(1.1rem, 2.5vw, 1.25rem);
        color: var(--text-secondary);
        margin-bottom: 48px;
        line-height: 1.5;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .about-visual {
        font-size: clamp(4rem, 10vw, 8rem);
        color: var(--primary-purple);
        margin-top: 48px;
        opacity: 0.8;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .our-story {
        padding: 100px 0;
        background-color: var(--dark-bg);
        position: relative;
    }

    .our-story::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    }

    .story-content {
        display: grid;
        grid-template-columns: 1.5fr 1fr;
        gap: 80px;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
    }

    .story-text h2 {
        font-size: clamp(2rem, 4vw, 2.5rem);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 32px;
        line-height: 1.3;
    }

    .story-text p {
        font-size: 1.125rem;
        color: var(--text-secondary);
        margin-bottom: 24px;
        line-height: 1.7;
        font-weight: 400;
    }

    .story-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
    }

    .stat-item {
        text-align: center;
        padding: 32px 24px;
        background-color: var(--dark-card);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-purple), #9c27b0);
    }

    .stat-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .stat-number {
        font-size: clamp(2rem, 4vw, 2.5rem);
        font-weight: 700;
        color: var(--primary-purple);
        margin-bottom: 12px;
        line-height: 1;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: 1rem;
        font-weight: 500;
    }

    .our-values {
        padding: 100px 0;
        background-color: var(--dark-surface);
        position: relative;
    }

    .our-values::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    }

    .section-header {
        text-align: center;
        margin-bottom: 80px;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .section-header h2 {
        font-size: clamp(2rem, 4vw, 2.5rem);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 16px;
        line-height: 1.3;
    }

    .section-header p {
        font-size: 1.125rem;
        color: var(--text-secondary);
        line-height: 1.6;
    }

    .values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 32px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .value-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 40px 32px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .value-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-purple), #9c27b0);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .value-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .value-card:hover::before {
        transform: scaleX(1);
    }

    .value-icon {
        font-size: clamp(2.5rem, 5vw, 3rem);
        color: var(--primary-purple);
        margin-bottom: 24px;
        transition: transform 0.3s ease;
    }

    .value-card:hover .value-icon {
        transform: scale(1.1);
    }

    .value-card h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 16px;
        line-height: 1.3;
    }

    .value-card p {
        color: var(--text-secondary);
        line-height: 1.7;
        font-size: 1rem;
    }

    .team-section {
        padding: 100px 0;
        background-color: var(--dark-bg);
        position: relative;
    }

    .team-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    }

    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 32px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .team-member {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 40px 32px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .team-member::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-purple), #9c27b0);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .team-member:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .team-member:hover::before {
        transform: scaleX(1);
    }

    .member-avatar {
        font-size: clamp(3rem, 6vw, 4rem);
        color: var(--primary-purple);
        margin-bottom: 24px;
        transition: transform 0.3s ease;
    }

    .team-member:hover .member-avatar {
        transform: scale(1.1);
    }

    .team-member h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 12px;
        line-height: 1.3;
    }

    .member-role {
        color: var(--primary-purple);
        font-weight: 500;
        margin-bottom: 16px;
        font-size: 1rem;
    }

    .member-bio {
        color: var(--text-secondary);
        line-height: 1.7;
        font-size: 1rem;
    }

    .about-cta {
        padding: 120px 0;
        background: linear-gradient(135deg, var(--primary-purple) 0%, #9c27b0 100%);
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .about-cta::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .cta-content {
        position: relative;
        z-index: 1;
        max-width: 800px;
        margin: 0 auto;
    }

    .cta-content h2 {
        font-size: clamp(2rem, 4vw, 2.5rem);
        font-weight: 700;
        color: white;
        margin-bottom: 24px;
        line-height: 1.3;
    }

    .cta-content p {
        font-size: 1.125rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 48px;
        line-height: 1.6;
    }

    .cta-actions {
        display: flex;
        gap: 24px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .cta-actions .btn {
        padding: 16px 32px;
        font-size: 1rem;
        font-weight: 500;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .cta-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .story-content {
            grid-template-columns: 1fr;
            gap: 60px;
        }

        .story-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }

        .values-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
        }

        .team-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
        }
    }

    @media (max-width: 768px) {
        .about-hero {
            padding: 80px 0 60px 0;
        }

        .our-story,
        .our-values,
        .team-section {
            padding: 80px 0;
        }

        .about-cta {
            padding: 80px 0;
        }

        .story-stats {
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .stat-item {
            padding: 24px 16px;
        }

        .value-card,
        .team-member {
            padding: 32px 24px;
        }

        .cta-actions {
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .cta-actions .btn {
            width: 100%;
            max-width: 300px;
        }
    }

    @media (max-width: 480px) {
        .story-stats {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .values-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .team-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .about-hero {
            padding: 60px 0;
        }

        .our-story,
        .our-values,
        .team-section {
            padding: 60px 0;
        }

        .about-cta {
            padding: 60px 0;
        }
    }
</style> 