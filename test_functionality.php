<?php

/**
 * Test script to check functionality
 */

require_once 'config/app.php';

echo "<h1>Cleanance Lab Functionality Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    $dbConfig = require 'config/database.php';
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // Test tables exist
    $tables = ['users', 'products', 'categories', 'orders', 'cart_items'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test 2: Check if admin user exists
echo "<h2>2. Admin User Test</h2>";
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE is_admin = 1 LIMIT 1");
    $stmt->execute();
    $admin = $stmt->fetch();

    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user exists: " . htmlspecialchars($admin['email']) . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠ No admin user found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Admin user check failed: " . $e->getMessage() . "</p>";
}

// Test 3: Check products and categories
echo "<h2>3. Sample Data Test</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
    $productCount = $stmt->fetch()['count'];
    echo "<p style='color: " . ($productCount > 0 ? 'green' : 'orange') . "'>Products: $productCount</p>";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE is_active = 1");
    $categoryCount = $stmt->fetch()['count'];
    echo "<p style='color: " . ($categoryCount > 0 ? 'green' : 'orange') . "'>Categories: $categoryCount</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Sample data check failed: " . $e->getMessage() . "</p>";
}

// Test 4: Check file permissions
echo "<h2>4. File Permissions Test</h2>";
$directories = [
    'logs' => 'logs',
    'uploads' => 'public/uploads',
    'uploads/products' => 'public/uploads/products',
    'uploads/users' => 'public/uploads/users'
];

foreach ($directories as $name => $path) {
    if (file_exists($path)) {
        if (is_writable($path)) {
            echo "<p style='color: green;'>✓ Directory '$name' is writable</p>";
        } else {
            echo "<p style='color: red;'>✗ Directory '$name' is not writable</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Directory '$name' does not exist</p>";
    }
}

// Test 5: Check environment configuration
echo "<h2>5. Environment Configuration Test</h2>";
if (file_exists('.env')) {
    echo "<p style='color: green;'>✓ .env file exists</p>";

    // Check critical settings
    $criticalSettings = ['DB_HOST', 'DB_NAME', 'SECRET_KEY', 'APP_URL'];
    foreach ($criticalSettings as $setting) {
        $value = env($setting);
        if (!empty($value)) {
            echo "<p style='color: green;'>✓ $setting is configured</p>";
        } else {
            echo "<p style='color: red;'>✗ $setting is not configured</p>";
        }
    }
} else {
    echo "<p style='color: red;'>✗ .env file missing</p>";
}

// Test 6: Check helper classes
echo "<h2>6. Helper Classes Test</h2>";

// Set up autoloader like in index.php
spl_autoload_register(function ($class) {
    $paths = [
        __DIR__ . '/app/controllers/',
        __DIR__ . '/app/models/',
        __DIR__ . '/app/helpers/'
    ];

    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

$helpers = ['SecurityHelper', 'CacheHelper', 'SearchHelper', 'PaymentHelper', 'EmailHelper'];
foreach ($helpers as $helper) {
    $helperFile = __DIR__ . '/app/helpers/' . $helper . '.php';
    if (file_exists($helperFile)) {
        echo "<p style='color: green;'>✓ $helper file exists</p>";
        require_once $helperFile;
        if (class_exists($helper)) {
            echo "<p style='color: green;'>✓ $helper class loaded</p>";
        } else {
            echo "<p style='color: red;'>✗ $helper class not loaded</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ $helper file missing: $helperFile</p>";
    }
}

// Test 7: Check routes
echo "<h2>7. Routes Test</h2>";
$routes = require 'routes/web.php';
$criticalRoutes = ['GET|/', 'GET|/products', 'GET|/login', 'GET|/admin'];
foreach ($criticalRoutes as $route) {
    if (isset($routes[$route])) {
        echo "<p style='color: green;'>✓ Route '$route' exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Route '$route' missing</p>";
    }
}

echo "<h2>Test Complete</h2>";
echo "<p>Check the results above to identify any issues that need to be fixed.</p>";
