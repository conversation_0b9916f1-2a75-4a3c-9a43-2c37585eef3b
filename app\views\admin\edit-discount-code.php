<section class="admin-section">
    <h1>Edit Discount Code</h1>
    <form action="/admin/discount-codes/edit/<?= $discountCode['id'] ?>" method="POST" class="admin-form">
        <label>Code <input type="text" name="code" value="<?= htmlspecialchars($discountCode['code']) ?>" required></label>
        <label>Type
            <select name="discount_type">
                <option value="percentage" <?= $discountCode['discount_type'] === 'percentage' ? 'selected' : '' ?>>Percentage</option>
                <option value="fixed" <?= $discountCode['discount_type'] === 'fixed' ? 'selected' : '' ?>>Fixed</option>
            </select>
        </label>
        <label>Value <input type="number" name="discount_value" step="0.01" value="<?= $discountCode['discount_value'] ?>" required></label>
        <label>Minimum Order Amount <input type="number" name="minimum_order_amount" step="0.01" value="<?= $discountCode['minimum_order_amount'] ?>"></label>
        <label>Maximum Discount <input type="number" name="maximum_discount" step="0.01" value="<?= $discountCode['maximum_discount'] ?>"></label>
        <label>Status
            <select name="is_active">
                <option value="1" <?= $discountCode['is_active'] ? 'selected' : '' ?>>Active</option>
                <option value="0" <?= !$discountCode['is_active'] ? 'selected' : '' ?>>Inactive</option>
            </select>
        </label>
        <button type="submit" class="btn btn-primary">Update Discount Code</button>
    </form>
</section>
<style>
    .admin-section {
        padding: 40px 0;
        max-width: 600px;
        margin: 0 auto;
    }

    .admin-form {
        display: flex;
        flex-direction: column;
        gap: 18px;
        background: var(--dark-card);
        padding: 32px;
        border-radius: 12px;
    }

    .admin-form label {
        color: var(--text-primary);
        font-weight: 500;
    }

    .admin-form input,
    .admin-form select {
        width: 100%;
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        background: var(--dark-surface);
        color: var(--text-primary);
    }
</style>