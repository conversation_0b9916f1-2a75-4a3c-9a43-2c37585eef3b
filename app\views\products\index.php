<!-- Products Header -->
<div class="products-header">
    <div class="container">
        <div class="products-header-content">
            <div class="products-title">
                <h1 class="page-title">
                    <?php if ($currentCategory): ?>
                        <?= htmlspecialchars($currentCategory) ?>
                    <?php else: ?>
                        All Products
                    <?php endif; ?>
                </h1>
                <p class="products-count"><?= $totalProducts ?> products found</p>
            </div>

            <div class="products-actions">
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid" title="Grid View">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>

                <div class="sort-select">
                    <select id="sortSelect" onchange="sortProducts(this.value)">
                        <option value="newest" <?= ($_GET['sort'] ?? 'newest') === 'newest' ? 'selected' : '' ?>>Newest First</option>
                        <option value="oldest" <?= ($_GET['sort'] ?? '') === 'oldest' ? 'selected' : '' ?>>Oldest First</option>
                        <option value="price-low" <?= ($_GET['sort'] ?? '') === 'price-low' ? 'selected' : '' ?>>Price: Low to High</option>
                        <option value="price-high" <?= ($_GET['sort'] ?? '') === 'price-high' ? 'selected' : '' ?>>Price: High to Low</option>
                        <option value="name-asc" <?= ($_GET['sort'] ?? '') === 'name-asc' ? 'selected' : '' ?>>Name: A to Z</option>
                        <option value="name-desc" <?= ($_GET['sort'] ?? '') === 'name-desc' ? 'selected' : '' ?>>Name: Z to A</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products Content -->
<div class="products-content">
    <div class="container">
        <div class="products-layout">
            <!-- Filters Sidebar -->
            <aside class="filters-sidebar" id="filtersSidebar">
                <div class="filters-header">
                    <h3>Filters</h3>
                    <button class="clear-filters" onclick="clearFilters()">Clear All</button>
                </div>

                <!-- Search Filter -->
                <div class="filter-group">
                    <h4>Search</h4>
                    <form action="" method="GET" class="search-filter-form" id="searchForm">
                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                            placeholder="Search products..." class="search-input" id="searchInput">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Category Filter -->
                <div class="filter-group">
                    <h4>Categories</h4>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="radio" name="category" value=""
                                <?= empty($currentCategory) ? 'checked' : '' ?>
                                onchange="filterProducts()">
                            <span>All Categories</span>
                        </label>
                        <?php foreach ($categories as $category): ?>
                            <label class="filter-option">
                                <input type="radio" name="category" value="<?= htmlspecialchars($category['slug']) ?>"
                                    <?= $currentCategory === $category['slug'] ? 'checked' : '' ?>
                                    onchange="filterProducts()">
                                <span><?= htmlspecialchars($category['name']) ?></span>
                            </label>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Price Filter -->
                <div class="filter-group">
                    <h4>Price Range</h4>
                    <div class="price-filter">
                        <div class="price-inputs">
                            <input type="number" id="minPrice" name="min_price"
                                value="<?= htmlspecialchars($minPrice) ?>"
                                placeholder="Min" min="0" step="0.01">
                            <span>-</span>
                            <input type="number" id="maxPrice" name="max_price"
                                value="<?= htmlspecialchars($maxPrice) ?>"
                                placeholder="Max" min="0" step="0.01">
                        </div>
                        <button type="button" class="btn btn-primary btn-sm" onclick="filterProducts()">
                            Apply
                        </button>
                    </div>
                </div>

                <!-- Availability Filter -->
                <div class="filter-group">
                    <h4>Availability</h4>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" name="in_stock" value="1"
                                <?= isset($_GET['in_stock']) ? 'checked' : '' ?>
                                onchange="filterProducts()">
                            <span>In Stock</span>
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" name="on_sale" value="1"
                                <?= isset($_GET['on_sale']) ? 'checked' : '' ?>
                                onchange="filterProducts()">
                            <span>On Sale</span>
                        </label>
                    </div>
                </div>
            </aside>

            <!-- Products Grid -->
            <main class="products-main">
                <?php if (empty($products)): ?>
                    <div class="no-products">
                        <div class="no-products-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>No products found</h3>
                        <p>Try adjusting your search or filter criteria</p>
                        <a href="<?= APP_URL ?>/products" class="btn btn-primary">View All Products</a>
                    </div>
                <?php else: ?>
                    <div class="products-grid" id="productsGrid">
                        <?php foreach ($products as $product): ?>
                            <div class="product-card fade-in">
                                <div class="product-image">
                                    <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                                        alt="<?= htmlspecialchars($product['name']) ?>"
                                        loading="lazy">
                                    <?php if ($product['sale_price']): ?>
                                        <div class="product-badge">Sale</div>
                                    <?php endif; ?>
                                    <?php if ($product['stock_quantity'] <= 0): ?>
                                        <div class="product-badge out-of-stock">Out of Stock</div>
                                    <?php endif; ?>
                                    <div class="product-overlay">
                                        <button class="btn btn-primary btn-sm quick-view" data-product-id="<?= $product['id'] ?>">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline btn-sm add-to-wishlist" data-product-id="<?= $product['id'] ?>">
                                            <i class="far fa-heart"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="product-content">
                                    <div class="product-category"><?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?></div>
                                    <h3 class="product-title">
                                        <a href="<?= APP_URL ?>/product/<?= htmlspecialchars($product['slug']) ?>">
                                            <?= htmlspecialchars($product['name']) ?>
                                        </a>
                                    </h3>

                                    <div class="product-price">
                                        <span class="product-price-current">
                                            $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                                        </span>
                                        <?php if ($product['sale_price']): ?>
                                            <span class="product-price-original">
                                                $<?= number_format($product['price'], 2) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <div class="product-actions">
                                        <?php if ($product['stock_quantity'] > 0): ?>
                                            <button class="btn btn-primary add-to-cart"
                                                data-product-id="<?= $product['id'] ?>"
                                                data-quantity="1">
                                                <i class="fas fa-shopping-cart"></i>
                                                Add to Cart
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-outline" disabled>
                                                <i class="fas fa-times"></i>
                                                Out of Stock
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($currentPage > 1): ?>
                                <a href="<?= buildPaginationUrl($currentPage - 1) ?>" class="pagination-btn">
                                    <i class="fas fa-chevron-left"></i>
                                    Previous
                                </a>
                            <?php endif; ?>

                            <div class="pagination-numbers">
                                <?php
                                $start = max(1, $currentPage - 2);
                                $end = min($totalPages, $currentPage + 2);

                                if ($start > 1): ?>
                                    <a href="<?= buildPaginationUrl(1) ?>" class="pagination-btn">1</a>
                                    <?php if ($start > 2): ?>
                                        <span class="pagination-ellipsis">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                    <a href="<?= buildPaginationUrl($i) ?>"
                                        class="pagination-btn <?= $i === $currentPage ? 'active' : '' ?>">
                                        <?= $i ?>
                                    </a>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <span class="pagination-ellipsis">...</span>
                                    <?php endif; ?>
                                    <a href="<?= buildPaginationUrl($totalPages) ?>" class="pagination-btn"><?= $totalPages ?></a>
                                <?php endif; ?>
                            </div>

                            <?php if ($currentPage < $totalPages): ?>
                                <a href="<?= buildPaginationUrl($currentPage + 1) ?>" class="pagination-btn">
                                    Next
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>
</div>

<!-- Mobile Filters Toggle -->
<div class="mobile-filters-toggle">
    <button class="btn btn-outline" onclick="toggleMobileFilters()">
        <i class="fas fa-filter"></i>
        Filters
    </button>
</div>

<?php
// Helper function to build pagination URLs with current filters
function buildPaginationUrl($page)
{
    $params = $_GET;
    $params['page'] = $page;
    return '?' . http_build_query($params);
}
?>

<style>
    /* Products Header */
    .products-header {
        background-color: var(--dark-card);
        border-bottom: 1px solid var(--border-color);
        padding: var(--spacing-lg) 0;
        position: sticky;
        top: 0;
        z-index: 100;
        backdrop-filter: blur(10px);
    }

    .products-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .page-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .products-count {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .products-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex-shrink: 0;
    }

    .view-toggle {
        display: flex;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        overflow: hidden;
        background-color: var(--dark-surface);
    }

    .view-btn {
        background: none;
        border: none;
        padding: var(--spacing-sm) var(--spacing-md);
        color: var(--text-secondary);
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .view-btn.active,
    .view-btn:hover {
        background-color: var(--primary-purple);
        color: var(--white);
    }

    .sort-select select {
        padding: var(--spacing-sm) var(--spacing-md);
        background-color: var(--dark-surface);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        cursor: pointer;
    }

    /* Products Content */
    .products-content {
        padding: var(--spacing-xl) 0;
        min-height: 60vh;
    }

    .products-layout {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: var(--spacing-xl);
        align-items: start;
    }

    /* Filters Sidebar */
    .filters-sidebar {
        background-color: var(--dark-surface);
        padding: var(--spacing-lg);
        height: fit-content;
        position: sticky;
        top: 120px;
        max-height: calc(100vh - 140px);
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--border-color) transparent;
    }

    .filters-sidebar::-webkit-scrollbar {
        width: 6px;
    }

    .filters-sidebar::-webkit-scrollbar-track {
        background: transparent;
    }

    .filters-sidebar::-webkit-scrollbar-thumb {
        background-color: var(--border-color);
        border-radius: 3px;
    }

    .filters-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
        position: sticky;
        top: 0;
        background-color: var(--dark-surface);
        z-index: 10;
    }

    .filters-header h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
    }

    .clear-filters {
        background: none;
        border: none;
        color: var(--primary-purple);
        font-size: var(--font-size-sm);
        cursor: pointer;
        text-decoration: underline;
        transition: color var(--transition-fast);
    }

    .clear-filters:hover {
        color: var(--primary-purple-light);
    }

    .filter-group {
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-lg);
        border-bottom: 1px solid var(--border-color);
    }

    .filter-group:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .filter-group h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .search-filter-form {
        display: flex;
        gap: var(--spacing-sm);
    }

    .search-filter-form .search-input {
        flex: 1;
        padding: var(--spacing-sm) var(--spacing-md);
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        transition: border-color var(--transition-fast);
    }

    .search-filter-form .search-input:focus {
        outline: none;
        border-color: var(--primary-purple);
    }

    .search-filter-form .search-btn {
        padding: var(--spacing-sm);
        background-color: var(--primary-purple);
        border: none;
        border-radius: var(--radius-md);
        color: var(--white);
        cursor: pointer;
        transition: background-color var(--transition-fast);
    }

    .search-filter-form .search-btn:hover {
        background-color: var(--primary-purple-light);
    }

    .filter-options {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .filter-option {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        cursor: pointer;
        padding: var(--spacing-xs);
        border-radius: var(--radius-sm);
        transition: background-color var(--transition-fast);
    }

    .filter-option:hover {
        background-color: var(--dark-card);
    }

    .filter-option input[type="radio"],
    .filter-option input[type="checkbox"] {
        accent-color: var(--primary-purple);
    }

    .filter-option span {
        color: var(--text-primary);
        font-size: var(--font-size-sm);
    }

    .price-filter {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .price-inputs {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .price-inputs input {
        flex: 1;
        padding: var(--spacing-sm);
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        text-align: center;
    }

    .price-inputs span {
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* Products Grid */
    .products-main {
        min-height: 400px;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .products-grid.list-view {
        grid-template-columns: 1fr;
    }

    .products-grid.list-view .product-card {
        display: grid;
        grid-template-columns: 200px 1fr auto;
        gap: var(--spacing-lg);
        align-items: center;
    }

    .product-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
        transition: all var(--transition-normal);
        position: relative;
    }

    .product-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.15);
        border-color: var(--primary-purple);
    }

    .product-image {
        position: relative;
        aspect-ratio: 1;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-normal);
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .product-badge {
        position: absolute;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        background-color: var(--primary-purple);
        color: var(--white);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-size-xs);
        font-weight: 600;
        text-transform: uppercase;
    }

    .product-badge.out-of-stock {
        background-color: var(--error-color);
    }

    .product-overlay {
        position: absolute;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        opacity: 0;
        transform: translateX(10px);
        transition: all var(--transition-normal);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
        transform: translateX(0);
    }

    .product-content {
        padding: var(--spacing-lg);
    }

    .product-category {
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: var(--spacing-xs);
    }

    .product-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        line-height: 1.3;
    }

    .product-title a {
        color: inherit;
        text-decoration: none;
        transition: color var(--transition-fast);
    }

    .product-title a:hover {
        color: var(--primary-purple);
    }

    .product-price {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-lg);
    }

    .product-price-current {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--primary-purple);
    }

    .product-price-original {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        text-decoration: line-through;
    }

    .product-actions {
        display: flex;
        gap: var(--spacing-sm);
    }

    .product-actions .btn {
        flex: 1;
        justify-content: center;
    }

    /* Pagination */
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: var(--spacing-sm);
        margin-top: var(--spacing-xl);
    }

    .pagination-btn {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        text-decoration: none;
        font-size: var(--font-size-sm);
        transition: all var(--transition-fast);
    }

    .pagination-btn:hover {
        background-color: var(--primary-purple);
        border-color: var(--primary-purple);
        color: var(--white);
    }

    .pagination-btn.active {
        background-color: var(--primary-purple);
        border-color: var(--primary-purple);
        color: var(--white);
    }

    .pagination-numbers {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .pagination-ellipsis {
        color: var(--text-secondary);
        padding: 0 var(--spacing-sm);
    }

    /* No Products */
    .no-products {
        text-align: center;
        padding: var(--spacing-xxl) var(--spacing-lg);
    }

    .no-products-icon {
        font-size: 4rem;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    .no-products h3 {
        font-size: var(--font-size-2xl);
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .no-products p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    /* Mobile Filters Toggle */
    .mobile-filters-toggle {
        display: none;
        position: fixed;
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        z-index: 1000;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .products-layout {
            grid-template-columns: 1fr;
        }

        .filters-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: 2000;
            transform: translateX(-100%);
            transition: transform var(--transition-normal);
            overflow-y: auto;
            max-height: none;
        }

        .filters-sidebar.active {
            transform: translateX(0);
        }

        .mobile-filters-toggle {
            display: block;
        }

        .products-header-content {
            flex-direction: column;
            gap: var(--spacing-md);
            text-align: center;
        }

        .products-actions {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 768px) {
        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        }

        .products-grid.list-view .product-card {
            grid-template-columns: 1fr;
        }

        .pagination {
            flex-wrap: wrap;
        }

        .pagination-numbers {
            display: none;
        }
    }

    @media (max-width: 480px) {
        .products-grid {
            grid-template-columns: 1fr;
        }

        .product-actions {
            flex-direction: column;
        }
    }
</style>

<script>
    // Filter state management
    let filterState = {};

    // Initialize filter state from URL parameters
    function initializeFilterState() {
        const urlParams = new URLSearchParams(window.location.search);
        filterState = {
            search: urlParams.get('search') || '',
            category: urlParams.get('category') || '',
            min_price: urlParams.get('min_price') || '',
            max_price: urlParams.get('max_price') || '',
            in_stock: urlParams.get('in_stock') === '1',
            on_sale: urlParams.get('on_sale') === '1',
            sort: urlParams.get('sort') || 'newest',
            page: urlParams.get('page') || '1'
        };
    }

    // AJAX Filter and sort functionality
    function filterProducts(pushState = true) {
        // Gather filter state
        filterState.search = document.querySelector('input[name="search"]').value;
        filterState.category = document.querySelector('input[name="category"]:checked')?.value || '';
        filterState.min_price = document.getElementById('minPrice').value;
        filterState.max_price = document.getElementById('maxPrice').value;
        filterState.in_stock = document.querySelector('input[name="in_stock"]').checked ? '1' : '';
        filterState.on_sale = document.querySelector('input[name="on_sale"]').checked ? '1' : '';
        filterState.sort = document.getElementById('sortSelect')?.value || '';
        filterState.page = '1'; // Reset to first page on filter

        // Build query string
        const params = new URLSearchParams();
        Object.entries(filterState).forEach(([key, value]) => {
            if (value && value !== '') params.append(key, value);
        });
        const url = window.location.pathname + (params.toString() ? '?' + params.toString() : '');

        // Update URL
        if (pushState) {
            window.history.pushState({}, '', url);
        }

        // Show loading state
        const productsGrid = document.getElementById('productsGrid');
        if (productsGrid) {
            productsGrid.innerHTML = '<div style="text-align:center;padding:2rem"><i class="fas fa-spinner fa-spin fa-2x"></i></div>';
        }

        // AJAX fetch
        fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(res => {
                if (!res.ok) {
                    throw new Error('Network response was not ok');
                }
                return res.json();
            })
            .then(data => {
                // Replace sidebar and grid
                const sidebar = document.querySelector('.filters-sidebar');
                if (sidebar && data.sidebar) {
                    sidebar.outerHTML = data.sidebar;
                }
                if (productsGrid && data.grid) {
                    productsGrid.outerHTML = data.grid;
                }
                // Re-bind events
                bindFilterEvents();
            })
            .catch(error => {
                console.error('Filter error:', error);
                // Fallback to page reload if AJAX fails
                window.location.href = url;
            });
    }

    // Bind filter events for AJAX
    function bindFilterEvents() {
        // Category radio buttons
        document.querySelectorAll('input[name="category"]').forEach(input => {
            input.onchange = () => filterProducts();
        });

        // Checkboxes
        document.querySelectorAll('input[name="in_stock"], input[name="on_sale"]').forEach(input => {
            input.onchange = () => filterProducts();
        });

        // Price inputs
        const minPrice = document.getElementById('minPrice');
        const maxPrice = document.getElementById('maxPrice');
        if (minPrice) minPrice.onchange = () => filterProducts();
        if (maxPrice) maxPrice.onchange = () => filterProducts();

        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.oninput = function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => filterProducts(), 200);
            };
        }

        // Sort select
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) sortSelect.onchange = () => filterProducts();

        // Clear filters
        const clearBtn = document.querySelector('.clear-filters');
        if (clearBtn) clearBtn.onclick = clearFilters;
    }

    // Overwrite clearFilters for AJAX
    function clearFilters() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';

        const allCategories = document.querySelector('input[name="category"][value=""]');
        if (allCategories) allCategories.checked = true;

        const minPrice = document.getElementById('minPrice');
        if (minPrice) minPrice.value = '';

        const maxPrice = document.getElementById('maxPrice');
        if (maxPrice) maxPrice.value = '';

        const inStock = document.querySelector('input[name="in_stock"]');
        if (inStock) inStock.checked = false;

        const onSale = document.querySelector('input[name="on_sale"]');
        if (onSale) onSale.checked = false;

        filterProducts();
    }

    // On popstate (back/forward), re-fetch
    window.addEventListener('popstate', function() {
        filterProducts(false);
    });

    // View toggle
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            const view = this.dataset.view;
            const grid = document.getElementById('productsGrid');

            if (view === 'list') {
                grid.classList.add('list-view');
            } else {
                grid.classList.remove('list-view');
            }

            // Save view preference
            localStorage.setItem('productView', view);
        });
    });

    // Mobile filters toggle
    function toggleMobileFilters() {
        const sidebar = document.querySelector('.filters-sidebar');
        sidebar.classList.toggle('active');
    }

    // Close mobile filters when clicking outside
    document.addEventListener('click', function(e) {
        const sidebar = document.querySelector('.filters-sidebar');
        const toggle = document.querySelector('.mobile-filters-toggle');

        if (sidebar && sidebar.classList.contains('active') &&
            !sidebar.contains(e.target) && !toggle.contains(e.target)) {
            sidebar.classList.remove('active');
        }
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeFilterState();
        bindFilterEvents();

        // Restore view preference
        const savedView = localStorage.getItem('productView');
        if (savedView) {
            const viewBtn = document.querySelector(`[data-view="${savedView}"]`);
            if (viewBtn) {
                viewBtn.click();
            }
        }

        // Add loading states for cart and wishlist
        document.addEventListener('click', function(e) {
            if (e.target.closest('.add-to-cart, .add-to-wishlist')) {
                const btn = e.target.closest('.add-to-cart, .add-to-wishlist');
                btn.style.pointerEvents = 'none';
                const originalHtml = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                setTimeout(() => {
                    btn.style.pointerEvents = 'auto';
                    btn.innerHTML = originalHtml;
                }, 2000);
            }
        });
    });

    // Global search timeout
    let searchTimeout;
</script>