<?php // Layout is handled by the controller. Remove require_once. 
?>

<div class="container mx-auto py-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-white mb-8">Profile Settings</h1>

        <?php if (isset($error)): ?>
            <div class="alert alert-error mb-6">
                <?= htmlspecialchars($error) ?>
                <button class="alert-close">&times;</button>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="alert alert-success mb-6">
                <?= htmlspecialchars($success) ?>
                <button class="alert-close">&times;</button>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Personal Information</h2>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= UrlHelper::url('/profile') ?>" class="space-y-6">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" id="first_name" name="first_name"
                                value="<?= htmlspecialchars($user['first_name'] ?? '') ?>"
                                class="form-input" required>
                        </div>

                        <div class="form-group">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" id="last_name" name="last_name"
                                value="<?= htmlspecialchars($user['last_name'] ?? '') ?>"
                                class="form-input" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" id="email" name="email"
                            value="<?= htmlspecialchars($user['email'] ?? '') ?>"
                            class="form-input" readonly>
                        <small class="text-gray-400">Email cannot be changed</small>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" id="phone" name="phone"
                            value="<?= htmlspecialchars($user['phone'] ?? '') ?>"
                            class="form-input">
                    </div>

                    <div class="form-group">
                        <label for="address" class="form-label">Address</label>
                        <textarea id="address" name="address" rows="3"
                            class="form-textarea"><?= htmlspecialchars($user['address'] ?? '') ?></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label for="city" class="form-label">City</label>
                            <input type="text" id="city" name="city"
                                value="<?= htmlspecialchars($user['city'] ?? '') ?>"
                                class="form-input">
                        </div>

                        <div class="form-group">
                            <label for="state" class="form-label">State</label>
                            <input type="text" id="state" name="state"
                                value="<?= htmlspecialchars($user['state'] ?? '') ?>"
                                class="form-input">
                        </div>

                        <div class="form-group">
                            <label for="zip_code" class="form-label">ZIP Code</label>
                            <input type="text" id="zip_code" name="zip_code"
                                value="<?= htmlspecialchars($user['zip_code'] ?? '') ?>"
                                class="form-input">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="country" class="form-label">Country</label>
                        <select id="country" name="country" class="form-select">
                            <option value="United States" <?= ($user['country'] ?? '') === 'United States' ? 'selected' : '' ?>>United States</option>
                            <option value="Canada" <?= ($user['country'] ?? '') === 'Canada' ? 'selected' : '' ?>>Canada</option>
                            <option value="United Kingdom" <?= ($user['country'] ?? '') === 'United Kingdom' ? 'selected' : '' ?>>United Kingdom</option>
                            <option value="Australia" <?= ($user['country'] ?? '') === 'Australia' ? 'selected' : '' ?>>Australia</option>
                            <option value="Germany" <?= ($user['country'] ?? '') === 'Germany' ? 'selected' : '' ?>>Germany</option>
                            <option value="France" <?= ($user['country'] ?? '') === 'France' ? 'selected' : '' ?>>France</option>
                            <option value="Other" <?= ($user['country'] ?? '') === 'Other' ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="btn btn-primary">
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>