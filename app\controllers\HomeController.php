<?php

class HomeController extends BaseController
{
    public function __construct($pdo)
    {
        parent::__construct($pdo);
        SearchHelper::init($pdo);
    }

    public function index()
    {
        try {
            // Get featured products with caching
            $featuredProducts = CacheHelper::remember('featured_products', function () {
                $stmt = $this->pdo->prepare("
                    SELECT p.*, c.name as category_name, pi.image_path as primary_image
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                    WHERE p.is_active = 1 AND p.is_featured = 1
                    ORDER BY p.created_at DESC
                    LIMIT 8
                ");
                $stmt->execute();
                return $stmt->fetchAll();
            }, 600); // Cache for 10 minutes

            // Get categories with caching
            $categories = CacheHelper::getCategories();

            // Get latest products with caching
            $latestProducts = CacheHelper::remember('latest_products', function () {
                $stmt = $this->pdo->prepare("
                    SELECT p.*, c.name as category_name, pi.image_path as primary_image
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                    WHERE p.is_active = 1
                    ORDER BY p.created_at DESC
                    LIMIT 6
                ");
                $stmt->execute();
                return $stmt->fetchAll();
            }, 300); // Cache for 5 minutes

            // If no featured products, get some regular products instead
            if (empty($featuredProducts)) {
                $featuredProducts = CacheHelper::remember('fallback_products', function () {
                    $stmt = $this->pdo->prepare("
                        SELECT p.*, c.name as category_name, pi.image_path as primary_image
                        FROM products p
                        LEFT JOIN categories c ON p.category_id = c.id
                        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                        WHERE p.is_active = 1
                        ORDER BY RAND()
                        LIMIT 3
                    ");
                    $stmt->execute();
                    return $stmt->fetchAll();
                }, 600); // Cache for 10 minutes
            }

            $this->render('home/index', [
                'featuredProducts' => $featuredProducts,
                'categories' => $categories,
                'latestProducts' => $latestProducts
            ]);
        } catch (Exception $e) {
            $this->handleException($e, 'Unable to load homepage');
        }
    }

    public function products($category = null, $page = 1)
    {
        $page = max(1, (int)$page);
        $limit = ITEMS_PER_PAGE;
        $offset = ($page - 1) * $limit;

        $whereConditions = ['p.is_active = 1'];
        $params = [];

        // Filter by category
        if ($category) {
            $stmt = $this->pdo->prepare("SELECT id FROM categories WHERE slug = ?");
            $stmt->execute([$category]);
            $categoryData = $stmt->fetch();

            if ($categoryData) {
                $whereConditions[] = 'p.category_id = ?';
                $params[] = $categoryData['id'];
            }
        }

        // Search functionality
        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $search = '%' . $_GET['search'] . '%';
            $whereConditions[] = '(p.name LIKE ? OR p.description LIKE ?)';
            $params[] = $search;
            $params[] = $search;
        }

        // Price filter
        if (isset($_GET['min_price']) && !empty($_GET['min_price'])) {
            $whereConditions[] = 'p.price >= ?';
            $params[] = $_GET['min_price'];
        }

        if (isset($_GET['max_price']) && !empty($_GET['max_price'])) {
            $whereConditions[] = 'p.price <= ?';
            $params[] = $_GET['max_price'];
        }

        // In stock filter
        if (isset($_GET['in_stock']) && $_GET['in_stock'] === '1') {
            $whereConditions[] = 'p.stock_quantity > 0';
        }

        // On sale filter
        if (isset($_GET['on_sale']) && $_GET['on_sale'] === '1') {
            $whereConditions[] = 'p.sale_price IS NOT NULL AND p.sale_price > 0';
        }

        $whereClause = implode(' AND ', $whereConditions);

        // Determine sorting
        $sortBy = $_GET['sort'] ?? 'newest';
        $orderBy = 'p.created_at DESC'; // default

        switch ($sortBy) {
            case 'oldest':
                $orderBy = 'p.created_at ASC';
                break;
            case 'price-low':
                $orderBy = 'COALESCE(p.sale_price, p.price) ASC';
                break;
            case 'price-high':
                $orderBy = 'COALESCE(p.sale_price, p.price) DESC';
                break;
            case 'name-asc':
                $orderBy = 'p.name ASC';
                break;
            case 'name-desc':
                $orderBy = 'p.name DESC';
                break;
            case 'newest':
            default:
                $orderBy = 'p.created_at DESC';
                break;
        }

        // Get total count
        $countStmt = $this->pdo->prepare("
            SELECT COUNT(*) as total 
            FROM products p 
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $totalProducts = $countStmt->fetch()['total'];
        $totalPages = ceil($totalProducts / $limit);

        // Get products
        $stmt = $this->pdo->prepare("
            SELECT p.*, c.name as category_name, pi.image_path as primary_image
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
            WHERE $whereClause
            ORDER BY $orderBy
            LIMIT ? OFFSET ?
        ");

        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $products = $stmt->fetchAll();

        // Get categories for filter
        $stmt = $this->pdo->prepare("
            SELECT * FROM categories 
            WHERE is_active = 1 
            ORDER BY name ASC
        ");
        $stmt->execute();
        $categories = $stmt->fetchAll();

        $viewData = [
            'products' => $products,
            'categories' => $categories,
            'currentCategory' => $category,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalProducts' => $totalProducts,
            'search' => $_GET['search'] ?? '',
            'minPrice' => $_GET['min_price'] ?? '',
            'maxPrice' => $_GET['max_price'] ?? '',
            'inStock' => isset($_GET['in_stock']) && $_GET['in_stock'] === '1',
            'onSale' => isset($_GET['on_sale']) && $_GET['on_sale'] === '1',
            'sort' => $sortBy
        ];

        // AJAX: Only return the product grid and sidebar
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            ob_start();
            $this->renderPartial('products/_sidebar', $viewData);
            $sidebarHtml = ob_get_clean();
            ob_start();
            $this->renderPartial('products/_grid', $viewData);
            $gridHtml = ob_get_clean();
            header('Content-Type: application/json');
            echo json_encode(['sidebar' => $sidebarHtml, 'grid' => $gridHtml]);
            return;
        }

        $this->render('products/index', $viewData);
    }

    public function product($slug)
    {
        // Get product details with primary image
        $stmt = $this->pdo->prepare("
            SELECT p.*, c.name as category_name, c.slug as category_slug, pi.image_path as primary_image
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
            WHERE p.slug = ? AND p.is_active = 1
        ");
        $stmt->execute([$slug]);
        $product = $stmt->fetch();

        if (!$product) {
            http_response_code(404);
            require_once APP_PATH . '/views/errors/404.php';
            return;
        }

        // Get product images
        $stmt = $this->pdo->prepare("
            SELECT * FROM product_images 
            WHERE product_id = ? 
            ORDER BY is_primary DESC, sort_order ASC
        ");
        $stmt->execute([$product['id']]);
        $images = $stmt->fetchAll();

        // Get product reviews
        $stmt = $this->pdo->prepare("
            SELECT pr.*, u.first_name, u.last_name 
            FROM product_reviews pr 
            JOIN users u ON pr.user_id = u.id 
            WHERE pr.product_id = ? AND pr.is_approved = 1 
            ORDER BY pr.created_at DESC
        ");
        $stmt->execute([$product['id']]);
        $reviews = $stmt->fetchAll();

        // Calculate average rating
        $avgRating = 0;
        if (!empty($reviews)) {
            $totalRating = array_sum(array_column($reviews, 'rating'));
            $avgRating = round($totalRating / count($reviews), 1);
        }

        // Get related products with images
        $stmt = $this->pdo->prepare("
            SELECT p.*, c.name as category_name, pi.image_path as primary_image
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
            WHERE p.category_id = ? AND p.id != ? AND p.is_active = 1 
            ORDER BY RAND() 
            LIMIT 4
        ");
        $stmt->execute([$product['category_id'], $product['id']]);
        $relatedProducts = $stmt->fetchAll();

        // Check if product is in user's wishlist
        $inWishlist = false;
        if ($this->user) {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count 
                FROM wishlist_items 
                WHERE user_id = ? AND product_id = ?
            ");
            $stmt->execute([$this->user['id'], $product['id']]);
            $inWishlist = $stmt->fetch()['count'] > 0;
        }

        $this->render('products/detail', [
            'product' => $product,
            'images' => $images,
            'reviews' => $reviews,
            'avgRating' => $avgRating,
            'relatedProducts' => $relatedProducts,
            'inWishlist' => $inWishlist
        ]);
    }

    public function about()
    {
        $this->render('pages/about');
    }

    public function contact()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $this->sanitizeInput($_POST['name'] ?? '');
            $email = $this->sanitizeInput($_POST['email'] ?? '');
            $subject = $this->sanitizeInput($_POST['subject'] ?? '');
            $message = $this->sanitizeInput($_POST['message'] ?? '');

            if (empty($name) || empty($email) || empty($subject) || empty($message)) {
                $error = 'All fields are required.';
            } elseif (!$this->validateEmail($email)) {
                $error = 'Please enter a valid email address.';
            } else {
                // Send contact email
                $body = "Name: $name\nEmail: $email\nSubject: $subject\n\nMessage:\n$message";
                if ($this->sendEmail('<EMAIL>', "Contact Form: $subject", $body)) {
                    $success = 'Thank you for your message. We will get back to you soon!';
                } else {
                    $error = 'Sorry, there was an error sending your message. Please try again.';
                }
            }
        }

        $this->render('pages/contact', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function productDetail($id)
    {
        $this->product($id);
    }

    public function categoryProducts($categoryId)
    {
        $stmt = $this->pdo->prepare("SELECT slug FROM categories WHERE id = ?");
        $stmt->execute([$categoryId]);
        $category = $stmt->fetch();

        if ($category) {
            $this->products($category['slug']);
        } else {
            $this->products();
        }
    }

    public function search()
    {
        $query = $this->sanitizeInput($_GET['q'] ?? $_POST['search'] ?? '');
        $categoryId = (int)($_GET['category'] ?? 0);
        $minPrice = (float)($_GET['min_price'] ?? 0);
        $maxPrice = (float)($_GET['max_price'] ?? 0);
        $sortBy = $_GET['sort'] ?? 'created_at';
        $page = max(1, (int)($_GET['page'] ?? 1));

        $filters = [
            'page' => $page,
            'sort_by' => $sortBy,
            'limit' => ITEMS_PER_PAGE
        ];

        if ($categoryId > 0) {
            $filters['category_id'] = $categoryId;
        }

        if ($minPrice > 0) {
            $filters['min_price'] = $minPrice;
        }

        if ($maxPrice > 0) {
            $filters['max_price'] = $maxPrice;
        }

        try {
            $searchResults = SearchHelper::searchProducts($query, $filters);

            // Log search for analytics
            SearchHelper::logSearch($query, $searchResults['total'], $this->user['id'] ?? null);

            // Get filter options for the search form
            $filterOptions = SearchHelper::getFilterOptions();

            $this->render('products/index', [
                'products' => $searchResults['products'],
                'query' => $query,
                'currentPage' => $searchResults['page'],
                'totalPages' => $searchResults['total_pages'],
                'totalProducts' => $searchResults['total'],
                'filterOptions' => $filterOptions,
                'currentFilters' => [
                    'category_id' => $categoryId,
                    'min_price' => $minPrice,
                    'max_price' => $maxPrice,
                    'sort_by' => $sortBy
                ]
            ]);
        } catch (Exception $e) {
            $this->handleException($e, 'Search failed');
        }
    }

    public function wishlist()
    {
        $this->requireAuth();

        $stmt = $this->pdo->prepare("
            SELECT p.*, c.name as category_name, pi.image_path as primary_image
            FROM wishlist_items wi 
            JOIN products p ON wi.product_id = p.id 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
            WHERE wi.user_id = ? AND p.is_active = 1
            ORDER BY wi.created_at DESC
        ");
        $stmt->execute([$this->user['id']]);
        $wishlistItems = $stmt->fetchAll();

        // Get recommended products based on wishlist categories
        $recommendedProducts = [];
        if (!empty($wishlistItems)) {
            $categoryIds = array_unique(array_filter(array_column($wishlistItems, 'category_id')));
            $wishlistProductIds = array_column($wishlistItems, 'id');

            if (!empty($categoryIds)) {
                $placeholders = str_repeat('?,', count($categoryIds) - 1) . '?';
                $productPlaceholders = str_repeat('?,', count($wishlistProductIds) - 1) . '?';

                $stmt = $this->pdo->prepare("
                    SELECT p.*, c.name as category_name, pi.image_path as primary_image
                    FROM products p 
                    LEFT JOIN categories c ON p.category_id = c.id 
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                    WHERE p.category_id IN ($placeholders) 
                    AND p.id NOT IN ($productPlaceholders)
                    AND p.is_active = 1
                    ORDER BY RAND()
                    LIMIT 3
                ");

                $params = array_merge($categoryIds, $wishlistProductIds);
                $stmt->execute($params);
                $recommendedProducts = $stmt->fetchAll();
            }
        }

        // If no recommended products from categories, get some featured products
        if (empty($recommendedProducts)) {
            $stmt = $this->pdo->prepare("
                SELECT p.*, c.name as category_name, pi.image_path as primary_image
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                WHERE p.is_active = 1 AND p.is_featured = 1
                ORDER BY RAND()
                LIMIT 3
            ");
            $stmt->execute();
            $recommendedProducts = $stmt->fetchAll();
        }

        $this->render('wishlist/index', [
            'wishlistItems' => $wishlistItems,
            'recommendedProducts' => $recommendedProducts
        ]);
    }

    public function addToWishlist()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $productId = (int)($_POST['product_id'] ?? 0);

        if (!$productId) {
            $this->json(['success' => false, 'message' => 'Invalid product']);
            return;
        }

        // Check if already in wishlist
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM wishlist_items WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$this->user['id'], $productId]);

        if ($stmt->fetchColumn() > 0) {
            $this->json(['success' => false, 'message' => 'Product already in wishlist']);
            return;
        }

        // Add to wishlist
        $stmt = $this->pdo->prepare("INSERT INTO wishlist_items (user_id, product_id) VALUES (?, ?)");
        if ($stmt->execute([$this->user['id'], $productId])) {
            $this->json(['success' => true, 'message' => 'Added to wishlist']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to add to wishlist']);
        }
    }

    public function removeFromWishlist()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $productId = (int)($_POST['product_id'] ?? 0);

        if (!$productId) {
            $this->json(['success' => false, 'message' => 'Invalid product']);
            return;
        }

        $stmt = $this->pdo->prepare("DELETE FROM wishlist_items WHERE user_id = ? AND product_id = ?");
        if ($stmt->execute([$this->user['id'], $productId])) {
            $this->json(['success' => true, 'message' => 'Removed from wishlist']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to remove from wishlist']);
        }
    }

    public function clearWishlist()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $stmt = $this->pdo->prepare("DELETE FROM wishlist_items WHERE user_id = ?");
        if ($stmt->execute([$this->user['id']])) {
            $this->json(['success' => true, 'message' => 'Wishlist cleared successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to clear wishlist']);
        }
    }

    public function addReview()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $productId = (int)($_POST['product_id'] ?? 0);
        $rating = (int)($_POST['rating'] ?? 0);
        $comment = $this->sanitizeInput($_POST['comment'] ?? '');

        if (!$productId || $rating < 1 || $rating > 5) {
            $this->json(['success' => false, 'message' => 'Invalid data']);
            return;
        }

        // Check if user already reviewed this product
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM product_reviews WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$this->user['id'], $productId]);

        if ($stmt->fetchColumn() > 0) {
            $this->json(['success' => false, 'message' => 'You have already reviewed this product']);
            return;
        }

        $stmt = $this->pdo->prepare("INSERT INTO product_reviews (product_id, user_id, rating, comment) VALUES (?, ?, ?, ?)");
        if ($stmt->execute([$productId, $this->user['id'], $rating, $comment])) {
            $this->json(['success' => true, 'message' => 'Review added successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to add review']);
        }
    }

    public function updateReview()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $reviewId = (int)($_POST['review_id'] ?? 0);
        $rating = (int)($_POST['rating'] ?? 0);
        $comment = $this->sanitizeInput($_POST['comment'] ?? '');

        if (!$reviewId || $rating < 1 || $rating > 5) {
            $this->json(['success' => false, 'message' => 'Invalid data']);
            return;
        }

        $stmt = $this->pdo->prepare("UPDATE product_reviews SET rating = ?, comment = ? WHERE id = ? AND user_id = ?");
        if ($stmt->execute([$rating, $comment, $reviewId, $this->user['id']])) {
            $this->json(['success' => true, 'message' => 'Review updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update review']);
        }
    }

    public function deleteReview()
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $reviewId = (int)($_POST['review_id'] ?? 0);

        if (!$reviewId) {
            $this->json(['success' => false, 'message' => 'Invalid review']);
            return;
        }

        $stmt = $this->pdo->prepare("DELETE FROM product_reviews WHERE id = ? AND user_id = ?");
        if ($stmt->execute([$reviewId, $this->user['id']])) {
            $this->json(['success' => true, 'message' => 'Review deleted successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to delete review']);
        }
    }

    public function subscribeNewsletter()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $email = $this->sanitizeInput($_POST['email'] ?? '');

        if (empty($email) || !$this->validateEmail($email)) {
            $this->json(['success' => false, 'message' => 'Please enter a valid email address']);
            return;
        }

        // Check if already subscribed
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM newsletter_subscribers WHERE email = ?");
        $stmt->execute([$email]);

        if ($stmt->fetchColumn() > 0) {
            $this->json(['success' => false, 'message' => 'Email already subscribed']);
            return;
        }

        $stmt = $this->pdo->prepare("INSERT INTO newsletter_subscribers (email, created_at) VALUES (?, NOW())");
        if ($stmt->execute([$email])) {
            $this->json(['success' => true, 'message' => 'Successfully subscribed to newsletter']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to subscribe']);
        }
    }

    public function unsubscribeNewsletter()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->json(['success' => false, 'message' => 'Invalid request']);
            return;
        }

        $email = $this->sanitizeInput($_POST['email'] ?? '');

        if (empty($email)) {
            $this->json(['success' => false, 'message' => 'Email is required']);
            return;
        }

        $stmt = $this->pdo->prepare("DELETE FROM newsletter_subscribers WHERE email = ?");
        if ($stmt->execute([$email])) {
            $this->json(['success' => true, 'message' => 'Successfully unsubscribed']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to unsubscribe']);
        }
    }

    public function sendContact()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('contact');
            return;
        }

        $firstName = $this->sanitizeInput($_POST['first_name'] ?? '');
        $lastName = $this->sanitizeInput($_POST['last_name'] ?? '');
        $name = trim($firstName . ' ' . $lastName);
        $email = $this->sanitizeInput($_POST['email'] ?? '');
        $subject = $this->sanitizeInput($_POST['subject'] ?? '');
        $message = $this->sanitizeInput($_POST['message'] ?? '');

        if (empty($firstName) || empty($lastName) || empty($email) || empty($subject) || empty($message)) {
            $error = 'All fields are required.';
        } elseif (!$this->validateEmail($email)) {
            $error = 'Please enter a valid email address.';
        } else {
            // Save contact request
            $stmt = $this->pdo->prepare("
                INSERT INTO contact_requests (name, email, subject, message, status, created_at)
                VALUES (?, ?, ?, ?, 'new', NOW())
            ");

            if ($stmt->execute([$name, $email, $subject, $message])) {
                // Optionally send email notification
                $adminEmail = '<EMAIL>';
                $emailBody = "New contact request from $name ($email):\n\nSubject: $subject\n\nMessage:\n$message";
                $this->sendEmail($adminEmail, 'New Contact Request', $emailBody);

                $success = 'Message sent successfully. We will get back to you soon.';
            } else {
                $error = 'Failed to send message. Please try again.';
            }
        }

        $this->render('pages/contact', [
            'error' => $error ?? null,
            'success' => $success ?? null,
            'formData' => [
                'first_name' => $firstName ?? '',
                'last_name' => $lastName ?? '',
                'email' => $email ?? '',
                'subject' => $subject ?? '',
                'message' => $message ?? ''
            ]
        ]);
    }

    public function support()
    {
        $this->render('pages/support');
    }

    public function sendSupport()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('support');
            return;
        }

        $name = $this->sanitizeInput($_POST['name'] ?? '');
        $email = $this->sanitizeInput($_POST['email'] ?? '');
        $issue = $this->sanitizeInput($_POST['issue'] ?? '');
        $description = $this->sanitizeInput($_POST['description'] ?? '');

        if (empty($name) || empty($email) || empty($issue) || empty($description)) {
            $error = 'All fields are required.';
        } elseif (!$this->validateEmail($email)) {
            $error = 'Please enter a valid email address.';
        } else {
            $stmt = $this->pdo->prepare("
                INSERT INTO support_tickets (name, email, issue, description, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");

            if ($stmt->execute([$name, $email, $issue, $description])) {
                $success = 'Support ticket created successfully. We will respond within 24 hours.';
            } else {
                $error = 'Failed to create support ticket. Please try again.';
            }
        }

        $this->render('pages/support', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function privacyPolicy()
    {
        $this->render('pages/privacy-policy');
    }

    public function termsOfService()
    {
        $this->render('pages/terms-of-service');
    }

    public function shippingPolicy()
    {
        $this->render('pages/shipping-policy');
    }

    public function returnPolicy()
    {
        $this->render('pages/return-policy');
    }

    public function helpCenter()
    {
        $this->render('pages/help-center');
    }

    public function shippingInfo()
    {
        $this->render('pages/shipping-info');
    }

    public function returns()
    {
        $this->render('pages/returns');
    }

    public function sitemap()
    {
        header('Content-Type: application/xml; charset=utf-8');

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Home page
        $xml .= '  <url>' . "\n";
        $xml .= '    <loc>' . APP_URL . '/</loc>' . "\n";
        $xml .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
        $xml .= '    <changefreq>daily</changefreq>' . "\n";
        $xml .= '    <priority>1.0</priority>' . "\n";
        $xml .= '  </url>' . "\n";

        // Products page
        $xml .= '  <url>' . "\n";
        $xml .= '    <loc>' . APP_URL . '/products</loc>' . "\n";
        $xml .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
        $xml .= '    <changefreq>daily</changefreq>' . "\n";
        $xml .= '    <priority>0.8</priority>' . "\n";
        $xml .= '  </url>' . "\n";

        // Individual products
        $stmt = $this->pdo->query("SELECT id, updated_at FROM products WHERE is_active = 1");
        while ($product = $stmt->fetch()) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . APP_URL . '/product/' . $product['id'] . '</loc>' . "\n";
            $xml .= '    <lastmod>' . date('Y-m-d', strtotime($product['updated_at'])) . '</lastmod>' . "\n";
            $xml .= '    <changefreq>weekly</changefreq>' . "\n";
            $xml .= '    <priority>0.6</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        // Categories
        $stmt = $this->pdo->query("SELECT id FROM categories WHERE is_active = 1");
        while ($category = $stmt->fetch()) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . APP_URL . '/category/' . $category['id'] . '</loc>' . "\n";
            $xml .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
            $xml .= '    <changefreq>weekly</changefreq>' . "\n";
            $xml .= '    <priority>0.5</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        // Static pages
        $staticPages = ['about', 'contact', 'privacy-policy', 'terms-of-service', 'shipping-policy', 'return-policy'];
        foreach ($staticPages as $page) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . APP_URL . '/' . $page . '</loc>' . "\n";
            $xml .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
            $xml .= '    <changefreq>monthly</changefreq>' . "\n";
            $xml .= '    <priority>0.3</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        $xml .= '</urlset>';

        echo $xml;
        exit;
    }

    public function robots()
    {
        header('Content-Type: text/plain');

        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /api/\n";
        $robots .= "Disallow: /webhook/\n";
        $robots .= "Disallow: /cart\n";
        $robots .= "Disallow: /checkout\n";
        $robots .= "Disallow: /account\n";
        $robots .= "Disallow: /wishlist\n";
        $robots .= "Disallow: /login\n";
        $robots .= "Disallow: /register\n";
        $robots .= "Disallow: /profile\n";
        $robots .= "\n";
        $robots .= "Sitemap: " . APP_URL . "/sitemap.xml\n";

        echo $robots;
        exit;
    }

    public function getCategories()
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM categories 
                WHERE is_active = 1 
                ORDER BY sort_order ASC, name ASC
            ");
            $stmt->execute();
            $categories = $stmt->fetchAll();

            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'categories' => $categories]);
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Failed to load categories']);
        }
    }
}
