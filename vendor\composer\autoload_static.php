<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitd8092bd63a074f74c213dc1a4b89e1a9
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            '<PERSON><PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'P<PERSON><PERSON>ailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitd8092bd63a074f74c213dc1a4b89e1a9::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitd8092bd63a074f74c213dc1a4b89e1a9::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitd8092bd63a074f74c213dc1a4b89e1a9::$classMap;

        }, null, ClassLoader::class);
    }
}
