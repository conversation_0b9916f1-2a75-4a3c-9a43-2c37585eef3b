/**
 * Modern 3D Interactions and Animations
 */

class ModernInteractions {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollReveal();
        this.setupParallax();
        this.setup3DCardEffects();
        this.setupRippleEffect();
        this.setupNotifications();
        this.setupLoadingStates();
        this.setupModalSystem();
    }

    // Scroll Reveal Animation
    setupScrollReveal() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    }

    // Parallax Effect
    setupParallax() {
        let ticking = false;

        const updateParallax = () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax');

            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });

            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick);
    }

    // 3D Card Effects
    setup3DCardEffects() {
        document.querySelectorAll('.hover-tilt').forEach(card => {
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
            });
        });
    }

    // Ripple Effect
    setupRippleEffect() {
        document.querySelectorAll('.ripple').forEach(button => {
            button.addEventListener('click', (e) => {
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                const ripple = document.createElement('span');
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple-animation 0.6s linear;
                    pointer-events: none;
                `;
                
                button.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Add ripple animation CSS
        if (!document.getElementById('ripple-styles')) {
            const style = document.createElement('style');
            style.id = 'ripple-styles';
            style.textContent = `
                @keyframes ripple-animation {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Notification System
    setupNotifications() {
        window.showNotification = (message, type = 'info', duration = 5000) => {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Auto hide
            const hideTimeout = setTimeout(() => {
                this.hideNotification(notification);
            }, duration);

            // Manual close
            notification.querySelector('.notification-close').addEventListener('click', () => {
                clearTimeout(hideTimeout);
                this.hideNotification(notification);
            });
        };
    }

    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // Loading States
    setupLoadingStates() {
        window.showLoading = (element, text = 'Loading...') => {
            const loader = document.createElement('div');
            loader.className = 'loading-overlay';
            loader.innerHTML = `
                <div class="loading-content">
                    <div class="loading-3d"></div>
                    <span class="loading-text">${text}</span>
                </div>
            `;

            element.style.position = 'relative';
            element.appendChild(loader);
        };

        window.hideLoading = (element) => {
            const loader = element.querySelector('.loading-overlay');
            if (loader) {
                loader.remove();
            }
        };
    }

    // Modal System
    setupModalSystem() {
        // Open modal
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal]')) {
                e.preventDefault();
                const modalId = e.target.getAttribute('data-modal');
                this.openModal(modalId);
            }
        });

        // Close modal
        document.addEventListener('click', (e) => {
            if (e.target.matches('.modal-overlay') || e.target.matches('.modal-close')) {
                e.preventDefault();
                this.closeModal();
            }
        });

        // Close on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal() {
        const activeModal = document.querySelector('.modal-overlay.active');
        if (activeModal) {
            activeModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // Utility Methods
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Enhanced Cart Functionality with 3D Effects
class EnhancedCart {
    constructor() {
        this.setupCartAnimations();
        this.setupQuickActions();
    }

    setupCartAnimations() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-to-cart-quick') || e.target.closest('.add-to-cart-quick')) {
                e.preventDefault();
                const button = e.target.matches('.add-to-cart-quick') ? e.target : e.target.closest('.add-to-cart-quick');
                this.animateAddToCart(button);
            }
        });
    }

    animateAddToCart(button) {
        const productId = button.getAttribute('data-product-id');
        
        // Show loading state
        const originalContent = button.innerHTML;
        button.innerHTML = '<div class="spinner"></div>';
        button.disabled = true;

        // Simulate API call
        setTimeout(() => {
            // Success animation
            button.innerHTML = '<i class="fas fa-check"></i> Added!';
            button.classList.add('success');

            // Update cart count with animation
            this.updateCartCount();

            // Reset button after delay
            setTimeout(() => {
                button.innerHTML = originalContent;
                button.disabled = false;
                button.classList.remove('success');
            }, 2000);

            // Show notification
            window.showNotification('Product added to cart!', 'success');
        }, 1000);
    }

    updateCartCount() {
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            const currentCount = parseInt(cartCount.textContent) || 0;
            cartCount.textContent = currentCount + 1;
            cartCount.classList.add('bounce');
            setTimeout(() => cartCount.classList.remove('bounce'), 300);
        }
    }

    setupQuickActions() {
        // Quick view functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quick-view') || e.target.closest('.quick-view')) {
                e.preventDefault();
                const button = e.target.matches('.quick-view') ? e.target : e.target.closest('.quick-view');
                const productId = button.getAttribute('data-product-id');
                this.showQuickView(productId);
            }
        });

        // Wishlist functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-to-wishlist') || e.target.closest('.add-to-wishlist')) {
                e.preventDefault();
                const button = e.target.matches('.add-to-wishlist') ? e.target : e.target.closest('.add-to-wishlist');
                this.toggleWishlist(button);
            }
        });
    }

    showQuickView(productId) {
        // Create and show quick view modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = 'quick-view-modal';
        modal.innerHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h3>Quick View</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="loading-3d"></div>
                    <p>Loading product details...</p>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        setTimeout(() => modal.classList.add('active'), 100);

        // Load product data (simulate API call)
        setTimeout(() => {
            modal.querySelector('.modal-body').innerHTML = `
                <div class="quick-view-content">
                    <div class="product-image">
                        <img src="/asma/public/assets/uploads/products/default.jpg" alt="Product">
                    </div>
                    <div class="product-details">
                        <h4>Product Name</h4>
                        <p class="price">$99.99</p>
                        <p class="description">Product description goes here...</p>
                        <button class="btn-3d btn-3d-primary">Add to Cart</button>
                    </div>
                </div>
            `;
        }, 1000);
    }

    toggleWishlist(button) {
        const icon = button.querySelector('i');
        const isInWishlist = icon.classList.contains('fas');

        if (isInWishlist) {
            icon.classList.remove('fas');
            icon.classList.add('far');
            window.showNotification('Removed from wishlist', 'info');
        } else {
            icon.classList.remove('far');
            icon.classList.add('fas');
            window.showNotification('Added to wishlist!', 'success');
        }

        button.classList.add('pulse-glow');
        setTimeout(() => button.classList.remove('pulse-glow'), 1000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernInteractions();
    new EnhancedCart();
    
    // Add loading overlay styles
    const loadingStyles = document.createElement('style');
    loadingStyles.textContent = `
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .loading-content {
            text-align: center;
            color: white;
        }
        
        .loading-text {
            display: block;
            margin-top: 1rem;
        }
        
        .bounce {
            animation: bounce 0.3s ease;
        }
        
        @keyframes bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        .btn.success {
            background: var(--success-color) !important;
            border-color: var(--success-color) !important;
        }
    `;
    document.head.appendChild(loadingStyles);
});
