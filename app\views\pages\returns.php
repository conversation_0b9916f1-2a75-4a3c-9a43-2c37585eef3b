<div class="returns-page">
    <div class="container">
        <!-- <PERSON> Header -->
        <div class="page-header">
            <h1>Returns & Refunds</h1>
            <p>Our hassle-free return policy ensures your complete satisfaction</p>
        </div>

        <!-- Return Policy Overview -->
        <div class="policy-overview">
            <div class="overview-cards">
                <div class="overview-card">
                    <i class="fas fa-calendar-alt"></i>
                    <h3>30-Day Returns</h3>
                    <p>Return most items within 30 days of delivery for a full refund</p>
                </div>

                <div class="overview-card">
                    <i class="fas fa-shipping-fast"></i>
                    <h3>Free Returns</h3>
                    <p>Free return shipping on all domestic returns</p>
                </div>

                <div class="overview-card">
                    <i class="fas fa-credit-card"></i>
                    <h3>Quick Refunds</h3>
                    <p>Refunds processed within 3-5 business days</p>
                </div>

                <div class="overview-card">
                    <i class="fas fa-shield-alt"></i>
                    <h3>Easy Process</h3>
                    <p>Simple online return process with tracking</p>
                </div>
            </div>
        </div>

        <!-- Return Process -->
        <div class="return-process">
            <h2>How to Return an Item</h2>
            <div class="process-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Start Your Return</h3>
                        <p>Log into your account and go to "My Orders" to initiate a return. Select the item you want to return and provide a reason.</p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Print Return Label</h3>
                        <p>We'll provide you with a prepaid return shipping label. Print it and attach it to your package.</p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Package Your Item</h3>
                        <p>Securely package your item in its original packaging with all accessories and documentation included.</p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Ship Your Return</h3>
                        <p>Drop off your package at any authorized shipping location. You can track your return online.</p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>Receive Your Refund</h3>
                        <p>Once we receive and inspect your return, we'll process your refund within 3-5 business days.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Return Requirements -->
        <div class="return-requirements">
            <h2>Return Requirements</h2>
            <div class="requirements-grid">
                <div class="requirement-card">
                    <div class="requirement-header">
                        <i class="fas fa-check-circle"></i>
                        <h3>What's Required</h3>
                    </div>
                    <ul>
                        <li>Item must be in original condition</li>
                        <li>Original packaging included</li>
                        <li>All accessories and documentation</li>
                        <li>Return within 30 days of delivery</li>
                        <li>Valid return reason provided</li>
                    </ul>
                </div>

                <div class="requirement-card">
                    <div class="requirement-header">
                        <i class="fas fa-times-circle"></i>
                        <h3>What's Not Accepted</h3>
                    </div>
                    <ul>
                        <li>Items showing signs of use or wear</li>
                        <li>Missing original packaging</li>
                        <li>Damaged or defective items (contact support)</li>
                        <li>Personalized or custom items</li>
                        <li>Items purchased on clearance</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Refund Information -->
        <div class="refund-info">
            <h2>Refund Information</h2>
            <div class="refund-details">
                <div class="refund-card">
                    <h3>Refund Timeline</h3>
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h4>Return Received</h4>
                                <p>We'll email you when we receive your return</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h4>Inspection (1-2 days)</h4>
                                <p>We inspect the item to ensure it meets return requirements</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h4>Refund Processed (3-5 days)</h4>
                                <p>Refund is processed and sent to your original payment method</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h4>Refund Received</h4>
                                <p>Refund appears in your account (timing varies by bank)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="refund-methods">
                    <h3>Refund Methods</h3>
                    <div class="method-list">
                        <div class="method-item">
                            <i class="fas fa-credit-card"></i>
                            <div>
                                <h4>Credit/Debit Cards</h4>
                                <p>Refunds are processed to your original payment method</p>
                            </div>
                        </div>
                        <div class="method-item">
                            <i class="fab fa-paypal"></i>
                            <div>
                                <h4>PayPal</h4>
                                <p>Refunds are sent directly to your PayPal account</p>
                            </div>
                        </div>
                        <div class="method-item">
                            <i class="fas fa-gift"></i>
                            <div>
                                <h4>Store Credit</h4>
                                <p>You can choose to receive store credit instead of a refund</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exchanges -->
        <div class="exchanges">
            <h2>Exchanges</h2>
            <div class="exchange-content">
                <div class="exchange-info">
                    <h3>Need a Different Size or Color?</h3>
                    <p>We make exchanges easy! If you need a different size, color, or style, you can exchange your item for something else.</p>

                    <div class="exchange-steps">
                        <h4>Exchange Process:</h4>
                        <ol>
                            <li>Start a return for the item you want to exchange</li>
                            <li>Select "Exchange" as your return reason</li>
                            <li>Choose the new item you want</li>
                            <li>We'll ship your new item once we receive the return</li>
                        </ol>
                    </div>
                </div>

                <div class="exchange-benefits">
                    <h3>Exchange Benefits</h3>
                    <ul>
                        <li>No additional shipping costs for exchanges</li>
                        <li>Priority processing for exchange orders</li>
                        <li>Same 30-day window applies</li>
                        <li>Available for most items</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Damaged or Defective Items -->
        <div class="damaged-items">
            <h2>Damaged or Defective Items</h2>
            <div class="damaged-content">
                <div class="damaged-info">
                    <h3>What to Do</h3>
                    <p>If you receive a damaged or defective item, please contact our customer service team immediately. We'll help you get a replacement or refund right away.</p>

                    <div class="damaged-steps">
                        <h4>For Damaged Items:</h4>
                        <ul>
                            <li>Take photos of the damage</li>
                            <li>Contact us within 48 hours of delivery</li>
                            <li>We'll provide a prepaid return label</li>
                            <li>We'll ship a replacement immediately</li>
                        </ul>
                    </div>
                </div>

                <div class="damaged-contact">
                    <h3>Contact Us</h3>
                    <p>For damaged or defective items, please contact us:</p>
                    <div class="contact-methods">
                        <a href="<?= UrlHelper::url('/contact') ?>" class="btn btn-primary">
                            <i class="fas fa-envelope"></i>
                            Contact Support
                        </a>
                        <a href="tel:+15551234567" class="btn btn-outline">
                            <i class="fas fa-phone"></i>
                            Call Us
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="returns-faq">
            <h2>Frequently Asked Questions</h2>
            <div class="faq-list">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Can I return an item after 30 days?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Our standard return policy is 30 days. However, we may accept returns after this period on a case-by-case basis. Please contact our customer service team for assistance.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Do I need to pay for return shipping?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>No! We provide free return shipping for all domestic returns. International customers may be responsible for return shipping costs.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How long does it take to get my refund?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>We process refunds within 3-5 business days after receiving your return. The time to appear in your account depends on your bank or credit card provider.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Can I return a gift?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Yes! Gift returns are accepted within 30 days of delivery. The refund will be issued as store credit to the original purchaser's account.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What if my item is lost in transit?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>If your return is lost in transit, we'll work with the shipping carrier to locate it. If it cannot be found, we'll process your refund once we confirm the loss.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="contact-section">
            <div class="contact-card">
                <h2>Need Help with Your Return?</h2>
                <p>Our customer service team is here to help with any questions about returns or refunds.</p>
                <div class="contact-options">
                    <a href="<?= UrlHelper::url('/contact') ?>" class="btn btn-primary">
                        <i class="fas fa-envelope"></i>
                        Contact Us
                    </a>
                    <a href="<?= UrlHelper::url('/help-center') ?>" class="btn btn-outline">
                        <i class="fas fa-question-circle"></i>
                        Help Center
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .returns-page {
        padding: var(--spacing-xl) 0;
        background-color: var(--dark-surface);
        min-height: 100vh;
    }

    .page-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .page-header h1 {
        font-size: var(--font-size-4xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-header p {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Section Headers */
    .returns-page h2 {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xl);
        text-align: center;
    }

    /* Policy Overview */
    .policy-overview {
        margin-bottom: var(--spacing-3xl);
    }

    .overview-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .overview-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-align: center;
        transition: all var(--transition-fast);
    }

    .overview-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .overview-card i {
        font-size: var(--font-size-3xl);
        color: var(--primary-purple);
        margin-bottom: var(--spacing-md);
    }

    .overview-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .overview-card p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Return Process */
    .return-process {
        margin-bottom: var(--spacing-3xl);
    }

    .process-steps {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .step {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        transition: all var(--transition-fast);
    }

    .step:hover {
        transform: translateX(4px);
        box-shadow: var(--shadow-lg);
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-lg);
        font-weight: 700;
        flex-shrink: 0;
    }

    .step-content h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .step-content p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Return Requirements */
    .return-requirements {
        margin-bottom: var(--spacing-3xl);
    }

    .requirements-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }

    .requirement-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        transition: all var(--transition-fast);
    }

    .requirement-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .requirement-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .requirement-header i {
        font-size: var(--font-size-xl);
    }

    .requirement-header i.fa-check-circle {
        color: var(--success-color);
    }

    .requirement-header i.fa-times-circle {
        color: var(--error-color);
    }

    .requirement-header h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .requirement-card ul {
        list-style: none;
        padding: 0;
    }

    .requirement-card li {
        color: var(--text-secondary);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        padding-left: var(--spacing-lg);
    }

    .requirement-card li::before {
        content: '•';
        position: absolute;
        left: 0;
        color: var(--primary-purple);
        font-weight: bold;
    }

    .requirement-card li:last-child {
        border-bottom: none;
    }

    /* Refund Information */
    .refund-info {
        margin-bottom: var(--spacing-3xl);
    }

    .refund-details {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--spacing-2xl);
    }

    .refund-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .refund-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .timeline {
        position: relative;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: var(--border-color);
    }

    .timeline-item {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
        position: relative;
    }

    .timeline-dot {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        flex-shrink: 0;
        z-index: 1;
    }

    .timeline-content h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .timeline-content p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin: 0;
    }

    .refund-methods {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .refund-methods h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .method-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .method-item {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background-color: var(--dark-surface);
        border-radius: var(--radius-md);
    }

    .method-item i {
        font-size: var(--font-size-lg);
        color: var(--primary-purple);
        margin-top: 2px;
    }

    .method-item h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .method-item p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin: 0;
    }

    /* Exchanges */
    .exchanges {
        margin-bottom: var(--spacing-3xl);
    }

    .exchange-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
    }

    .exchange-info h3,
    .exchange-benefits h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .exchange-info p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: var(--spacing-lg);
    }

    .exchange-steps h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .exchange-steps ol {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-left: var(--spacing-lg);
    }

    .exchange-steps li {
        margin-bottom: var(--spacing-sm);
    }

    .exchange-benefits ul {
        list-style: none;
        padding: 0;
    }

    .exchange-benefits li {
        color: var(--text-secondary);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        padding-left: var(--spacing-lg);
    }

    .exchange-benefits li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: var(--success-color);
        font-weight: bold;
    }

    .exchange-benefits li:last-child {
        border-bottom: none;
    }

    /* Damaged Items */
    .damaged-items {
        margin-bottom: var(--spacing-3xl);
    }

    .damaged-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--spacing-2xl);
    }

    .damaged-info h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .damaged-info p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: var(--spacing-lg);
    }

    .damaged-steps h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .damaged-steps ul {
        list-style: none;
        padding: 0;
    }

    .damaged-steps li {
        color: var(--text-secondary);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        padding-left: var(--spacing-lg);
    }

    .damaged-steps li::before {
        content: '•';
        position: absolute;
        left: 0;
        color: var(--primary-purple);
        font-weight: bold;
    }

    .damaged-steps li:last-child {
        border-bottom: none;
    }

    .damaged-contact {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
    }

    .damaged-contact h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .damaged-contact p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    .contact-methods {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    /* FAQ Section */
    .returns-faq {
        margin-bottom: var(--spacing-3xl);
    }

    .faq-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .faq-item {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
        transition: all var(--transition-fast);
    }

    .faq-item:hover {
        box-shadow: var(--shadow-lg);
    }

    .faq-question {
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        transition: background-color var(--transition-fast);
    }

    .faq-question:hover {
        background-color: var(--dark-surface);
    }

    .faq-question h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .faq-question i {
        color: var(--text-secondary);
        transition: transform var(--transition-fast);
    }

    .faq-item.active .faq-question i {
        transform: rotate(180deg);
    }

    .faq-answer {
        padding: 0 var(--spacing-lg);
        max-height: 0;
        overflow: hidden;
        transition: all var(--transition-fast);
    }

    .faq-item.active .faq-answer {
        padding: 0 var(--spacing-lg) var(--spacing-lg);
        max-height: 500px;
    }

    .faq-answer p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Contact Section */
    .contact-section {
        text-align: center;
    }

    .contact-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        max-width: 600px;
        margin: 0 auto;
    }

    .contact-card h2 {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .contact-card p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
    }

    .contact-options {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background-color: var(--dark-surface);
        border-color: var(--primary-purple);
        color: var(--primary-purple);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {

        .refund-details,
        .exchange-content,
        .damaged-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }
    }

    @media (max-width: 768px) {
        .returns-page {
            padding: var(--spacing-lg) 0;
        }

        .page-header h1 {
            font-size: var(--font-size-3xl);
        }

        .overview-cards,
        .requirements-grid {
            grid-template-columns: 1fr;
        }

        .contact-options {
            flex-direction: column;
        }

        .step {
            flex-direction: column;
            text-align: center;
        }

        .step-number {
            align-self: center;
        }
    }

    /* Light Mode Support */
    .light-mode .returns-page {
        background-color: var(--light-surface);
    }

    .light-mode .overview-card,
    .light-mode .step,
    .light-mode .requirement-card,
    .light-mode .refund-card,
    .light-mode .refund-methods,
    .light-mode .damaged-contact,
    .light-mode .faq-item,
    .light-mode .contact-card {
        background-color: var(--light-card);
        border-color: var(--light-border);
    }

    .light-mode .method-item {
        background-color: var(--light-surface);
    }

    .light-mode .btn-outline {
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .btn-outline:hover {
        background-color: var(--light-surface);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // FAQ Toggle Functionality
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            question.addEventListener('click', () => {
                const isActive = item.classList.contains('active');

                // Close all other FAQ items
                faqItems.forEach(otherItem => {
                    otherItem.classList.remove('active');
                });

                // Toggle current item
                if (!isActive) {
                    item.classList.add('active');
                }
            });
        });
    });
</script>