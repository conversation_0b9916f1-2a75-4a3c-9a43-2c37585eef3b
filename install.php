<?php

/**
 * Cleanance Lab Installation Script
 * Run this file to set up the database and initial configuration
 */

// Check if already installed
if (file_exists('config/installed.txt')) {
    die('Cleanance Lab is already installed. Remove config/installed.txt to reinstall.');
}

$errors = [];
$success = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dbHost = $_POST['db_host'] ?? 'localhost';
    $dbName = $_POST['db_name'] ?? '';
    $dbUser = $_POST['db_user'] ?? '';
    $dbPass = $_POST['db_pass'] ?? '';
    $adminEmail = $_POST['admin_email'] ?? '';
    $adminPassword = $_POST['admin_password'] ?? '';

    // Validate inputs
    if (empty($dbName) || empty($dbUser) || empty($adminEmail) || empty($adminPassword)) {
        $errors[] = 'All fields are required.';
    }

    if (strlen($adminPassword) < 6) {
        $errors[] = 'Admin password must be at least 6 characters long.';
    }

    if (!filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid admin email address.';
    }

    // Test database connection
    if (empty($errors)) {
        try {
            $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$dbName`");

            // Import schema
            $schema = file_get_contents('database/schema.sql');
            $pdo->exec($schema);

            // Update admin user
            $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET email = ?, password = ? WHERE username = 'admin'");
            $stmt->execute([$adminEmail, $hashedPassword]);

            // Create database config file
            $configContent = "<?php\n\nreturn [\n";
            $configContent .= "    'host' => '$dbHost',\n";
            $configContent .= "    'dbname' => '$dbName',\n";
            $configContent .= "    'username' => '$dbUser',\n";
            $configContent .= "    'password' => '$dbPass',\n";
            $configContent .= "    'charset' => 'utf8mb4',\n";
            $configContent .= "    'options' => [\n";
            $configContent .= "        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
            $configContent .= "        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
            $configContent .= "        PDO::ATTR_EMULATE_PREPARES => false,\n";
            $configContent .= "    ]\n";
            $configContent .= "];\n";

            file_put_contents('config/database.php', $configContent);

            // Create installed marker
            file_put_contents('config/installed.txt', date('Y-m-d H:i:s'));

            $success[] = 'Installation completed successfully!';
            $success[] = 'Admin email: ' . $adminEmail;
            $success[] = 'You can now delete this install.php file for security.';
        } catch (PDOException $e) {
            $errors[] = 'Database connection failed: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cleanance Lab Installation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-purple: #7E57C2;
            --primary-purple-dark: #5E35B1;
            --white: #FFFFFF;
            --dark-bg: #1a1a1a;
            --dark-card: #2d2d2d;
            --text-primary: #e0e0e0;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --success-color: #4CAF50;
            --error-color: #f44336;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .install-container {
            background-color: var(--dark-card);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 2rem;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .install-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .install-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-purple);
            margin-bottom: 1rem;
        }

        .install-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .install-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(76, 175, 80, 0.2);
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(244, 67, 54, 0.2);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            background-color: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-purple);
            box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary-purple);
            color: var(--white);
            width: 100%;
        }

        .btn-primary:hover {
            background-color: var(--primary-purple-dark);
            transform: translateY(-1px);
        }

        .requirements {
            background-color: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .requirements h4 {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.25rem 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .requirement-status {
            font-weight: 500;
        }

        .requirement-status.ok {
            color: var(--success-color);
        }

        .requirement-status.error {
            color: var(--error-color);
        }

        @media (max-width: 768px) {
            .install-container {
                padding: 1.5rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="install-container">
        <div class="install-header">
            <div class="install-logo">
                <i class="fas fa-flask"></i>
                <span>Cleanance Lab</span>
            </div>
            <h1 class="install-title">Installation</h1>
            <p class="install-subtitle">Set up your e-commerce website</p>
        </div>

        <?php if (!empty($success)): ?>
            <?php foreach ($success as $message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endforeach; ?>

            <div style="text-align: center; margin-top: 2rem;">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Go to Website
                </a>
            </div>
        <?php else: ?>
            <!-- System Requirements Check -->
            <div class="requirements">
                <h4>System Requirements</h4>
                <div class="requirement-item">
                    <span>PHP Version (>= 8.0)</span>
                    <span class="requirement-status <?= version_compare(PHP_VERSION, '8.0.0', '>=') ? 'ok' : 'error' ?>">
                        <?= PHP_VERSION ?>
                    </span>
                </div>
                <div class="requirement-item">
                    <span>PDO MySQL Extension</span>
                    <span class="requirement-status <?= extension_loaded('pdo_mysql') ? 'ok' : 'error' ?>">
                        <?= extension_loaded('pdo_mysql') ? 'Installed' : 'Missing' ?>
                    </span>
                </div>
                <div class="requirement-item">
                    <span>Config Directory Writable</span>
                    <span class="requirement-status <?= is_writable('config') ? 'ok' : 'error' ?>">
                        <?= is_writable('config') ? 'Writable' : 'Not Writable' ?>
                    </span>
                </div>
                <div class="requirement-item">
                    <span>Uploads Directory Writable</span>
                    <span class="requirement-status <?= is_writable('public/uploads') ? 'ok' : 'error' ?>">
                        <?= is_writable('public/uploads') ? 'Writable' : 'Not Writable' ?>
                    </span>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <?php foreach ($errors as $error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <form method="POST" action="">
                <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Database Configuration</h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="db_host" class="form-label">Database Host</label>
                        <input type="text" id="db_host" name="db_host" value="localhost" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="db_name" class="form-label">Database Name</label>
                        <input type="text" id="db_name" name="db_name" value="cleanance_lab" class="form-input" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="db_user" class="form-label">Database Username</label>
                        <input type="text" id="db_user" name="db_user" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="db_pass" class="form-label">Database Password</label>
                        <input type="password" id="db_pass" name="db_pass" class="form-input">
                    </div>
                </div>

                <h3 style="margin: 2rem 0 1rem; color: var(--text-primary);">Admin Account</h3>

                <div class="form-group">
                    <label for="admin_email" class="form-label">Admin Email</label>
                    <input type="email" id="admin_email" name="admin_email" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="admin_password" class="form-label">Admin Password</label>
                    <input type="password" id="admin_password" name="admin_password" class="form-input" required>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-cog"></i>
                    Install Cleanance Lab
                </button>
            </form>
        <?php endif; ?>
    </div>
</body>

</html>