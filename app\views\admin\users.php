<div class="admin-container">
    <div class="admin-header">
        <h1>User Management</h1>
        <div class="header-actions">
            <button class="btn btn-secondary" id="exportUsers">
                <i class="fas fa-download"></i> Export Users
            </button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-filters">
        <div class="filter-row">
            <div class="search-box">
                <input type="text" id="userSearch" placeholder="Search users by name, email, or ID..." class="form-input">
                <i class="fas fa-search"></i>
            </div>

            <div class="filter-group">
                <select id="roleFilter" class="form-select">
                    <option value="">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="user">User</option>
                </select>

                <select id="statusFilter" class="form-select">
                    <option value="">All Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>

                <select id="dateFilter" class="form-select">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="year">This Year</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="admin-table-container">
        <table class="admin-table">
            <thead>
                <tr>
                    <th width="60">ID</th>
                    <th width="80">Avatar</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Orders</th>
                    <th>Total Spent</th>
                    <th>Status</th>
                    <th>Joined</th>
                    <th width="150">Actions</th>
                </tr>
            </thead>
            <tbody id="usersTableBody">
                <?php if (!empty($users)): ?>
                    <?php foreach ($users as $user): ?>
                        <tr data-user-id="<?= $user['id'] ?>">
                            <td><?= $user['id'] ?></td>
                            <td>
                                <div class="user-avatar">
                                    <?php if ($user['avatar']): ?>
                                        <img src="<?= UrlHelper::asset('uploads/users/' . $user['avatar']) ?>"
                                            alt="<?= htmlspecialchars($user['first_name']) ?>">
                                    <?php else: ?>
                                        <div class="avatar-placeholder">
                                            <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="user-info">
                                    <strong><?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?></strong>
                                    <small><?= htmlspecialchars($user['phone'] ?? 'No phone') ?></small>
                                </div>
                            </td>
                            <td><?= htmlspecialchars($user['email']) ?></td>
                            <td>
                                <span class="role-badge <?= $user['is_admin'] ? 'admin' : 'user' ?>">
                                    <?= $user['is_admin'] ? 'Admin' : 'User' ?>
                                </span>
                            </td>
                            <td>
                                <span class="order-count">
                                    <?= $user['order_count'] ?? 0 ?> orders
                                </span>
                            </td>
                            <td>
                                <div class="total-spent">
                                    <strong>$<?= number_format($user['total_spent'] ?? 0, 2) ?></strong>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge <?= $user['is_active'] ? 'active' : 'inactive' ?>">
                                    <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td><?= date('M j, Y', strtotime($user['created_at'])) ?></td>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?= UrlHelper::url('/admin/users/' . $user['id']) ?>"
                                        class="btn btn-sm btn-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-secondary toggle-status"
                                        data-id="<?= $user['id'] ?>"
                                        data-status="<?= $user['is_active'] ?>"
                                        title="<?= $user['is_active'] ? 'Deactivate' : 'Activate' ?>">
                                        <i class="fas fa-<?= $user['is_active'] ? 'times' : 'check' ?>"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning toggle-role"
                                        data-id="<?= $user['id'] ?>"
                                        data-role="<?= $user['is_admin'] ? 'admin' : 'user' ?>"
                                        title="Toggle Role">
                                        <i class="fas fa-user-shield"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-user"
                                        data-id="<?= $user['id'] ?>"
                                        data-name="<?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>"
                                        title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="10" class="no-data">
                            <div class="empty-state">
                                <i class="fas fa-users"></i>
                                <h3>No Users Found</h3>
                                <p>Users will appear here once they register.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if (isset($pagination) && $pagination['totalPages'] > 1): ?>
        <div class="pagination">
            <?php if ($pagination['currentPage'] > 1): ?>
                <a href="?page=<?= $pagination['currentPage'] - 1 ?>" class="page-link">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            <?php endif; ?>

            <?php for ($i = max(1, $pagination['currentPage'] - 2); $i <= min($pagination['totalPages'], $pagination['currentPage'] + 2); $i++): ?>
                <a href="?page=<?= $i ?>" class="page-link <?= $i == $pagination['currentPage'] ? 'active' : '' ?>">
                    <?= $i ?>
                </a>
            <?php endfor; ?>

            <?php if ($pagination['currentPage'] < $pagination['totalPages']): ?>
                <a href="?page=<?= $pagination['currentPage'] + 1 ?>" class="page-link">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<style>
    .admin-container {
        padding: 2rem;
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .admin-header h1 {
        color: var(--text-primary);
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 1rem;
    }

    .admin-filters {
        background: var(--card-bg);
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .search-box {
        position: relative;
        flex: 1;
        min-width: 300px;
    }

    .search-box input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
    }

    .search-box i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }

    .filter-group {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .form-select {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
        min-width: 120px;
    }

    .admin-table-container {
        background: var(--card-bg);
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
    }

    .admin-table th,
    .admin-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--bg-secondary);
        font-weight: 600;
        color: var(--text-primary);
    }

    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
    }

    .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .avatar-placeholder {
        width: 100%;
        height: 100%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.2rem;
    }

    .user-info strong {
        display: block;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .user-info small {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .role-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .role-badge.admin {
        background: #d4edda;
        color: #155724;
    }

    .role-badge.user {
        background: #e2e3e5;
        color: #383d41;
    }

    .order-count {
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .total-spent strong {
        color: var(--text-primary);
        font-size: 1.1rem;
    }

    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .action-buttons {
        display: flex;
        gap: 0.25rem;
    }

    .btn-sm {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .btn-warning {
        background: #ffc107;
        color: #212529;
    }

    .btn-warning:hover {
        background: #e0a800;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-state h3 {
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .empty-state p {
        margin-bottom: 1.5rem;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 2rem;
    }

    .page-link {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        color: var(--text-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .page-link:hover {
        background: var(--bg-secondary);
    }

    .page-link.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    @media (max-width: 768px) {
        .admin-container {
            padding: 1rem;
        }

        .admin-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .filter-row {
            flex-direction: column;
            align-items: stretch;
        }

        .search-box {
            min-width: auto;
        }

        .filter-group {
            flex-direction: column;
        }

        .admin-table {
            font-size: 0.875rem;
        }

        .admin-table th,
        .admin-table td {
            padding: 0.5rem;
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.125rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('userSearch');
        const roleFilter = document.getElementById('roleFilter');
        const statusFilter = document.getElementById('statusFilter');
        const dateFilter = document.getElementById('dateFilter');

        function filterUsers() {
            const searchTerm = searchInput.value.toLowerCase();
            const role = roleFilter.value;
            const status = statusFilter.value;

            const rows = document.querySelectorAll('#usersTableBody tr');

            rows.forEach(row => {
                const name = row.querySelector('.user-info strong').textContent.toLowerCase();
                const email = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
                const userId = row.querySelector('td:nth-child(1)').textContent;
                const roleCell = row.querySelector('.role-badge').textContent.toLowerCase();
                const statusCell = row.querySelector('.status-badge').textContent;

                let show = true;

                // Search filter
                if (searchTerm && !name.includes(searchTerm) && !email.includes(searchTerm) && !userId.includes(searchTerm)) {
                    show = false;
                }

                // Role filter
                if (role && roleCell !== role) {
                    show = false;
                }

                // Status filter
                if (status) {
                    const isActive = statusCell === 'Active';
                    if ((status === '1' && !isActive) || (status === '0' && isActive)) {
                        show = false;
                    }
                }

                row.style.display = show ? '' : 'none';
            });
        }

        searchInput.addEventListener('input', filterUsers);
        roleFilter.addEventListener('change', filterUsers);
        statusFilter.addEventListener('change', filterUsers);
        dateFilter.addEventListener('change', filterUsers);

        // Toggle status
        document.querySelectorAll('.toggle-status').forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = this.dataset.id;
                const currentStatus = this.dataset.status === '1';
                const newStatus = !currentStatus;

                if (confirm(`Are you sure you want to ${newStatus ? 'activate' : 'deactivate'} this user?`)) {
                    fetch(`/admin/users/${userId}/toggle-status`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                status: newStatus
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('Failed to update user status');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred');
                        });
                }
            });
        });

        // Toggle role
        document.querySelectorAll('.toggle-role').forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = this.dataset.id;
                const currentRole = this.dataset.role;
                const newRole = currentRole === 'admin' ? 'user' : 'admin';

                if (confirm(`Are you sure you want to change this user's role to ${newRole}?`)) {
                    fetch(`/admin/users/${userId}/toggle-role`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                role: newRole
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('Failed to update user role');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred');
                        });
                }
            });
        });

        // Delete user
        document.querySelectorAll('.delete-user').forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = this.dataset.id;
                const userName = this.dataset.name;

                if (confirm(`Are you sure you want to delete the user "${userName}"? This action cannot be undone and will delete all their data.`)) {
                    fetch(`/admin/users/${userId}/delete`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('Failed to delete user');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred');
                        });
                }
            });
        });

        // Export users
        document.getElementById('exportUsers').addEventListener('click', function() {
            const params = new URLSearchParams();
            if (roleFilter.value) params.append('role', roleFilter.value);
            if (statusFilter.value) params.append('status', statusFilter.value);
            if (dateFilter.value) params.append('date', dateFilter.value);

            window.open(`/admin/users/export?${params.toString()}`, '_blank');
        });
    });
</script>