<?php
/**
 * Cleanance Lab Setup Script
 * Run this script to set up the application after installation
 */

// Prevent running in production
if (file_exists('.env') && !empty(getenv('APP_ENV')) && getenv('APP_ENV') === 'production') {
    die('Setup script cannot be run in production environment.');
}

require_once 'config/app.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cleanance Lab Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background: #7E57C2; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .section { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .btn { background: #7E57C2; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #6a4c93; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .step { margin-bottom: 30px; }
        .step h3 { color: #7E57C2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Cleanance Lab Setup</h1>
        <p>Welcome! Let's get your e-commerce platform up and running.</p>
    </div>

    <?php
    $step = $_GET['step'] ?? 1;
    $errors = [];
    $warnings = [];
    $success = [];

    // Step 1: Environment Check
    if ($step == 1) {
        echo '<div class="step">';
        echo '<h2>Step 1: Environment Check</h2>';

        // Check PHP version
        if (version_compare(PHP_VERSION, '8.0.0', '>=')) {
            $success[] = "✓ PHP version: " . PHP_VERSION;
        } else {
            $errors[] = "✗ PHP 8.0+ required. Current: " . PHP_VERSION;
        }

        // Check required extensions
        $required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'curl', 'gd'];
        foreach ($required_extensions as $ext) {
            if (extension_loaded($ext)) {
                $success[] = "✓ Extension: $ext";
            } else {
                $errors[] = "✗ Missing extension: $ext";
            }
        }

        // Check directories
        $directories = [
            'logs' => ROOT_PATH . '/logs',
            'uploads' => PUBLIC_PATH . '/uploads',
            'uploads/products' => PUBLIC_PATH . '/uploads/products',
            'uploads/users' => PUBLIC_PATH . '/uploads/users'
        ];

        foreach ($directories as $name => $path) {
            if (!file_exists($path)) {
                if (mkdir($path, 0755, true)) {
                    $success[] = "✓ Created directory: $name";
                } else {
                    $errors[] = "✗ Cannot create directory: $name";
                }
            } else {
                if (is_writable($path)) {
                    $success[] = "✓ Directory writable: $name";
                } else {
                    $errors[] = "✗ Directory not writable: $name";
                }
            }
        }

        // Check .env file
        if (!file_exists('.env')) {
            if (file_exists('.env.example')) {
                if (copy('.env.example', '.env')) {
                    $warnings[] = "⚠ Created .env file from .env.example. Please configure it.";
                } else {
                    $errors[] = "✗ Cannot create .env file";
                }
            } else {
                $errors[] = "✗ .env.example file not found";
            }
        } else {
            $success[] = "✓ .env file exists";
        }

        // Display results
        if (!empty($success)) {
            echo '<div class="section success">';
            foreach ($success as $msg) echo "<p>$msg</p>";
            echo '</div>';
        }

        if (!empty($warnings)) {
            echo '<div class="section warning">';
            foreach ($warnings as $msg) echo "<p>$msg</p>";
            echo '</div>';
        }

        if (!empty($errors)) {
            echo '<div class="section error">';
            foreach ($errors as $msg) echo "<p>$msg</p>";
            echo '</div>';
            echo '<p>Please fix the errors above before continuing.</p>';
        } else {
            echo '<a href="?step=2" class="btn">Continue to Database Setup</a>';
        }

        echo '</div>';
    }

    // Step 2: Database Setup
    elseif ($step == 2) {
        echo '<div class="step">';
        echo '<h2>Step 2: Database Setup</h2>';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Test database connection
            $host = $_POST['db_host'] ?? 'localhost';
            $name = $_POST['db_name'] ?? 'cleanance_lab';
            $username = $_POST['db_username'] ?? 'root';
            $password = $_POST['db_password'] ?? '';

            try {
                $dsn = "mysql:host=$host;dbname=$name;charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                ]);

                $success[] = "✓ Database connection successful";

                // Update .env file
                $envContent = file_get_contents('.env');
                $envContent = preg_replace('/DB_HOST=.*/', "DB_HOST=$host", $envContent);
                $envContent = preg_replace('/DB_NAME=.*/', "DB_NAME=$name", $envContent);
                $envContent = preg_replace('/DB_USERNAME=.*/', "DB_USERNAME=$username", $envContent);
                $envContent = preg_replace('/DB_PASSWORD=.*/', "DB_PASSWORD=$password", $envContent);
                
                if (file_put_contents('.env', $envContent)) {
                    $success[] = "✓ Database configuration saved";
                } else {
                    $warnings[] = "⚠ Could not update .env file automatically";
                }

                echo '<div class="section success">';
                foreach ($success as $msg) echo "<p>$msg</p>";
                echo '</div>';

                echo '<a href="?step=3" class="btn">Continue to Migration</a>';

            } catch (PDOException $e) {
                $errors[] = "✗ Database connection failed: " . $e->getMessage();
                echo '<div class="section error">';
                foreach ($errors as $msg) echo "<p>$msg</p>";
                echo '</div>';
            }
        }

        if (empty($success)) {
            echo '<form method="POST">';
            echo '<p><label>Database Host: <input type="text" name="db_host" value="localhost" required></label></p>';
            echo '<p><label>Database Name: <input type="text" name="db_name" value="cleanance_lab" required></label></p>';
            echo '<p><label>Database Username: <input type="text" name="db_username" value="root" required></label></p>';
            echo '<p><label>Database Password: <input type="password" name="db_password"></label></p>';
            echo '<button type="submit" class="btn">Test Connection & Save</button>';
            echo '</form>';
        }

        echo '</div>';
    }

    // Step 3: Run Migrations
    elseif ($step == 3) {
        echo '<div class="step">';
        echo '<h2>Step 3: Database Migration</h2>';

        if (isset($_GET['run'])) {
            echo '<pre>';
            $output = shell_exec('php database/migrate.php 2>&1');
            echo htmlspecialchars($output);
            echo '</pre>';
            echo '<a href="?step=4" class="btn">Continue to Admin Setup</a>';
        } else {
            echo '<p>This will create the database tables and insert sample data.</p>';
            echo '<a href="?step=3&run=1" class="btn">Run Database Migration</a>';
        }

        echo '</div>';
    }

    // Step 4: Admin Setup
    elseif ($step == 4) {
        echo '<div class="step">';
        echo '<h2>Step 4: Admin Account Setup</h2>';

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $_POST['admin_email'];
            $password = $_POST['admin_password'];
            $firstName = $_POST['first_name'];
            $lastName = $_POST['last_name'];

            try {
                $dbConfig = require 'config/database.php';
                $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
                $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);

                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, first_name, last_name, is_admin, is_active, email_verified) 
                    VALUES (?, ?, ?, ?, ?, 1, 1, 1)
                    ON DUPLICATE KEY UPDATE 
                    password = VALUES(password), first_name = VALUES(first_name), last_name = VALUES(last_name)
                ");
                
                if ($stmt->execute([$email, $email, $hashedPassword, $firstName, $lastName])) {
                    echo '<div class="section success">';
                    echo '<p>✓ Admin account created successfully!</p>';
                    echo '<p><strong>Email:</strong> ' . htmlspecialchars($email) . '</p>';
                    echo '</div>';
                    echo '<a href="?step=5" class="btn">Complete Setup</a>';
                } else {
                    echo '<div class="section error"><p>✗ Failed to create admin account</p></div>';
                }

            } catch (Exception $e) {
                echo '<div class="section error"><p>✗ Error: ' . htmlspecialchars($e->getMessage()) . '</p></div>';
            }
        } else {
            echo '<form method="POST">';
            echo '<p><label>First Name: <input type="text" name="first_name" required></label></p>';
            echo '<p><label>Last Name: <input type="text" name="last_name" required></label></p>';
            echo '<p><label>Admin Email: <input type="email" name="admin_email" required></label></p>';
            echo '<p><label>Admin Password: <input type="password" name="admin_password" required minlength="8"></label></p>';
            echo '<button type="submit" class="btn">Create Admin Account</button>';
            echo '</form>';
        }

        echo '</div>';
    }

    // Step 5: Complete
    elseif ($step == 5) {
        echo '<div class="step">';
        echo '<h2>🎉 Setup Complete!</h2>';
        echo '<div class="section success">';
        echo '<p>Congratulations! Your Cleanance Lab e-commerce platform is now ready.</p>';
        echo '<h3>Next Steps:</h3>';
        echo '<ul>';
        echo '<li><a href="public/">Visit your website</a></li>';
        echo '<li><a href="public/admin">Access admin dashboard</a></li>';
        echo '<li>Configure email settings in .env file</li>';
        echo '<li>Add your products and categories</li>';
        echo '<li>Customize your theme</li>';
        echo '</ul>';
        echo '<h3>Important Security Notes:</h3>';
        echo '<ul>';
        echo '<li>Delete or rename this setup.php file</li>';
        echo '<li>Set DEBUG_MODE=false in .env for production</li>';
        echo '<li>Configure SSL/HTTPS</li>';
        echo '<li>Set up regular backups</li>';
        echo '</ul>';
        echo '</div>';
        echo '</div>';
    }
    ?>

    <div class="section">
        <h3>Need Help?</h3>
        <p>Check the README.md file for detailed documentation and troubleshooting tips.</p>
    </div>
</body>
</html>
