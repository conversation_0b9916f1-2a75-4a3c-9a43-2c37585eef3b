# Cleanance Lab Environment Configuration
# Copy this file to .env and update the values

# Application Settings
APP_NAME="Cleanance Lab"
APP_URL="http://localhost/asma"
APP_VERSION="1.0.0"
APP_ENV="development"
DEBUG_MODE=true

# Security Settings
SECRET_KEY="generate-a-secure-random-key-here"
CSRF_TOKEN_NAME="csrf_token"

# Session Settings
SESSION_NAME="cleanance_session"
SESSION_LIFETIME=3600

# Database Configuration
DB_HOST="localhost"
DB_NAME="cleanance_lab"
DB_USERNAME="root"
DB_PASSWORD=""
DB_CHARSET="utf8mb4"

# Email Configuration (SMTP)
SMTP_HOST="ssl0.ovh.net"
SMTP_PORT=465
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-secure-password"
SMTP_ENCRYPTION="ssl"
SMTP_FROM_EMAIL="<EMAIL>"
SMTP_FROM_NAME="Cleanance Lab"

# Upload Settings
UPLOAD_MAX_SIZE=5242880
ALLOWED_IMAGE_TYPES="jpg,jpeg,png,gif,webp"

# Pagination
ITEMS_PER_PAGE=12

# Payment Settings (Development - Replace with real keys in production)
STRIPE_PUBLIC_KEY="pk_test_your_stripe_public_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
PAYPAL_CLIENT_ID="your_paypal_client_id"
PAYPAL_CLIENT_SECRET="your_paypal_client_secret"

# Cache Settings
CACHE_DRIVER="file"
CACHE_TTL=3600

# Security Headers
ENABLE_HTTPS_REDIRECT=false
ENABLE_HSTS=false
ENABLE_CSP=true

# Rate Limiting
LOGIN_RATE_LIMIT=5
LOGIN_RATE_WINDOW=300

# Timezone
APP_TIMEZONE="Africa/Tunis"
