<div class="admin-container">
    <div class="admin-header">
        <h1>Add New Product</h1>
        <a href="<?= UrlHelper::url('/admin/products') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
    </div>

    <div class="admin-content">
        <div class="form-card">
            <form method="POST" enctype="multipart/form-data" id="addProductForm">
                <div class="form-grid">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3>Basic Information</h3>

                        <div class="form-group">
                            <label for="name">Product Name *</label>
                            <input type="text" id="name" name="name" class="form-input" required
                                value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" class="form-textarea" rows="4"
                                placeholder="Enter product description..."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="category_id">Category *</label>
                            <select id="category_id" name="category_id" class="form-select" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories ?? [] as $category): ?>
                                    <option value="<?= $category['id'] ?>"
                                        <?= (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="form-section">
                        <h3>Pricing</h3>

                        <div class="form-group">
                            <label for="price">Regular Price *</label>
                            <div class="input-group">
                                <span class="input-prefix">$</span>
                                <input type="number" id="price" name="price" class="form-input" step="0.01" min="0" required
                                    value="<?= htmlspecialchars($_POST['price'] ?? '') ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sale_price">Sale Price</label>
                            <div class="input-group">
                                <span class="input-prefix">$</span>
                                <input type="number" id="sale_price" name="sale_price" class="form-input" step="0.01" min="0"
                                    value="<?= htmlspecialchars($_POST['sale_price'] ?? '') ?>">
                            </div>
                            <small class="form-help">Leave empty if no sale price</small>
                        </div>
                    </div>

                    <!-- Inventory -->
                    <div class="form-section">
                        <h3>Inventory</h3>

                        <div class="form-group">
                            <label for="stock_quantity">Stock Quantity *</label>
                            <input type="number" id="stock_quantity" name="stock_quantity" class="form-input" min="0" required
                                value="<?= htmlspecialchars($_POST['stock_quantity'] ?? '0') ?>">
                        </div>

                        <div class="form-group">
                            <label for="sku">SKU</label>
                            <input type="text" id="sku" name="sku" class="form-input"
                                value="<?= htmlspecialchars($_POST['sku'] ?? '') ?>">
                            <small class="form-help">Stock Keeping Unit (optional)</small>
                        </div>
                    </div>

                    <!-- Media -->
                    <div class="form-section">
                        <h3>Media</h3>

                        <div class="form-group">
                            <label for="image">Product Image</label>
                            <div class="file-upload">
                                <input type="file" id="image" name="image" class="file-input" accept="image/*">
                                <div class="file-upload-area">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>Click to upload or drag and drop</p>
                                    <small>PNG, JPG, GIF up to 5MB</small>
                                </div>
                            </div>
                            <div id="imagePreview" class="image-preview" style="display: none;">
                                <img src="" alt="Preview">
                                <button type="button" class="remove-image" onclick="removeImage()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="form-section">
                        <h3>Settings</h3>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="is_active" value="1"
                                    <?= (isset($_POST['is_active']) || !isset($_POST)) ? 'checked' : '' ?>>
                                <span class="checkmark"></span>
                                Active Product
                            </label>
                            <small class="form-help">Inactive products won't be visible to customers</small>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="featured" value="1"
                                    <?= (isset($_POST['featured'])) ? 'checked' : '' ?>>
                                <span class="checkmark"></span>
                                Featured Product
                            </label>
                            <small class="form-help">Featured products appear on the homepage</small>
                        </div>
                    </div>
                </div>

                <!-- Error/Success Messages -->
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="history.back()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .admin-container {
        padding: 2rem;
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .admin-header h1 {
        color: var(--text-primary);
        margin: 0;
    }

    .admin-content {
        max-width: 1200px;
    }

    .form-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        background: var(--bg-secondary);
        padding: 1.5rem;
        border-radius: 6px;
    }

    .form-section h3 {
        margin: 0 0 1rem 0;
        color: var(--text-primary);
        font-size: 1.1rem;
        font-weight: 600;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group:last-child {
        margin-bottom: 0;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    .form-input,
    .form-textarea,
    .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .form-input:focus,
    .form-textarea:focus,
    .form-select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
    }

    .form-textarea {
        resize: vertical;
        min-height: 100px;
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-prefix {
        position: absolute;
        left: 0.75rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .input-group .form-input {
        padding-left: 2rem;
    }

    .form-help {
        display: block;
        margin-top: 0.25rem;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: var(--text-primary);
        font-weight: 500;
    }

    .checkbox-label input[type="checkbox"] {
        display: none;
    }

    .checkmark {
        width: 20px;
        height: 20px;
        border: 2px solid var(--border-color);
        border-radius: 4px;
        margin-right: 0.75rem;
        position: relative;
        transition: all 0.3s ease;
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark::after {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 0.75rem;
    }

    .file-upload {
        position: relative;
    }

    .file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .file-upload-area {
        border: 2px dashed var(--border-color);
        border-radius: 6px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .file-upload-area:hover {
        border-color: var(--primary-color);
        background: rgba(126, 87, 194, 0.05);
    }

    .file-upload-area i {
        font-size: 2rem;
        color: var(--text-secondary);
        margin-bottom: 1rem;
    }

    .file-upload-area p {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
        font-weight: 500;
    }

    .file-upload-area small {
        color: var(--text-secondary);
    }

    .image-preview {
        margin-top: 1rem;
        position: relative;
        display: inline-block;
    }

    .image-preview img {
        max-width: 200px;
        max-height: 200px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
    }

    .remove-image {
        position: absolute;
        top: -0.5rem;
        right: -0.5rem;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #dc3545;
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
    }

    .alert {
        padding: 1rem;
        border-radius: 6px;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        padding-top: 1.5rem;
        border-top: 1px solid var(--border-color);
    }

    @media (max-width: 768px) {
        .admin-container {
            padding: 1rem;
        }

        .admin-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .form-actions {
            flex-direction: column;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image upload preview
        const fileInput = document.getElementById('image');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = imagePreview.querySelector('img');

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 5 * 1024 * 1024) { // 5MB limit
                    alert('File size must be less than 5MB');
                    this.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    imagePreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        // Form validation
        const form = document.getElementById('addProductForm');
        form.addEventListener('submit', function(e) {
            const price = parseFloat(document.getElementById('price').value);
            const salePrice = parseFloat(document.getElementById('sale_price').value);

            if (price <= 0) {
                e.preventDefault();
                alert('Regular price must be greater than 0');
                return;
            }

            if (salePrice > 0 && salePrice >= price) {
                e.preventDefault();
                alert('Sale price must be less than regular price');
                return;
            }
        });
    });

    function removeImage() {
        document.getElementById('image').value = '';
        document.getElementById('imagePreview').style.display = 'none';
    }
</script>