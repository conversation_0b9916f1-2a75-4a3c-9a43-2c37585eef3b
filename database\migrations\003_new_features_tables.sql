-- Migration: New E-commerce Features Tables
-- Date: 2025-01-18
-- Description: Create tables for reviews, wishlist, comparison, and other new features
-- Create wishlists table
CREATE TABLE IF NOT EXISTS wishlists (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_wishlist (user_id, product_id),
    INDEX idx_wishlists_user (user_id),
    INDEX idx_wishlists_product (product_id)
);
-- Create product_comparisons table
CREATE TABLE IF NOT EXISTS product_comparisons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_comparison (user_id, product_id),
    INDEX idx_comparisons_user (user_id),
    INDEX idx_comparisons_product (product_id)
);
-- Create review_votes table for review helpfulness
CREATE TABLE IF NOT EXISTS review_votes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    review_id INT NOT NULL,
    user_id INT NOT NULL,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_vote (review_id, user_id),
    INDEX idx_review_votes_review (review_id),
    INDEX idx_review_votes_user (user_id)
);
-- Create wishlist_shares table for shareable wishlists
CREATE TABLE IF NOT EXISTS wishlist_shares (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    share_token VARCHAR(32) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    UNIQUE KEY unique_user_share (user_id)
);
-- Create recently_viewed table
CREATE TABLE IF NOT EXISTS recently_viewed (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    session_id VARCHAR(128) NULL,
    product_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_viewed (user_id, viewed_at),
    INDEX idx_session_viewed (session_id, viewed_at),
    INDEX idx_recently_viewed_product (product_id)
);
-- Create product_recommendations table
CREATE TABLE IF NOT EXISTS product_recommendations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    recommended_product_id INT NOT NULL,
    recommendation_type ENUM(
        'related',
        'frequently_bought',
        'similar',
        'trending'
    ) NOT NULL,
    score DECIMAL(3, 2) DEFAULT 1.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (recommended_product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_recommendation (
        product_id,
        recommended_product_id,
        recommendation_type
    )
);
-- Create newsletter_subscribers table
CREATE TABLE IF NOT EXISTS newsletter_subscribers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NULL,
    status ENUM('active', 'unsubscribed', 'bounced') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL,
    preferences JSON NULL,
    verification_token VARCHAR(64) NULL,
    is_verified BOOLEAN DEFAULT FALSE
);
-- Create email_templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    variables TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Create support_tickets table
CREATE TABLE IF NOT EXISTS support_tickets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_number VARCHAR(20) NOT NULL UNIQUE,
    user_id INT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE
    SET NULL,
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE
    SET NULL
);
-- Create support_ticket_replies table
CREATE TABLE IF NOT EXISTS support_ticket_replies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_id INT NOT NULL,
    user_id INT NULL,
    message TEXT NOT NULL,
    is_staff_reply BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE
    SET NULL
);
-- Create site_settings table for admin customization
CREATE TABLE IF NOT EXISTS site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NULL,
    setting_type ENUM(
        'text',
        'textarea',
        'boolean',
        'number',
        'json',
        'file'
    ) DEFAULT 'text',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Create banners table for homepage customization
CREATE TABLE IF NOT EXISTS banners (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    subtitle TEXT NULL,
    image_path VARCHAR(500) NULL,
    link_url VARCHAR(500) NULL,
    button_text VARCHAR(100) NULL,
    position ENUM('hero', 'top', 'middle', 'bottom', 'sidebar') DEFAULT 'hero',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_wishlists_user ON wishlists(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlists_product ON wishlists(product_id);
CREATE INDEX IF NOT EXISTS idx_comparisons_user ON product_comparisons(user_id);
CREATE INDEX IF NOT EXISTS idx_comparisons_product ON product_comparisons(product_id);
CREATE INDEX IF NOT EXISTS idx_review_votes_review ON review_votes(review_id);
CREATE INDEX IF NOT EXISTS idx_review_votes_user ON review_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_recently_viewed_product ON recently_viewed(product_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_product ON product_recommendations(product_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_type ON product_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_newsletter_email ON newsletter_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_newsletter_status ON newsletter_subscribers(status);
CREATE INDEX IF NOT EXISTS idx_tickets_status ON support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_tickets_user ON support_tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_ticket_replies_ticket ON support_ticket_replies(ticket_id);
CREATE INDEX IF NOT EXISTS idx_banners_position ON banners(position);
CREATE INDEX IF NOT EXISTS idx_banners_active ON banners(is_active);
-- Insert default email templates
INSERT IGNORE INTO email_templates (name, subject, body, variables)
VALUES (
        'order_confirmation',
        'Order Confirmation - {order_number}',
        '<h2>Thank you for your order!</h2>
<p>Dear {customer_name},</p>
<p>We have received your order and it is being processed.</p>
<p><strong>Order Number:</strong> {order_number}</p>
<p><strong>Order Total:</strong> {order_total}</p>
<p><strong>Order Date:</strong> {order_date}</p>
<p>You will receive another email when your order ships.</p>
<p>Thank you for shopping with {app_name}!</p>',
        'customer_name, order_number, order_total, order_date, app_name'
    ),
    (
        'password_reset',
        'Password Reset Request - {app_name}',
        '<h2>Password Reset Request</h2>
<p>Dear {customer_name},</p>
<p>You have requested to reset your password. Click the link below to reset it:</p>
<p><a href="{reset_link}" style="background: #7E57C2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
<p>This link will expire at {expires_at}.</p>
<p>If you did not request this, please ignore this email.</p>
<p>Best regards,<br>{app_name}</p>',
        'customer_name, reset_link, expires_at, app_name'
    ),
    (
        'welcome',
        'Welcome to {app_name}!',
        '<h2>Welcome to {app_name}!</h2>
<p>Dear {customer_name},</p>
<p>Thank you for creating an account with us. We are excited to have you as part of our community!</p>
<p>You can now:</p>
<ul>
<li>Browse our products</li>
<li>Add items to your wishlist</li>
<li>Track your orders</li>
<li>Leave product reviews</li>
</ul>
<p>Happy shopping!</p>
<p>Best regards,<br>The {app_name} Team</p>',
        'customer_name, app_name'
    );
-- Insert default site settings
INSERT IGNORE INTO site_settings (
        setting_key,
        setting_value,
        setting_type,
        category,
        description,
        is_public
    )
VALUES (
        'site_name',
        'Cleanance Lab',
        'text',
        'general',
        'Website name',
        TRUE
    ),
    (
        'site_description',
        'Your trusted source for quality products',
        'textarea',
        'general',
        'Website description',
        TRUE
    ),
    (
        'site_logo',
        '',
        'file',
        'general',
        'Website logo',
        TRUE
    ),
    (
        'site_favicon',
        '',
        'file',
        'general',
        'Website favicon',
        TRUE
    ),
    (
        'contact_email',
        '<EMAIL>',
        'text',
        'contact',
        'Contact email address',
        TRUE
    ),
    (
        'contact_phone',
        '',
        'text',
        'contact',
        'Contact phone number',
        TRUE
    ),
    (
        'contact_address',
        '',
        'textarea',
        'contact',
        'Contact address',
        TRUE
    ),
    (
        'social_facebook',
        '',
        'text',
        'social',
        'Facebook URL',
        TRUE
    ),
    (
        'social_twitter',
        '',
        'text',
        'social',
        'Twitter URL',
        TRUE
    ),
    (
        'social_instagram',
        '',
        'text',
        'social',
        'Instagram URL',
        TRUE
    ),
    (
        'theme_primary_color',
        '#7E57C2',
        'text',
        'theme',
        'Primary theme color',
        TRUE
    ),
    (
        'theme_secondary_color',
        '#6a4c93',
        'text',
        'theme',
        'Secondary theme color',
        TRUE
    ),
    (
        'enable_reviews',
        '1',
        'boolean',
        'features',
        'Enable product reviews',
        FALSE
    ),
    (
        'enable_wishlist',
        '1',
        'boolean',
        'features',
        'Enable wishlist feature',
        FALSE
    ),
    (
        'enable_comparison',
        '1',
        'boolean',
        'features',
        'Enable product comparison',
        FALSE
    ),
    (
        'enable_newsletter',
        '1',
        'boolean',
        'features',
        'Enable newsletter subscription',
        FALSE
    ),
    (
        'products_per_page',
        '12',
        'number',
        'catalog',
        'Products per page',
        FALSE
    ),
    (
        'featured_products_count',
        '8',
        'number',
        'catalog',
        'Featured products count on homepage',
        FALSE
    ),
    (
        'recent_products_count',
        '6',
        'number',
        'catalog',
        'Recent products count on homepage',
        FALSE
    );