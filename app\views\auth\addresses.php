<div class="container mt-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>My Account
                    </h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/asma/public/account" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="/asma/public/profile" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-edit me-2"></i>Profile Settings
                    </a>
                    <a href="/asma/public/orders" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>My Orders
                    </a>
                    <a href="/asma/public/account/addresses" class="list-group-item list-group-item-action active">
                        <i class="fas fa-map-marker-alt me-2"></i>Addresses
                    </a>
                    <a href="/asma/public/wishlist" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i>Wishlist
                    </a>
                    <a href="/asma/public/change-password" class="list-group-item list-group-item-action">
                        <i class="fas fa-key me-2"></i>Change Password
                    </a>
                    <a href="/asma/public/account/settings" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2"></i>Account Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Dashboard Navigation -->
            <nav class="dashboard-nav">
                <ul class="dashboard-nav-list">
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/account" class="dashboard-nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/orders" class="dashboard-nav-link">
                            <i class="fas fa-shopping-bag"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/addresses" class="dashboard-nav-link active">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Addresses</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/settings" class="dashboard-nav-link">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/wishlist" class="dashboard-nav-link">
                            <i class="fas fa-heart"></i>
                            <span>Wishlist</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Manage Addresses</h1>
                <p class="page-subtitle">Add, edit, and manage your shipping addresses for faster checkout.</p>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>
                    <i class="fas fa-map-marker-alt me-2"></i>My Addresses
                </h3>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                    <i class="fas fa-plus me-2"></i>Add New Address
                </button>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($success)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Addresses Grid -->
            <div class="row">
                <?php if (!empty($addresses)): ?>
                    <?php foreach ($addresses as $address): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card shadow-sm h-100 address-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h6 class="card-title mb-0">
                                            <?php if ($address['is_default']): ?>
                                                <span class="badge bg-primary me-2">Default</span>
                                            <?php endif; ?>
                                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                            <?php echo htmlspecialchars($address['address_type']); ?>
                                        </h6>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="editAddress(<?php echo $address['id']; ?>)">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a>
                                                </li>
                                                <?php if (!$address['is_default']): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="setDefaultAddress(<?php echo $address['id']; ?>)">
                                                            <i class="fas fa-star me-2"></i>Set as Default
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteAddress(<?php echo $address['id']; ?>)">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="address-details">
                                        <div class="address-header mb-3">
                                            <h6 class="fw-bold text-dark mb-1">
                                                <?php echo htmlspecialchars($address['first_name'] . ' ' . $address['last_name']); ?>
                                            </h6>
                                        </div>
                                        <div class="address-content">
                                            <p class="mb-2 text-muted">
                                                <i class="fas fa-home me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($address['address_line1']); ?>
                                            </p>
                                            <?php if (!empty($address['address_line2'])): ?>
                                                <p class="mb-2 text-muted">
                                                    <i class="fas fa-building me-2 text-primary"></i>
                                                    <?php echo htmlspecialchars($address['address_line2']); ?>
                                                </p>
                                            <?php endif; ?>
                                            <p class="mb-2 text-muted">
                                                <i class="fas fa-map me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($address['city'] . ', ' . $address['state'] . ' ' . $address['postal_code']); ?>
                                            </p>
                                            <p class="mb-2 text-muted">
                                                <i class="fas fa-globe me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($address['country']); ?>
                                            </p>
                                            <?php if (!empty($address['phone'])): ?>
                                                <p class="mb-0 text-muted">
                                                    <i class="fas fa-phone me-2 text-primary"></i>
                                                    <?php echo htmlspecialchars($address['phone']); ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="card shadow-sm empty-state-card">
                            <div class="card-body text-center py-5">
                                <div class="empty-state-icon mb-4">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h5 class="text-muted mb-3">No addresses saved</h5>
                                <p class="text-muted mb-4">Add your first address to make checkout faster and more convenient.</p>
                                <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                    <i class="fas fa-plus me-2"></i>Add Your First Address
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Address Modal -->
<div class="modal fade" id="addAddressModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Add New Address
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="/account/addresses/add" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address_type" class="form-label">Address Type *</label>
                        <select class="form-select" id="address_type" name="address_type" required>
                            <option value="">Select address type</option>
                            <option value="Home">Home</option>
                            <option value="Work">Work</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="address_line1" class="form-label">Address Line 1 *</label>
                        <input type="text" class="form-control" id="address_line1" name="address_line1" required>
                    </div>

                    <div class="mb-3">
                        <label for="address_line2" class="form-label">Address Line 2</label>
                        <input type="text" class="form-control" id="address_line2" name="address_line2">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">City *</label>
                            <input type="text" class="form-control" id="city" name="city" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="state" class="form-label">State/Province *</label>
                            <input type="text" class="form-control" id="state" name="state" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="postal_code" class="form-label">Postal Code *</label>
                            <input type="text" class="form-control" id="postal_code" name="postal_code" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">Country *</label>
                            <input type="text" class="form-control" id="country" name="country" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_default" name="is_default">
                        <label class="form-check-label" for="is_default">
                            Set as default address
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Address</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function editAddress(addressId) {
        // Implement edit functionality
        window.location.href = `/account/addresses/edit/${addressId}`;
    }

    function setDefaultAddress(addressId) {
        if (confirm('Set this address as your default address?')) {
            // Implement set default functionality
            window.location.href = `/account/addresses/set-default/${addressId}`;
        }
    }

    function deleteAddress(addressId) {
        if (confirm('Are you sure you want to delete this address?')) {
            // Implement delete functionality
            window.location.href = `/account/addresses/delete/${addressId}`;
        }
    }
</script>

<style>
    .card {
        border: none;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        transition: transform var(--transition-fast), box-shadow var(--transition-fast);
        background: var(--dark-card);
        border: 1px solid var(--border-color);
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    .card-header {
        border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
        border-bottom: 1px solid var(--border-color);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: var(--white);
    }

    .list-group-item {
        border: none;
        border-radius: 0;
        padding: var(--spacing-md) var(--spacing-lg);
        transition: all var(--transition-fast);
        border-left: 3px solid transparent;
        background: var(--dark-surface);
        color: var(--text-primary);
    }

    .list-group-item:hover {
        background: var(--primary-purple);
        border-left-color: var(--white);
        transform: translateX(5px);
        color: var(--white);
    }

    .list-group-item.active {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-color: var(--primary-purple);
        border-left-color: var(--white);
        box-shadow: var(--shadow-md);
        color: var(--white);
    }

    .btn {
        border-radius: var(--radius-md);
        font-weight: 500;
        padding: var(--spacing-sm) var(--spacing-lg);
        transition: all var(--transition-fast);
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border: none;
        color: var(--white);
    }

    .btn-outline-secondary {
        border-color: var(--border-color);
        color: var(--text-secondary);
        background: var(--dark-surface);
    }

    .btn-outline-secondary:hover {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-color: var(--primary-purple);
        color: var(--white);
    }

    .address-card {
        border: 1px solid var(--border-color);
        background: var(--dark-card);
    }

    .address-card:hover {
        border-color: var(--primary-purple);
    }

    .address-header h6 {
        color: var(--text-primary);
        font-size: var(--font-size-lg);
    }

    .address-content p {
        font-size: var(--font-size-sm);
        line-height: 1.5;
        color: var(--text-secondary);
    }

    .address-content i {
        width: 16px;
        text-align: center;
        color: var(--primary-purple);
    }

    .badge {
        border-radius: 20px;
        padding: 0.5em 0.75em;
        font-weight: 500;
        background: var(--primary-purple);
    }

    .empty-state-card {
        background: var(--dark-surface);
        border: 2px dashed var(--border-color);
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        font-size: var(--font-size-2xl);
        color: var(--white);
        box-shadow: var(--shadow-lg);
    }

    .dropdown-menu {
        border-radius: var(--radius-md);
        border: none;
        box-shadow: var(--shadow-lg);
        background: var(--dark-card);
        border: 1px solid var(--border-color);
    }

    .dropdown-item {
        padding: var(--spacing-sm) var(--spacing-md);
        transition: all var(--transition-fast);
        color: var(--text-primary);
    }

    .dropdown-item:hover {
        background: var(--primary-purple);
        transform: translateX(5px);
        color: var(--white);
    }

    .dropdown-item.text-danger:hover {
        background: var(--error-color);
        color: var(--white);
    }

    .modal-content {
        border-radius: var(--radius-lg);
        border: none;
        box-shadow: var(--shadow-xl);
        background: var(--dark-card);
        border: 1px solid var(--border-color);
    }

    .modal-header {
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: var(--white);
        border-bottom: none;
    }

    .form-control,
    .form-select {
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
        padding: var(--spacing-sm) var(--spacing-md);
        transition: all var(--transition-fast);
        background: var(--dark-surface);
        color: var(--text-primary);
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 0.2rem rgba(126, 87, 194, 0.25);
        transform: translateY(-1px);
    }

    .alert {
        border-radius: var(--radius-md);
        border: none;
        box-shadow: var(--shadow-md);
    }

    .text-muted {
        color: var(--text-muted) !important;
    }

    .fw-bold {
        font-weight: 600 !important;
    }

    .page-header {
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        border: 1px solid var(--border-color);
    }

    .page-title {
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        margin-bottom: 0;
    }

    /* Dashboard Navigation */
    .dashboard-nav {
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .dashboard-nav-list {
        display: flex;
        list-style: none;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
        margin: 0;
        padding: 0;
    }

    .dashboard-nav-item {
        flex: 1;
        min-width: 200px;
    }

    .dashboard-nav-link {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        color: var(--text-primary);
        text-decoration: none;
        border-radius: var(--radius-md);
        transition: all var(--transition-fast);
        background: var(--dark-surface);
        border: 1px solid var(--border-color);
    }

    .dashboard-nav-link:hover {
        background: var(--primary-purple);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .dashboard-nav-link.active {
        background: var(--primary-purple);
        color: var(--white);
        box-shadow: var(--shadow-md);
    }

    .dashboard-nav-link i {
        font-size: var(--font-size-lg);
        width: 20px;
        text-align: center;
    }
</style>