<div class="container mt-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>My Account
                    </h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/asma/public/account" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="/asma/public/profile" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-edit me-2"></i>Profile Settings
                    </a>
                    <a href="/asma/public/orders" class="list-group-item list-group-item-action active">
                        <i class="fas fa-shopping-bag me-2"></i>My Orders
                    </a>
                    <a href="/asma/public/account/addresses" class="list-group-item list-group-item-action">
                        <i class="fas fa-map-marker-alt me-2"></i>Addresses
                    </a>
                    <a href="/asma/public/wishlist" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i>Wishlist
                    </a>
                    <a href="/asma/public/change-password" class="list-group-item list-group-item-action">
                        <i class="fas fa-key me-2"></i>Change Password
                    </a>
                    <a href="/asma/public/account/settings" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2"></i>Account Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Dashboard Navigation -->
            <nav class="dashboard-nav">
                <ul class="dashboard-nav-list">
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/account" class="dashboard-nav-link">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/orders" class="dashboard-nav-link active">
                            <i class="fas fa-shopping-bag"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/addresses" class="dashboard-nav-link">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Addresses</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/settings" class="dashboard-nav-link">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                    <li class="dashboard-nav-item">
                        <a href="/asma/public/wishlist" class="dashboard-nav-link">
                            <i class="fas fa-heart"></i>
                            <span>Wishlist</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Order History</h1>
                <p class="page-subtitle">Track your orders and view order details.</p>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>
                    <i class="fas fa-shopping-bag me-2"></i>My Orders
                </h3>
                <a href="/asma/public/products" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Continue Shopping
                </a>
            </div>

            <!-- Filters -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo ($_GET['status'] ?? '') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="processing" <?php echo ($_GET['status'] ?? '') === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                <option value="shipped" <?php echo ($_GET['status'] ?? '') === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                                <option value="delivered" <?php echo ($_GET['status'] ?? '') === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                <option value="cancelled" <?php echo ($_GET['status'] ?? '') === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                value="<?php echo $_GET['date_from'] ?? ''; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                value="<?php echo $_GET['date_to'] ?? ''; ?>">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                            <a href="/asma/public/orders" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Orders List -->
            <div class="row">
                <?php if (!empty($orders)): ?>
                    <?php foreach ($orders as $order): ?>
                        <div class="col-12 mb-4">
                            <div class="card shadow-sm order-card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-0">
                                                <i class="fas fa-shopping-bag me-2"></i>
                                                Order #<?php echo htmlspecialchars($order['order_number']); ?>
                                            </h6>
                                        </div>
                                        <div class="col-md-6 text-md-end">
                                            <span class="badge status-badge status-<?php echo strtolower($order['status']); ?>">
                                                <?php echo htmlspecialchars(ucfirst($order['status'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="order-details">
                                                <div class="order-info mb-3">
                                                    <div class="row">
                                                        <div class="col-sm-6 mb-2">
                                                            <small class="text-muted">Order Date</small>
                                                            <div class="fw-semibold">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                <?php echo date('M j, Y', strtotime($order['created_at'])); ?>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-6 mb-2">
                                                            <small class="text-muted">Total Amount</small>
                                                            <div class="fw-semibold text-primary">
                                                                <i class="fas fa-dollar-sign me-1"></i>
                                                                $<?php echo number_format($order['total_amount'], 2); ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="order-items">
                                                    <h6 class="fw-bold mb-3">
                                                        <i class="fas fa-box me-2"></i>
                                                        Items (<?php echo count($order['items']); ?>)
                                                    </h6>
                                                    <div class="items-list">
                                                        <?php foreach ($order['items'] as $item): ?>
                                                            <div class="item-row">
                                                                <div class="d-flex align-items-center">
                                                                    <div class="item-image me-3">
                                                                        <?php if (!empty($item['image'])): ?>
                                                                            <img src="/asma/public/uploads/products/<?php echo htmlspecialchars($item['image']); ?>"
                                                                                alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                                                class="img-thumbnail">
                                                                        <?php else: ?>
                                                                            <div class="placeholder-image">
                                                                                <i class="fas fa-image"></i>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                    <div class="item-details flex-grow-1">
                                                                        <h6 class="mb-1"><?php echo htmlspecialchars($item['name']); ?></h6>
                                                                        <small class="text-muted">
                                                                            Quantity: <?php echo $item['quantity']; ?> × $<?php echo number_format($item['price'], 2); ?>
                                                                        </small>
                                                                    </div>
                                                                    <div class="item-total">
                                                                        <span class="fw-semibold">$<?php echo number_format($item['quantity'] * $item['price'], 2); ?></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="order-actions">
                                                <div class="action-buttons">
                                                    <a href="/asma/public/orders/<?php echo $order['id']; ?>" class="btn btn-primary btn-sm w-100 mb-2">
                                                        <i class="fas fa-eye me-2"></i>View Details
                                                    </a>
                                                    <?php if ($order['status'] === 'pending'): ?>
                                                        <button class="btn btn-outline-danger btn-sm w-100 mb-2" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                                            <i class="fas fa-times me-2"></i>Cancel Order
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($order['status'] === 'delivered'): ?>
                                                        <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="reorder(<?php echo $order['id']; ?>)">
                                                            <i class="fas fa-redo me-2"></i>Reorder
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="downloadInvoice(<?php echo $order['id']; ?>)">
                                                        <i class="fas fa-download me-2"></i>Download Invoice
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="card shadow-sm empty-state-card">
                            <div class="card-body text-center py-5">
                                <div class="empty-state-icon mb-4">
                                    <i class="fas fa-shopping-bag"></i>
                                </div>
                                <h5 class="text-muted mb-3">No orders yet</h5>
                                <p class="text-muted mb-4">Start shopping to see your order history here.</p>
                                <a href="/asma/public/products" class="btn btn-primary btn-lg">
                                    <i class="fas fa-shopping-cart me-2"></i>Start Shopping
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Orders pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($currentPage > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $currentPage - 1; ?>&status=<?php echo $_GET['status'] ?? ''; ?>&date_from=<?php echo $_GET['date_from'] ?? ''; ?>&date_to=<?php echo $_GET['date_to'] ?? ''; ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $currentPage ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $_GET['status'] ?? ''; ?>&date_from=<?php echo $_GET['date_from'] ?? ''; ?>&date_to=<?php echo $_GET['date_to'] ?? ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($currentPage < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $currentPage + 1; ?>&status=<?php echo $_GET['status'] ?? ''; ?>&date_from=<?php echo $_GET['date_from'] ?? ''; ?>&date_to=<?php echo $_GET['date_to'] ?? ''; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
function getStatusColor($status)
{
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'processing':
            return 'info';
        case 'shipped':
            return 'primary';
        case 'delivered':
            return 'success';
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}
?>

<script>
    function cancelOrder(orderId) {
        if (confirm('Are you sure you want to cancel this order? This action cannot be undone.')) {
            fetch(`/asma/public/order/cancel/${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to cancel order: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while cancelling the order.');
                });
        }
    }

    function reorder(orderId) {
        if (confirm('Add all items from this order to your cart?')) {
            fetch(`/asma/public/order/reorder/${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/asma/public/cart';
                    } else {
                        alert('Failed to reorder: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while processing the reorder.');
                });
        }
    }
</script>

<style>
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid #dee2e6;
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        color: white;
    }

    .list-group-item {
        border: none;
        border-radius: 0;
        padding: 15px 20px;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
        border-left-color: #0d6efd;
        transform: translateX(5px);
    }

    .list-group-item.active {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border-color: #0d6efd;
        border-left-color: #fff;
        box-shadow: 0 2px 10px rgba(13, 110, 253, 0.3);
    }

    .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 10px 20px;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border: none;
    }

    .btn-outline-primary {
        border-color: #0d6efd;
        color: #0d6efd;
    }

    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border-color: #0d6efd;
        color: white;
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        border-color: #6c757d;
        color: white;
    }

    .btn-outline-danger {
        border-color: #dc3545;
        color: #dc3545;
    }

    .btn-outline-danger:hover {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border-color: #dc3545;
        color: white;
    }

    .order-card {
        border: 1px solid #e9ecef;
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
    }

    .order-card:hover {
        border-color: #0d6efd;
    }

    .status-badge {
        border-radius: 20px;
        padding: 0.5em 0.75em;
        font-weight: 500;
        font-size: 0.85rem;
    }

    .status-pending {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
    }

    .status-processing {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }

    .status-shipped {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
    }

    .status-delivered {
        background: linear-gradient(135deg, #6f42c1, #5a32a3);
        color: white;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
    }

    .order-info {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 15px;
    }

    .items-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .item-row {
        padding: 12px;
        border-radius: 8px;
        background: #f8f9fa;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .item-row:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .item-image img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
    }

    .placeholder-image {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 1.2rem;
    }

    .item-details h6 {
        color: #495057;
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .item-total {
        font-size: 1.1rem;
        color: #0d6efd;
    }

    .order-actions {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 20px;
        height: fit-content;
    }

    .action-buttons .btn {
        margin-bottom: 10px;
    }

    .action-buttons .btn:last-child {
        margin-bottom: 0;
    }

    .empty-state-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px dashed #dee2e6;
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        font-size: 2rem;
        color: white;
        box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
    }

    .text-primary {
        color: #0d6efd !important;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .fw-bold {
        font-weight: 600 !important;
    }

    .fw-semibold {
        font-weight: 500 !important;
    }

    .page-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }

    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0;
    }

    .page-subtitle {
        color: #6c757d;
        margin-bottom: 0;
    }

    .form-control,
    .form-select {
        border-radius: 10px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        transform: translateY(-1px);
    }

    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .pagination {
        justify-content: center;
    }

    .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: none;
        color: #0d6efd;
        transition: all 0.3s ease;
    }

    .page-link:hover {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        color: white;
        transform: translateY(-1px);
    }

    .page-item.active .page-link {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
        border-color: #0d6efd;
    }
</style>