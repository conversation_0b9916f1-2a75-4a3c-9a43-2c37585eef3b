<section class="admin-section">
    <h1>Users Report</h1>
    <div class="admin-table-wrapper">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Email</th>
                    <th>Orders</th>
                    <th>Total Spent</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($users)): foreach ($users as $user): ?>
                        <tr>
                            <td><?= htmlspecialchars($user['name']) ?></td>
                            <td><?= htmlspecialchars($user['email']) ?></td>
                            <td><?= $user['orders'] ?></td>
                            <td>$<?= number_format($user['total_spent'], 2) ?></td>
                        </tr>
                    <?php endforeach;
                else: ?>
                    <tr>
                        <td colspan="4" style="text-align:center; color:var(--text-secondary);">No users found.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</section>
<style>
    .admin-section {
        padding: 40px 0;
    }

    .admin-table-wrapper {
        overflow-x: auto;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--dark-card);
    }

    .admin-table th,
    .admin-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--dark-surface);
        color: var(--primary-purple);
        font-weight: 600;
    }
</style>