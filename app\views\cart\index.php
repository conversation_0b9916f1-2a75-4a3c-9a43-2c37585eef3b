<div class="cart-container">
    <div class="container">
        <div class="cart-header">
            <h1 class="page-title">Shopping Cart</h1>
            <p class="cart-subtitle">Review your items and proceed to checkout</p>
        </div>

        <?php if (empty($cart)): ?>
            <div class="empty-cart">
                <div class="empty-cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h2>Your cart is empty</h2>
                <p>Looks like you haven't added any items to your cart yet.</p>
                <div class="empty-cart-actions">
                    <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag"></i>
                        Start Shopping
                    </a>
                    <a href="<?= UrlHelper::url('/wishlist') ?>" class="btn btn-outline btn-lg">
                        <i class="fas fa-heart"></i>
                        View Wishlist
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="cart-content">
                <div class="cart-items">
                    <div class="cart-items-header">
                        <h3>Cart Items (<?= count($cart) ?>)</h3>
                        <button class="clear-cart-btn" onclick="clearCart()">
                            <i class="fas fa-trash"></i>
                            Clear Cart
                        </button>
                    </div>

                    <?php foreach ($cart as $item): ?>
                        <div class="cart-item" data-product-id="<?= $item['id'] ?>">
                            <div class="cart-item-image">
                                <?php if (!empty($item['image_path'])): ?>
                                    <img src="<?= UrlHelper::url('/uploads/products/' . $item['image_path']) ?>"
                                        alt="<?= htmlspecialchars($item['name']) ?>">
                                <?php else: ?>
                                    <img src="<?= UrlHelper::url('/assets/images/default-product.jpg') ?>"
                                        alt="<?= htmlspecialchars($item['name']) ?>">
                                <?php endif; ?>
                            </div>

                            <div class="cart-item-details">
                                <h4 class="cart-item-title">
                                    <a href="<?= UrlHelper::url('/product/' . ($item['slug'] ?? '')) ?>">
                                        <?= htmlspecialchars($item['name']) ?>
                                    </a>
                                </h4>
                                <p class="cart-item-category"><?= htmlspecialchars($item['category_name'] ?? 'Uncategorized') ?></p>

                                <div class="cart-item-price">
                                    <span class="current-price">
                                        $<?= number_format($item['sale_price'] ?: $item['price'], 2) ?>
                                    </span>
                                    <?php if ($item['sale_price']): ?>
                                        <span class="original-price">
                                            $<?= number_format($item['price'], 2) ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="cart-item-quantity">
                                <div class="quantity-controls">
                                    <button class="quantity-btn" onclick="updateQuantity(<?= $item['id'] ?>, -1)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" class="quantity-input"
                                        value="<?= $item['quantity'] ?>"
                                        min="1" max="99"
                                        onchange="updateQuantity(<?= $item['id'] ?>, this.value, true)">
                                    <button class="quantity-btn" onclick="updateQuantity(<?= $item['id'] ?>, 1)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="cart-item-total">
                                <span class="item-total">
                                    $<?= number_format(($item['sale_price'] ?: $item['price']) * $item['quantity'], 2) ?>
                                </span>
                            </div>

                            <div class="cart-item-actions">
                                <button class="remove-item-btn" onclick="removeItem(<?= $item['id'] ?>)" title="Remove item">
                                    <i class="fas fa-times"></i>
                                </button>
                                <button class="wishlist-btn" onclick="moveToWishlist(<?= $item['id'] ?>)" title="Move to wishlist">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="cart-summary">
                    <div class="summary-card">
                        <h3 class="summary-title">Order Summary</h3>

                        <div class="summary-items">
                            <div class="summary-item">
                                <span>Subtotal (<?= count($cart) ?> items)</span>
                                <span>$<?= number_format($total, 2) ?></span>
                            </div>

                            <div class="summary-item">
                                <span>Shipping</span>
                                <span class="shipping-cost">Calculated at checkout</span>
                            </div>

                            <div class="summary-item">
                                <span>Tax</span>
                                <span class="tax-amount">Calculated at checkout</span>
                            </div>
                        </div>

                        <div class="summary-total">
                            <span>Estimated Total</span>
                            <span class="total-amount">$<?= number_format($total, 2) ?></span>
                        </div>

                        <div class="summary-actions">
                            <a href="<?= UrlHelper::url('/checkout') ?>" class="btn btn-primary btn-lg checkout-btn">
                                <i class="fas fa-credit-card"></i>
                                Proceed to Checkout
                            </a>

                            <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-outline continue-shopping-btn">
                                <i class="fas fa-arrow-left"></i>
                                Continue Shopping
                            </a>
                        </div>

                        <div class="payment-methods">
                            <p class="payment-title">We Accept</p>
                            <div class="payment-icons">
                                <i class="fab fa-cc-visa"></i>
                                <i class="fab fa-cc-mastercard"></i>
                                <i class="fab fa-cc-paypal"></i>
                                <i class="fab fa-cc-stripe"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Discount Code -->
                    <div class="discount-card">
                        <h4>Have a discount code?</h4>
                        <form class="discount-form" onsubmit="applyDiscount(event)">
                            <div class="discount-input-group">
                                <input type="text" id="discountCode" placeholder="Enter code" class="discount-input">
                                <button type="submit" class="btn btn-primary">Apply</button>
                            </div>
                        </form>
                        <div id="discountMessage" class="discount-message"></div>
                    </div>

                    <!-- Security Notice -->
                    <div class="security-notice">
                        <div class="security-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="security-text">
                            <h5>Secure Checkout</h5>
                            <p>Your payment information is encrypted and secure</p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    .cart-container {
        padding: var(--spacing-xl) 0;
        background-color: var(--dark-bg);
        min-height: calc(100vh - 200px);
    }

    .cart-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .page-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .cart-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Empty Cart */
    .empty-cart {
        text-align: center;
        padding: var(--spacing-2xl) 0;
    }

    .empty-cart-icon {
        font-size: var(--font-size-4xl);
        color: var(--text-muted);
        margin-bottom: var(--spacing-lg);
    }

    .empty-cart h2 {
        font-size: var(--font-size-2xl);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .empty-cart p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
    }

    .empty-cart-actions {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Cart Content */
    .cart-content {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: var(--spacing-xl);
    }

    /* Cart Items */
    .cart-items {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
    }

    .cart-items-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-color);
    }

    .cart-items-header h3 {
        font-size: var(--font-size-xl);
        font-weight: 600;
        color: var(--text-primary);
    }

    .clear-cart-btn {
        background: none;
        border: none;
        color: var(--error-color);
        cursor: pointer;
        font-size: var(--font-size-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        transition: color var(--transition-fast);
    }

    .clear-cart-btn:hover {
        color: var(--error-color);
        text-decoration: underline;
    }

    .cart-item {
        display: grid;
        grid-template-columns: 120px 1fr auto auto auto;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-color);
        align-items: center;
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    .cart-item-image {
        width: 120px;
        height: 120px;
        border-radius: var(--radius-md);
        overflow: hidden;
    }

    .cart-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .cart-item-details {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .cart-item-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
    }

    .cart-item-title a {
        color: inherit;
        text-decoration: none;
        transition: color var(--transition-fast);
    }

    .cart-item-title a:hover {
        color: var(--primary-purple);
    }

    .cart-item-category {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .cart-item-price {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .current-price {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--primary-purple);
    }

    .original-price {
        font-size: var(--font-size-sm);
        color: var(--text-muted);
        text-decoration: line-through;
    }

    /* Quantity Controls */
    .quantity-controls {
        display: flex;
        align-items: center;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        overflow: hidden;
    }

    .quantity-btn {
        background: none;
        border: none;
        padding: var(--spacing-sm);
        color: var(--text-secondary);
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .quantity-btn:hover {
        background-color: var(--dark-surface);
        color: var(--primary-purple);
    }

    .quantity-input {
        width: 60px;
        text-align: center;
        border: none;
        background: none;
        color: var(--text-primary);
        font-size: var(--font-size-base);
        padding: var(--spacing-sm);
    }

    .quantity-input:focus {
        outline: none;
    }

    .cart-item-total {
        text-align: right;
    }

    .item-total {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
    }

    .cart-item-actions {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .remove-item-btn,
    .wishlist-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: var(--spacing-xs);
        border-radius: var(--radius-sm);
        transition: all var(--transition-fast);
    }

    .remove-item-btn:hover {
        color: var(--error-color);
        background-color: rgba(244, 67, 54, 0.1);
    }

    .wishlist-btn:hover {
        color: var(--primary-purple);
        background-color: rgba(126, 87, 194, 0.1);
    }

    /* Cart Summary */
    .cart-summary {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .summary-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .summary-title {
        font-size: var(--font-size-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
    }

    .summary-items {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .summary-total {
        display: flex;
        justify-content: space-between;
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-color);
    }

    .total-amount {
        color: var(--primary-purple);
    }

    .summary-actions {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .checkout-btn {
        width: 100%;
    }

    .continue-shopping-btn {
        width: 100%;
    }

    .payment-methods {
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--border-color);
    }

    .payment-title {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-sm);
    }

    .payment-icons {
        display: flex;
        gap: var(--spacing-sm);
        font-size: var(--font-size-xl);
        color: var(--text-muted);
    }

    /* Discount Card */
    .discount-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .discount-card h4 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .discount-form {
        margin-bottom: var(--spacing-md);
    }

    .discount-input-group {
        display: flex;
        gap: var(--spacing-sm);
    }

    .discount-input {
        flex: 1;
        padding: var(--spacing-sm) var(--spacing-md);
        background-color: var(--dark-surface);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
    }

    .discount-input:focus {
        outline: none;
        border-color: var(--primary-purple);
    }

    .discount-message {
        font-size: var(--font-size-sm);
        padding: var(--spacing-sm);
        border-radius: var(--radius-md);
    }

    .discount-message.success {
        background-color: rgba(76, 175, 80, 0.1);
        color: var(--success-color);
        border: 1px solid rgba(76, 175, 80, 0.2);
    }

    .discount-message.error {
        background-color: rgba(244, 67, 54, 0.1);
        color: var(--error-color);
        border: 1px solid rgba(244, 67, 54, 0.2);
    }

    /* Security Notice */
    .security-notice {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .security-icon {
        font-size: var(--font-size-2xl);
        color: var(--success-color);
    }

    .security-text h5 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .security-text p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .cart-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .cart-summary {
            order: -1;
        }
    }

    @media (max-width: 768px) {
        .cart-item {
            grid-template-columns: 80px 1fr;
            gap: var(--spacing-md);
            position: relative;
        }

        .cart-item-image {
            width: 80px;
            height: 80px;
        }

        .cart-item-quantity,
        .cart-item-total {
            grid-column: 2;
            justify-self: start;
        }

        .cart-item-actions {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            flex-direction: row;
        }

        .quantity-controls {
            width: fit-content;
        }

        .empty-cart-actions {
            flex-direction: column;
            align-items: center;
        }
    }

    @media (max-width: 480px) {
        .cart-container {
            padding: var(--spacing-md) 0;
        }

        .cart-item {
            padding: var(--spacing-md);
        }

        .summary-card,
        .discount-card,
        .security-notice {
            padding: var(--spacing-md);
        }
    }
</style>

<script>
    // Update quantity
    function updateQuantity(productId, change, isDirectInput = false) {
        let newQuantity;

        if (isDirectInput) {
            newQuantity = parseInt(change);
        } else {
            const currentInput = document.querySelector(`[data-product-id="${productId}"] .quantity-input`);
            const currentQuantity = parseInt(currentInput.value);
            newQuantity = currentQuantity + change;
        }

        if (newQuantity < 1) {
            newQuantity = 1;
        }

        if (newQuantity > 99) {
            newQuantity = 99;
        }

        // Update the input value
        const input = document.querySelector(`[data-product-id="${productId}"] .quantity-input`);
        input.value = newQuantity;

        // Send AJAX request to update cart
        fetch('/cart/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `product_id=${productId}&quantity=${newQuantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update cart count in header
                    updateCartCount(data.cartCount || 0);

                    // Update item total
                    const itemTotal = document.querySelector(`[data-product-id="${productId}"] .item-total`);
                    if (itemTotal) {
                        itemTotal.textContent = `$${data.itemTotal}`;
                    }

                    // Update cart total
                    const cartTotal = document.querySelector('.total-amount');
                    if (cartTotal) {
                        cartTotal.textContent = `$${data.cartTotal}`;
                    }

                    showNotification('Cart updated successfully', 'success');
                } else {
                    showNotification(data.message || 'Failed to update cart', 'error');
                }
            })
            .catch(error => {
                showNotification('An error occurred. Please try again.', 'error');
            });
    }

    // Remove item from cart
    function removeItem(productId) {
        if (confirm('Are you sure you want to remove this item from your cart?')) {
            fetch(`/cart/remove/${productId}`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the item from DOM
                        const cartItem = document.querySelector(`[data-product-id="${productId}"]`);
                        cartItem.style.opacity = '0';
                        cartItem.style.transform = 'translateX(-100%)';

                        setTimeout(() => {
                            cartItem.remove();

                            // Update cart count
                            updateCartCount(data.cartCount || 0);

                            // Check if cart is empty
                            const remainingItems = document.querySelectorAll('.cart-item');
                            if (remainingItems.length === 0) {
                                location.reload(); // Reload to show empty cart
                            }
                        }, 300);

                        showNotification('Item removed from cart', 'success');
                    } else {
                        showNotification(data.message || 'Failed to remove item', 'error');
                    }
                })
                .catch(error => {
                    showNotification('An error occurred. Please try again.', 'error');
                });
        }
    }

    // Clear cart
    function clearCart() {
        if (confirm('Are you sure you want to clear your entire cart?')) {
            fetch('/cart/clear', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload(); // Reload to show empty cart
                    } else {
                        showNotification(data.message || 'Failed to clear cart', 'error');
                    }
                })
                .catch(error => {
                    showNotification('An error occurred. Please try again.', 'error');
                });
        }
    }

    // Move item to wishlist
    function moveToWishlist(productId) {
        fetch('/wishlist/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `product_id=${productId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove from cart and add to wishlist
                    removeItem(productId);
                    showNotification('Item moved to wishlist', 'success');
                } else {
                    showNotification(data.message || 'Failed to move item to wishlist', 'error');
                }
            })
            .catch(error => {
                showNotification('An error occurred. Please try again.', 'error');
            });
    }

    // Apply discount code
    function applyDiscount(event) {
        event.preventDefault();

        const discountCode = document.getElementById('discountCode').value.trim();
        const messageDiv = document.getElementById('discountMessage');

        if (!discountCode) {
            messageDiv.textContent = 'Please enter a discount code';
            messageDiv.className = 'discount-message error';
            return;
        }

        fetch('/cart/apply-discount', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `discount_code=${discountCode}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageDiv.textContent = data.message;
                    messageDiv.className = 'discount-message success';
                    document.getElementById('discountCode').value = '';

                    // Update cart total if provided
                    if (data.cartTotal) {
                        const cartTotal = document.querySelector('.total-amount');
                        if (cartTotal) {
                            cartTotal.textContent = `$${data.cartTotal}`;
                        }
                    }
                } else {
                    messageDiv.textContent = data.message;
                    messageDiv.className = 'discount-message error';
                }
            })
            .catch(error => {
                messageDiv.textContent = 'An error occurred. Please try again.';
                messageDiv.className = 'discount-message error';
            });
    }

    // Add smooth animations for cart items
    document.querySelectorAll('.cart-item').forEach(item => {
        item.style.transition = 'all 0.3s ease';
    });
</script>