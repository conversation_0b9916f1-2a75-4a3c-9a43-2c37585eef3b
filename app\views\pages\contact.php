<div class="contact-page">
    <div class="container">
        <!-- <PERSON> Header -->
        <div class="page-header">
            <h1>Contact Us</h1>
            <p>Get in touch with our team. We're here to help!</p>
        </div>

        <div class="contact-content">
            <!-- Contact Information -->
            <div class="contact-info">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="info-content">
                        <h3>Our Location</h3>
                        <p>123 Cleanance Street<br>Lab District, CL 12345<br>United States</p>
                    </div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="info-content">
                        <h3>Phone Number</h3>
                        <p>+****************<br>Mon-Fri: 9AM-6PM EST</p>
                    </div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="info-content">
                        <h3>Email Address</h3>
                        <p><EMAIL><br><EMAIL></p>
                    </div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="info-content">
                        <h3>Business Hours</h3>
                        <p>Monday - Friday: 9AM-6PM<br>Saturday: 10AM-4PM<br>Sunday: Closed</p>
                    </div>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="contact-form-container">
                <div class="form-card">
                    <h2>Send us a Message</h2>
                    <p>Fill out the form below and we'll get back to you as soon as possible.</p>

                    <?php if (isset($success)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <?= htmlspecialchars($success) ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <form class="contact-form" method="POST" action="<?= UrlHelper::url('/contact') ?>">
                        <input type="hidden" name="csrf_token" value="<?= $this->generateCSRFToken() ?>">

                        <div class="form-row">
                            <div class="form-group">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input
                                    type="text"
                                    id="first_name"
                                    name="first_name"
                                    class="form-input"
                                    value="<?= htmlspecialchars($formData['first_name'] ?? '') ?>"
                                    required>
                            </div>

                            <div class="form-group">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input
                                    type="text"
                                    id="last_name"
                                    name="last_name"
                                    class="form-input"
                                    value="<?= htmlspecialchars($formData['last_name'] ?? '') ?>"
                                    required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                class="form-input"
                                value="<?= htmlspecialchars($formData['email'] ?? '') ?>"
                                required>
                        </div>

                        <div class="form-group">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input
                                type="tel"
                                id="phone"
                                name="phone"
                                class="form-input"
                                value="<?= htmlspecialchars($formData['phone'] ?? '') ?>">
                        </div>

                        <div class="form-group">
                            <label for="subject" class="form-label">Subject *</label>
                            <select id="subject" name="subject" class="form-input" required>
                                <option value="">Select a subject</option>
                                <option value="general" <?= ($formData['subject'] ?? '') === 'general' ? 'selected' : '' ?>>General Inquiry</option>
                                <option value="support" <?= ($formData['subject'] ?? '') === 'support' ? 'selected' : '' ?>>Technical Support</option>
                                <option value="order" <?= ($formData['subject'] ?? '') === 'order' ? 'selected' : '' ?>>Order Status</option>
                                <option value="return" <?= ($formData['subject'] ?? '') === 'return' ? 'selected' : '' ?>>Returns & Refunds</option>
                                <option value="partnership" <?= ($formData['subject'] ?? '') === 'partnership' ? 'selected' : '' ?>>Partnership</option>
                                <option value="other" <?= ($formData['subject'] ?? '') === 'other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message" class="form-label">Message *</label>
                            <textarea
                                id="message"
                                name="message"
                                class="form-input"
                                rows="6"
                                placeholder="Please describe your inquiry in detail..."
                                required><?= htmlspecialchars($formData['message'] ?? '') ?></textarea>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="newsletter" value="1" <?= isset($formData['newsletter']) ? 'checked' : '' ?>>
                                <span class="checkmark"></span>
                                Subscribe to our newsletter for updates and exclusive offers
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="faq-section">
            <h2>Frequently Asked Questions</h2>
            <div class="faq-grid">
                <div class="faq-item">
                    <h3>How can I track my order?</h3>
                    <p>You can track your order by logging into your account and visiting the "My Orders" section, or by using the tracking number provided in your order confirmation email.</p>
                </div>

                <div class="faq-item">
                    <h3>What is your return policy?</h3>
                    <p>We offer a 30-day return policy for most items. Products must be in their original condition and packaging. Please visit our Returns page for detailed information.</p>
                </div>

                <div class="faq-item">
                    <h3>Do you ship internationally?</h3>
                    <p>Yes, we ship to most countries worldwide. Shipping costs and delivery times vary by location. You can check shipping options during checkout.</p>
                </div>

                <div class="faq-item">
                    <h3>How can I change or cancel my order?</h3>
                    <p>Orders can be modified or cancelled within 2 hours of placement. Please contact our customer service team immediately for assistance.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .contact-page {
        padding: var(--spacing-xl) 0;
        background-color: var(--dark-surface);
        min-height: 100vh;
    }

    .page-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .page-header h1 {
        font-size: var(--font-size-4xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-header p {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    .contact-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        margin-bottom: var(--spacing-2xl);
    }

    /* Contact Information */
    .contact-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .info-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        transition: all var(--transition-fast);
    }

    .info-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .info-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .info-icon i {
        font-size: var(--font-size-lg);
        color: white;
    }

    .info-content h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .info-content p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Contact Form */
    .contact-form-container {
        display: flex;
        justify-content: center;
    }

    .form-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        width: 100%;
        max-width: 600px;
    }

    .form-card h2 {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .form-card>p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
    }

    .contact-form {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .form-input {
        padding: var(--spacing-md);
        background-color: var(--dark-surface);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all var(--transition-fast);
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
    }

    .form-input::placeholder {
        color: var(--text-tertiary);
    }

    textarea.form-input {
        resize: vertical;
        min-height: 120px;
    }

    /* Checkbox Styles */
    .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-sm);
        cursor: pointer;
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        line-height: 1.4;
    }

    .checkbox-label input[type="checkbox"] {
        display: none;
    }

    .checkmark {
        width: 20px;
        height: 20px;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-sm);
        position: relative;
        flex-shrink: 0;
        margin-top: 0.125rem;
        transition: all var(--transition-fast);
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark {
        background-color: var(--primary-purple);
        border-color: var(--primary-purple);
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 0.75rem;
        font-weight: bold;
    }

    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-lg {
        padding: var(--spacing-lg) var(--spacing-xl);
        font-size: var(--font-size-lg);
    }

    /* Alert Styles */
    .alert {
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    .alert-success {
        background-color: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: var(--success-color);
    }

    .alert-error {
        background-color: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: var(--error-color);
    }

    /* FAQ Section */
    .faq-section {
        margin-top: var(--spacing-3xl);
    }

    .faq-section h2 {
        text-align: center;
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xl);
    }

    .faq-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    .faq-item {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        transition: all var(--transition-fast);
    }

    .faq-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .faq-item h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .faq-item p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .contact-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }
    }

    @media (max-width: 768px) {
        .contact-page {
            padding: var(--spacing-lg) 0;
        }

        .page-header h1 {
            font-size: var(--font-size-3xl);
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .form-card {
            padding: var(--spacing-xl);
        }

        .faq-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Light Mode Support */
    .light-mode .contact-page {
        background-color: var(--light-surface);
    }

    .light-mode .info-card,
    .light-mode .form-card,
    .light-mode .faq-item {
        background-color: var(--light-card);
        border-color: var(--light-border);
    }

    .light-mode .form-input {
        background-color: var(--light-surface);
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .form-input:focus {
        background-color: var(--light-surface);
    }

    .light-mode .checkmark {
        border-color: var(--light-border);
    }
</style>