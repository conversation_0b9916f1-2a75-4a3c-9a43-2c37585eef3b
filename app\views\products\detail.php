<div class="product-detail-page">
    <div class="container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb-nav">
            <a href="<?= UrlHelper::url('/') ?>">Home</a>
            <span class="separator">/</span>
            <a href="<?= UrlHelper::url('/products') ?>">Products</a>
            <span class="separator">/</span>
            <?php if ($product['category_name']): ?>
                <a href="<?= UrlHelper::url('/products/' . $product['category_slug']) ?>"><?= htmlspecialchars($product['category_name']) ?></a>
                <span class="separator">/</span>
            <?php endif; ?>
            <span class="current"><?= htmlspecialchars($product['name']) ?></span>
        </nav>

        <div class="product-detail-layout">
            <!-- Product Images Section -->
            <div class="product-images-section">
                <div class="main-image-container">
                    <img src="<?= UrlHelper::url('uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                        alt="<?= htmlspecialchars($product['name']) ?>"
                        class="main-product-image"
                        id="mainImage">
                    <?php if ($product['sale_price']): ?>
                        <div class="sale-badge">
                            <span>-<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%</span>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if (!empty($images) && count($images) > 1): ?>
                    <div class="image-thumbnails-container">
                        <div class="thumbnails-scroll">
                            <?php foreach ($images as $img): ?>
                                <div class="thumbnail-item" onclick="changeMainImage('<?= UrlHelper::url('uploads/products/' . $img['image_path']) ?>')">
                                    <img src="<?= UrlHelper::url('uploads/products/' . $img['image_path']) ?>"
                                        alt="Product thumbnail"
                                        class="thumbnail-image">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Product Info Section -->
            <div class="product-info-section">
                <div class="product-header">
                    <h1 class="product-title"><?= htmlspecialchars($product['name']) ?></h1>
                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-tag"></i>
                            <span><?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-box"></i>
                            <span>SKU: <?= htmlspecialchars($product['sku'] ?? 'N/A') ?></span>
                        </div>
                    </div>
                </div>

                <!-- Rating Section -->
                <div class="product-rating-section">
                    <div class="rating-stars">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star <?= $i <= $avgRating ? 'filled' : '' ?>"></i>
                        <?php endfor; ?>
                    </div>
                    <div class="rating-info">
                        <span class="rating-value"><?= number_format($avgRating, 1) ?></span>
                        <span class="rating-count">(<?= count($reviews) ?> reviews)</span>
                    </div>
                    <div class="stock-status <?= $product['stock_quantity'] > 0 ? 'in-stock' : 'out-of-stock' ?>">
                        <i class="fas fa-<?= $product['stock_quantity'] > 0 ? 'check' : 'times' ?>-circle"></i>
                        <span><?= $product['stock_quantity'] > 0 ? 'In Stock' : 'Out of Stock' ?></span>
                    </div>
                </div>

                <!-- Price Section -->
                <div class="product-price-section">
                    <?php if ($product['sale_price']): ?>
                        <div class="price-display">
                            <span class="current-price">$<?= number_format($product['sale_price'], 2) ?></span>
                            <span class="original-price">$<?= number_format($product['price'], 2) ?></span>
                            <span class="savings">Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?></span>
                        </div>
                    <?php else: ?>
                        <div class="price-display">
                            <span class="current-price">$<?= number_format($product['price'], 2) ?></span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Description -->
                <div class="product-description-section">
                    <h3>Description</h3>
                    <div class="description-content">
                        <?= nl2br(htmlspecialchars($product['description'])) ?>
                    </div>
                </div>

                <!-- Add to Cart Section -->
                <div class="add-to-cart-section">
                    <form class="add-to-cart-form" method="POST" action="<?= UrlHelper::url('/cart/add') ?>">
                        <input type="hidden" name="product_id" value="<?= $product['id'] ?>">

                        <div class="quantity-selector">
                            <label for="quantity">Quantity:</label>
                            <div class="quantity-controls">
                                <button type="button" class="qty-btn" onclick="changeQuantity(-1)">-</button>
                                <input type="number" id="quantity" name="quantity" value="1" min="1" max="<?= $product['stock_quantity'] ?>" readonly>
                                <button type="button" class="qty-btn" onclick="changeQuantity(1)">+</button>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button type="submit" class="btn btn-primary btn-lg add-to-cart-btn" <?= $product['stock_quantity'] <= 0 ? 'disabled' : '' ?>>
                                <i class="fas fa-shopping-cart"></i>
                                <span>Add to Cart</span>
                            </button>

                            <button type="button" class="btn btn-outline btn-lg wishlist-btn <?= $inWishlist ? 'in-wishlist' : '' ?>"
                                data-product-id="<?= $product['id'] ?>"
                                onclick="toggleWishlist(<?= $product['id'] ?>)">
                                <i class="fas fa-heart"></i>
                                <span><?= $inWishlist ? 'Remove from Wishlist' : 'Add to Wishlist' ?></span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Product Features -->
                <div class="product-features">
                    <div class="feature-item">
                        <i class="fas fa-shipping-fast"></i>
                        <span>Free Shipping</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure Payment</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-undo"></i>
                        <span>30-Day Returns</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-headset"></i>
                        <span>24/7 Support</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Tabs -->
        <div class="product-tabs-section">
            <div class="tabs-container">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="showTab('reviews')">Reviews (<?= count($reviews) ?>)</button>
                    <button class="tab-btn" onclick="showTab('specifications')">Specifications</button>
                    <button class="tab-btn" onclick="showTab('shipping')">Shipping & Returns</button>
                </div>

                <!-- Reviews Tab -->
                <div id="reviews-tab" class="tab-content active">
                    <div class="reviews-header">
                        <h3>Customer Reviews</h3>
                        <button class="btn btn-primary" onclick="showReviewForm()">Write a Review</button>
                    </div>

                    <?php if (empty($reviews)): ?>
                        <div class="no-reviews">
                            <i class="fas fa-comment-slash"></i>
                            <p>No reviews yet. Be the first to review this product!</p>
                        </div>
                    <?php else: ?>
                        <div class="reviews-list">
                            <?php foreach ($reviews as $review): ?>
                                <div class="review-item">
                                    <div class="review-header">
                                        <div class="reviewer-info">
                                            <div class="reviewer-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="reviewer-details">
                                                <span class="reviewer-name"><?= htmlspecialchars($review['first_name'] . ' ' . $review['last_name']) ?></span>
                                                <span class="review-date"><?= date('M d, Y', strtotime($review['created_at'])) ?></span>
                                            </div>
                                        </div>
                                        <div class="review-rating">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star <?= $i <= $review['rating'] ? 'filled' : '' ?>"></i>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    <div class="review-content">
                                        <p><?= nl2br(htmlspecialchars($review['comment'])) ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Add Review Form -->
                    <div id="review-form" class="review-form" style="display: none;">
                        <h4>Write a Review</h4>
                        <form method="POST" action="<?= UrlHelper::url('/review/add') ?>">
                            <input type="hidden" name="product_id" value="<?= $product['id'] ?>">

                            <div class="form-group">
                                <label for="rating">Rating:</label>
                                <div class="rating-input">
                                    <?php for ($i = 5; $i >= 1; $i--): ?>
                                        <input type="radio" name="rating" id="star<?= $i ?>" value="<?= $i ?>" required>
                                        <label for="star<?= $i ?>"><i class="fas fa-star"></i></label>
                                    <?php endfor; ?>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="comment">Your Review:</label>
                                <textarea name="comment" id="comment" rows="4" placeholder="Share your experience with this product..." required></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">Submit Review</button>
                                <button type="button" class="btn btn-outline" onclick="hideReviewForm()">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Specifications Tab -->
                <div id="specifications-tab" class="tab-content">
                    <div class="specifications-content">
                        <h3>Product Specifications</h3>
                        <div class="specs-grid">
                            <div class="spec-item">
                                <span class="spec-label">SKU:</span>
                                <span class="spec-value"><?= htmlspecialchars($product['sku'] ?? 'N/A') ?></span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">Category:</span>
                                <span class="spec-value"><?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?></span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">Weight:</span>
                                <span class="spec-value"><?= htmlspecialchars($product['weight'] ?? 'N/A') ?></span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">Dimensions:</span>
                                <span class="spec-value"><?= htmlspecialchars($product['dimensions'] ?? 'N/A') ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Tab -->
                <div id="shipping-tab" class="tab-content">
                    <div class="shipping-content">
                        <h3>Shipping & Returns</h3>
                        <div class="shipping-info">
                            <div class="info-item">
                                <i class="fas fa-shipping-fast"></i>
                                <div>
                                    <h4>Free Shipping</h4>
                                    <p>Free standard shipping on orders over $50</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-undo"></i>
                                <div>
                                    <h4>30-Day Returns</h4>
                                    <p>Easy returns within 30 days of purchase</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-shield-alt"></i>
                                <div>
                                    <h4>Secure Payment</h4>
                                    <p>Your payment information is secure</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Products -->
        <?php if (!empty($relatedProducts)): ?>
            <div class="related-products-section">
                <h2>Related Products</h2>
                <div class="related-products-grid">
                    <?php foreach ($relatedProducts as $rel): ?>
                        <div class="related-product-card">
                            <a href="<?= UrlHelper::url('/product/' . $rel['slug']) ?>" class="product-link">
                                <div class="product-image">
                                    <img src="<?= UrlHelper::url('uploads/products/' . ($rel['primary_image'] ?? 'default.jpg')) ?>"
                                        alt="<?= htmlspecialchars($rel['name']) ?>">
                                    <?php if ($rel['sale_price']): ?>
                                        <div class="sale-badge-small">
                                            <span>-<?= round((($rel['price'] - $rel['sale_price']) / $rel['price']) * 100) ?>%</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="product-details">
                                    <h3 class="product-name"><?= htmlspecialchars($rel['name']) ?></h3>
                                    <div class="product-price">
                                        <?php if ($rel['sale_price']): ?>
                                            <span class="current-price">$<?= number_format($rel['sale_price'], 2) ?></span>
                                            <span class="original-price">$<?= number_format($rel['price'], 2) ?></span>
                                        <?php else: ?>
                                            <span class="current-price">$<?= number_format($rel['price'], 2) ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    .product-detail-page {
        padding: var(--spacing-xl) 0;
        background: linear-gradient(135deg, var(--dark-surface) 0%, var(--dark-card) 100%);
        min-height: 100vh;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 var(--spacing-lg);
    }

    /* Breadcrumb */
    .breadcrumb-nav {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-xl);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
    }

    .breadcrumb-nav a {
        color: var(--text-secondary);
        text-decoration: none;
        transition: color var(--transition-fast);
    }

    .breadcrumb-nav a:hover {
        color: var(--primary-purple);
    }

    .breadcrumb-nav .separator {
        color: var(--text-tertiary);
    }

    .breadcrumb-nav .current {
        color: var(--text-primary);
        font-weight: 600;
    }

    /* Main Layout */
    .product-detail-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3xl);
        margin-bottom: var(--spacing-4xl);
    }

    /* Images Section */
    .product-images-section {
        background: var(--dark-card);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-lg);
    }

    .main-image-container {
        position: relative;
        margin-bottom: var(--spacing-lg);
    }

    .main-product-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        transition: transform var(--transition-fast);
    }

    .main-product-image:hover {
        transform: scale(1.02);
    }

    .sale-badge {
        position: absolute;
        top: var(--spacing-md);
        left: var(--spacing-md);
        background: var(--error-color);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-weight: 600;
        font-size: var(--font-size-sm);
    }

    .image-thumbnails-container {
        margin-top: var(--spacing-lg);
    }

    .thumbnails-scroll {
        display: flex;
        gap: var(--spacing-sm);
        overflow-x: auto;
        padding: var(--spacing-xs);
    }

    .thumbnail-item {
        flex-shrink: 0;
        cursor: pointer;
        border-radius: var(--radius-md);
        overflow: hidden;
        border: 2px solid transparent;
        transition: border var(--transition-fast);
    }

    .thumbnail-item:hover {
        border-color: var(--primary-purple);
    }

    .thumbnail-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
    }

    /* Info Section */
    .product-info-section {
        background: var(--dark-card);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-lg);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xl);
    }

    .product-header {
        border-bottom: 1px solid var(--border-color);
        padding-bottom: var(--spacing-lg);
    }

    .product-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        line-height: 1.2;
    }

    .product-meta {
        display: flex;
        gap: var(--spacing-lg);
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .meta-item i {
        color: var(--primary-purple);
    }

    /* Rating Section */
    .product-rating-section {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        flex-wrap: wrap;
    }

    .rating-stars {
        display: flex;
        gap: var(--spacing-xs);
    }

    .rating-stars .fa-star {
        color: var(--text-tertiary);
        font-size: var(--font-size-lg);
    }

    .rating-stars .fa-star.filled {
        color: #ffd700;
    }

    .rating-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .rating-value {
        font-weight: 600;
        color: var(--text-primary);
    }

    .rating-count {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .stock-status {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
        font-weight: 600;
    }

    .stock-status.in-stock {
        color: var(--success-color);
    }

    .stock-status.out-of-stock {
        color: var(--error-color);
    }

    /* Price Section */
    .product-price-section {
        background: linear-gradient(135deg, var(--primary-purple) 0%, #9c27b0 100%);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        color: white;
    }

    .price-display {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex-wrap: wrap;
    }

    .current-price {
        font-size: var(--font-size-2xl);
        font-weight: 700;
    }

    .original-price {
        font-size: var(--font-size-lg);
        text-decoration: line-through;
        opacity: 0.8;
    }

    .savings {
        background: rgba(255, 255, 255, 0.2);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: 600;
    }

    /* Description */
    .product-description-section h3 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        font-size: var(--font-size-xl);
    }

    .description-content {
        color: var(--text-secondary);
        line-height: 1.6;
    }

    /* Add to Cart Section */
    .add-to-cart-section {
        border-top: 1px solid var(--border-color);
        padding-top: var(--spacing-lg);
    }

    .quantity-selector {
        margin-bottom: var(--spacing-lg);
    }

    .quantity-selector label {
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--text-primary);
        font-weight: 600;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        max-width: 150px;
    }

    .qty-btn {
        width: 40px;
        height: 40px;
        border: 1px solid var(--border-color);
        background: var(--dark-surface);
        color: var(--text-primary);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .qty-btn:hover {
        background: var(--primary-purple);
        color: white;
        border-color: var(--primary-purple);
    }

    .quantity-controls input {
        flex: 1;
        height: 40px;
        text-align: center;
        border: 1px solid var(--border-color);
        background: var(--dark-surface);
        color: var(--text-primary);
        border-radius: var(--radius-md);
    }

    .action-buttons {
        display: flex;
        gap: var(--spacing-md);
        flex-wrap: wrap;
    }

    .add-to-cart-btn {
        flex: 1;
        min-width: 200px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, #9c27b0 100%);
        border: none;
        color: white;
        font-weight: 600;
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .add-to-cart-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .add-to-cart-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .wishlist-btn {
        flex: 1;
        min-width: 200px;
        height: 50px;
        background: transparent;
        border: 2px solid var(--primary-purple);
        color: var(--primary-purple);
        font-weight: 600;
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .wishlist-btn:hover,
    .wishlist-btn.in-wishlist {
        background: var(--primary-purple);
        color: white;
    }

    /* Product Features */
    .product-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        background: var(--dark-surface);
        border-radius: var(--radius-md);
        color: var(--text-secondary);
    }

    .feature-item i {
        color: var(--primary-purple);
        font-size: var(--font-size-lg);
    }

    /* Tabs Section */
    .product-tabs-section {
        background: var(--dark-card);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-lg);
        margin-bottom: var(--spacing-4xl);
    }

    .tabs-container {
        width: 100%;
    }

    .tab-buttons {
        display: flex;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-xl);
        border-bottom: 1px solid var(--border-color);
    }

    .tab-btn {
        padding: var(--spacing-md) var(--spacing-lg);
        background: transparent;
        border: none;
        color: var(--text-secondary);
        font-weight: 600;
        cursor: pointer;
        transition: all var(--transition-fast);
        border-bottom: 2px solid transparent;
    }

    .tab-btn:hover,
    .tab-btn.active {
        color: var(--primary-purple);
        border-bottom-color: var(--primary-purple);
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    /* Reviews */
    .reviews-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xl);
    }

    .reviews-header h3 {
        color: var(--text-primary);
        font-size: var(--font-size-xl);
    }

    .no-reviews {
        text-align: center;
        padding: var(--spacing-3xl);
        color: var(--text-secondary);
    }

    .no-reviews i {
        font-size: var(--font-size-4xl);
        margin-bottom: var(--spacing-lg);
        color: var(--text-tertiary);
    }

    .reviews-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .review-item {
        background: var(--dark-surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        border: 1px solid var(--border-color);
    }

    .review-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-md);
    }

    .reviewer-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .reviewer-avatar {
        width: 40px;
        height: 40px;
        background: var(--primary-purple);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .reviewer-details {
        display: flex;
        flex-direction: column;
    }

    .reviewer-name {
        font-weight: 600;
        color: var(--text-primary);
    }

    .review-date {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
    }

    .review-rating {
        display: flex;
        gap: var(--spacing-xs);
    }

    .review-rating .fa-star {
        color: var(--text-tertiary);
        font-size: var(--font-size-sm);
    }

    .review-rating .fa-star.filled {
        color: #ffd700;
    }

    .review-content {
        color: var(--text-secondary);
        line-height: 1.6;
    }

    /* Review Form */
    .review-form {
        background: var(--dark-surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        margin-top: var(--spacing-xl);
        border: 1px solid var(--border-color);
    }

    .review-form h4 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .form-group label {
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--text-primary);
        font-weight: 600;
    }

    .rating-input {
        display: flex;
        gap: var(--spacing-xs);
        flex-direction: row-reverse;
    }

    .rating-input input {
        display: none;
    }

    .rating-input label {
        cursor: pointer;
        font-size: var(--font-size-xl);
        color: var(--text-tertiary);
        transition: color var(--transition-fast);
    }

    .rating-input input:checked~label,
    .rating-input label:hover,
    .rating-input label:hover~label {
        color: #ffd700;
    }

    .review-form textarea {
        width: 100%;
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        background: var(--dark-surface);
        color: var(--text-primary);
        border-radius: var(--radius-md);
        resize: vertical;
    }

    .form-actions {
        display: flex;
        gap: var(--spacing-md);
    }

    /* Specifications */
    .specifications-content h3 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .specs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .spec-item {
        display: flex;
        justify-content: space-between;
        padding: var(--spacing-md);
        background: var(--dark-surface);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
    }

    .spec-label {
        color: var(--text-secondary);
        font-weight: 600;
    }

    .spec-value {
        color: var(--text-primary);
    }

    /* Shipping */
    .shipping-content h3 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .shipping-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    .info-item {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
        background: var(--dark-surface);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
    }

    .info-item i {
        color: var(--primary-purple);
        font-size: var(--font-size-xl);
        margin-top: var(--spacing-xs);
    }

    .info-item h4 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .info-item p {
        color: var(--text-secondary);
        line-height: 1.5;
    }

    /* Related Products */
    .related-products-section {
        margin-top: var(--spacing-4xl);
    }

    .related-products-section h2 {
        color: var(--text-primary);
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-xl);
        text-align: center;
    }

    .related-products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .related-product-card {
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        transition: transform var(--transition-fast);
    }

    .related-product-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .product-link {
        text-decoration: none;
        color: inherit;
        display: block;
    }

    .product-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-fast);
    }

    .related-product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .sale-badge-small {
        position: absolute;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        background: var(--error-color);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    .product-details {
        padding: var(--spacing-lg);
    }

    .product-name {
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-sm);
        line-height: 1.3;
    }

    .product-price {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .product-price .current-price {
        color: var(--primary-purple);
        font-weight: 600;
        font-size: var(--font-size-lg);
    }

    .product-price .original-price {
        color: var(--text-secondary);
        text-decoration: line-through;
        font-size: var(--font-size-sm);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .product-detail-layout {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }

        .action-buttons {
            flex-direction: column;
        }

        .add-to-cart-btn,
        .wishlist-btn {
            min-width: 100%;
        }

        .product-features {
            grid-template-columns: 1fr;
        }

        .tab-buttons {
            flex-direction: column;
        }

        .reviews-header {
            flex-direction: column;
            gap: var(--spacing-md);
            align-items: flex-start;
        }

        .related-products-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
    }

    @media (max-width: 480px) {
        .container {
            padding: 0 var(--spacing-md);
        }

        .product-title {
            font-size: var(--font-size-2xl);
        }

        .main-product-image {
            height: 300px;
        }

        .price-display {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .related-products-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<script>
    // Change main image when thumbnail is clicked
    function changeMainImage(imageSrc) {
        document.getElementById('mainImage').src = imageSrc;

        // Update active thumbnail
        document.querySelectorAll('.thumbnail-item').forEach(item => {
            item.style.borderColor = 'transparent';
        });
        event.currentTarget.style.borderColor = 'var(--primary-purple)';
    }

    // Quantity controls
    function changeQuantity(delta) {
        const input = document.getElementById('quantity');
        const newValue = Math.max(1, Math.min(parseInt(input.value) + delta, parseInt(input.max)));
        input.value = newValue;
    }

    // Wishlist toggle
    function toggleWishlist(productId) {
        const btn = event.currentTarget;
        const isInWishlist = btn.classList.contains('in-wishlist');

        fetch('<?= UrlHelper::url('/wishlist/') ?>' + (isInWishlist ? 'remove' : 'add'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'product_id=' + productId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (isInWishlist) {
                        btn.classList.remove('in-wishlist');
                        btn.querySelector('span').textContent = 'Add to Wishlist';
                    } else {
                        btn.classList.add('in-wishlist');
                        btn.querySelector('span').textContent = 'Remove from Wishlist';
                    }

                    // Show notification
                    showNotification(data.message, 'success');
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('An error occurred', 'error');
            });
    }

    // Tab functionality
    function showTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Show selected tab content
        document.getElementById(tabName + '-tab').classList.add('active');

        // Add active class to clicked button
        event.currentTarget.classList.add('active');
    }

    // Review form toggle
    function showReviewForm() {
        document.getElementById('review-form').style.display = 'block';
        document.getElementById('review-form').scrollIntoView({
            behavior: 'smooth'
        });
    }

    function hideReviewForm() {
        document.getElementById('review-form').style.display = 'none';
    }

    // Notification function
    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            ${type === 'success' ? 'background: var(--success-color);' : 'background: var(--error-color);'}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
</script>