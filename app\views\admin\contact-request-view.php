<section class="admin-section">
    <h1>Contact Request #<?= $request['id'] ?></h1>
    <div class="contact-details">
        <p><strong>Name:</strong> <?= htmlspecialchars($request['name']) ?></p>
        <p><strong>Email:</strong> <?= htmlspecialchars($request['email']) ?></p>
        <p><strong>Subject:</strong> <?= htmlspecialchars($request['subject']) ?></p>
        <p><strong>Status:</strong> <?= ucfirst($request['status']) ?></p>
        <p><strong>Date:</strong> <?= $request['created_at'] ?></p>
        <p><strong>Message:</strong></p>
        <div class="contact-message"><?= nl2br(htmlspecialchars($request['message'])) ?></div>
    </div>
    <div class="contact-actions">
        <?php if ($request['status'] !== 'archived'): ?>
            <form action="/admin/contact-requests/archive/<?= $request['id'] ?>" method="POST" style="display:inline;">
                <button class="btn btn-sm">Archive</button>
            </form>
        <?php endif; ?>
        <form action="/admin/contact-requests/delete/<?= $request['id'] ?>" method="POST" style="display:inline;">
            <button class="btn btn-sm btn-danger" onclick="return confirm('Delete this request?')">Delete</button>
        </form>
        <a href="/admin/contact-requests" class="btn btn-sm">Back to List</a>
    </div>
</section>
<style>
    .admin-section {
        padding: 40px 0;
        max-width: 700px;
        margin: 0 auto;
    }

    .contact-details {
        background: var(--dark-card);
        padding: 24px;
        border-radius: 12px;
        margin-bottom: 24px;
    }

    .contact-message {
        background: var(--dark-surface);
        padding: 16px;
        border-radius: 8px;
        color: var(--text-primary);
        margin-top: 8px;
    }

    .contact-actions {
        margin-top: 16px;
    }

    .btn-sm {
        padding: 4px 12px;
        font-size: 0.95em;
    }

    .btn-danger {
        background: #e53935;
        color: #fff;
    }

    .btn-danger:hover {
        background: #b71c1c;
    }
</style>