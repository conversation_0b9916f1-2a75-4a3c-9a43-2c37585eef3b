-- Cleanance Lab E-commerce Database Schema
-- Create database if not exists
CREATE DATABASE IF NOT EXISTS cleanance_lab;
USE cleanance_lab;
-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    is_admin B<PERSON>OLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Categories table
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    image VARCHAR(255),
    parent_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE
    SET NULL
);
-- Products table
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    price DECIMAL(10, 2) NOT NULL,
    sale_price DECIMAL(10, 2),
    cost_price DECIMAL(10, 2),
    sku VARCHAR(100) UNIQUE,
    stock_quantity INT DEFAULT 0,
    weight DECIMAL(8, 2),
    dimensions VARCHAR(100),
    category_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE
    SET NULL
);
-- Product images table
CREATE TABLE product_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
-- Product attributes table
CREATE TABLE product_attributes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    attribute_name VARCHAR(100) NOT NULL,
    attribute_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
-- Orders table
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INT,
    guest_email VARCHAR(100),
    status ENUM(
        'pending',
        'processing',
        'shipped',
        'delivered',
        'cancelled',
        'refunded'
    ) DEFAULT 'pending',
    subtotal DECIMAL(10, 2) NOT NULL,
    tax_amount DECIMAL(10, 2) DEFAULT 0,
    shipping_amount DECIMAL(10, 2) DEFAULT 0,
    discount_amount DECIMAL(10, 2) DEFAULT 0,
    total_amount DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    shipping_address TEXT,
    billing_address TEXT,
    notes TEXT,
    tracking_number VARCHAR(100),
    shipped_at DATETIME,
    delivered_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE
    SET NULL
);
-- Order items table
CREATE TABLE order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_sku VARCHAR(100),
    quantity INT NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
-- Shopping cart table
CREATE TABLE cart_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    session_id VARCHAR(255),
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
-- Wishlist table
CREATE TABLE wishlist_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_wishlist_item (user_id, product_id)
);
-- Product reviews table
CREATE TABLE product_reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL CHECK (
        rating >= 1
        AND rating <= 5
    ),
    title VARCHAR(255),
    comment TEXT,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
-- Discount codes table
CREATE TABLE discount_codes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    discount_type ENUM('percentage', 'fixed') NOT NULL,
    discount_value DECIMAL(10, 2) NOT NULL,
    minimum_order_amount DECIMAL(10, 2) DEFAULT 0,
    maximum_discount DECIMAL(10, 2),
    usage_limit INT,
    used_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    starts_at DATETIME,
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Discount code usage table
CREATE TABLE discount_code_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    discount_code_id INT NOT NULL,
    user_id INT NOT NULL,
    order_id INT NOT NULL,
    discount_amount DECIMAL(10, 2) NOT NULL,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (discount_code_id) REFERENCES discount_codes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);
-- Email templates table
CREATE TABLE email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    variables TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Settings table
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Contact requests table
CREATE TABLE IF NOT EXISTS contact_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'archived') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- Insert admin user
INSERT INTO users (
        email,
        password,
        first_name,
        last_name,
        is_admin,
        is_active,
        email_verified_at,
        created_at,
        updated_at
    )
VALUES (
        '<EMAIL>',
        '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
        -- password: admin123
        'Admin',
        'User',
        TRUE,
        TRUE,
        NOW(),
        NOW(),
        NOW()
    );
-- Insert default categories
INSERT INTO categories (name, slug, description, sort_order)
VALUES (
        'Electronics',
        'electronics',
        'Latest electronic devices and gadgets',
        1
    ),
    (
        'Fashion',
        'fashion',
        'Trendy clothing and accessories',
        2
    ),
    (
        'Home & Garden',
        'home-garden',
        'Everything for your home and garden',
        3
    ),
    (
        'Sports',
        'sports',
        'Sports equipment and activewear',
        4
    ),
    (
        'Books',
        'books',
        'Books, magazines, and educational materials',
        5
    );
-- Insert default settings
INSERT INTO settings (
        setting_key,
        setting_value,
        setting_type,
        description
    )
VALUES (
        'site_name',
        'Cleanance Lab',
        'string',
        'Website name'
    ),
    (
        'site_description',
        'Modern e-commerce platform',
        'string',
        'Website description'
    ),
    ('currency', 'USD', 'string', 'Default currency'),
    (
        'tax_rate',
        '8.5',
        'number',
        'Default tax rate percentage'
    ),
    (
        'shipping_cost',
        '10.00',
        'number',
        'Default shipping cost'
    ),
    (
        'free_shipping_threshold',
        '100.00',
        'number',
        'Minimum order amount for free shipping'
    ),
    (
        'items_per_page',
        '12',
        'number',
        'Number of products per page'
    ),
    (
        'enable_reviews',
        '1',
        'boolean',
        'Enable product reviews'
    ),
    (
        'enable_wishlist',
        '1',
        'boolean',
        'Enable wishlist feature'
    ),
    (
        'enable_discount_codes',
        '1',
        'boolean',
        'Enable discount codes'
    );
-- Insert sample products
INSERT INTO products (
        name,
        slug,
        description,
        short_description,
        price,
        category_id,
        stock_quantity,
        is_active,
        is_featured
    )
VALUES (
        'Wireless Bluetooth Headphones',
        'wireless-bluetooth-headphones',
        'High-quality wireless headphones with noise cancellation and long battery life.',
        'Premium wireless headphones with amazing sound quality.',
        89.99,
        1,
        50,
        TRUE,
        TRUE
    ),
    (
        'Smart Fitness Watch',
        'smart-fitness-watch',
        'Track your fitness goals with this advanced smartwatch featuring heart rate monitoring and GPS.',
        'Advanced fitness tracking with health monitoring.',
        199.99,
        1,
        30,
        TRUE,
        TRUE
    ),
    (
        'Organic Cotton T-Shirt',
        'organic-cotton-tshirt',
        'Comfortable and eco-friendly cotton t-shirt available in multiple colors and sizes.',
        'Soft and comfortable organic cotton t-shirt.',
        24.99,
        2,
        100,
        TRUE,
        FALSE
    ),
    (
        'Garden Tool Set',
        'garden-tool-set',
        'Complete set of essential garden tools for maintaining your outdoor space.',
        'Essential tools for every gardener.',
        49.99,
        3,
        25,
        TRUE,
        FALSE
    ),
    (
        'Yoga Mat Premium',
        'yoga-mat-premium',
        'Non-slip yoga mat made from eco-friendly materials, perfect for home workouts.',
        'Premium non-slip yoga mat for your practice.',
        34.99,
        4,
        75,
        TRUE,
        TRUE
    );
-- Insert sample discount codes
INSERT INTO discount_codes (
        code,
        description,
        discount_type,
        discount_value,
        minimum_order_amount,
        usage_limit,
        is_active
    )
VALUES (
        'WELCOME10',
        'Welcome discount - 10% off your first order',
        'percentage',
        10.00,
        25.00,
        1000,
        TRUE
    ),
    (
        'SAVE20',
        'Save 20% on orders over $100',
        'percentage',
        20.00,
        100.00,
        500,
        TRUE
    ),
    (
        'FREESHIP',
        'Free shipping on any order',
        'fixed',
        10.00,
        0.00,
        2000,
        TRUE
    );
-- Insert email templates
INSERT INTO email_templates (name, subject, body, variables)
VALUES (
        'order_confirmation',
        'Order Confirmation - {order_number}',
        'Dear {customer_name},\n\nThank you for your order! Your order number is {order_number}.\n\nOrder Total: {order_total}\n\nWe will notify you when your order ships.\n\nBest regards,\nCleanance Lab Team',
        '["customer_name", "order_number", "order_total"]'
    ),
    (
        'password_reset',
        'Password Reset Request',
        '<!DOCTYPE html><html><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Password Reset</title><style>body{background:#18122B;color:#fff;font-family:Roboto,sans-serif;margin:0;padding:0;} .container{max-width:480px;margin:40px auto;background:#231942;border-radius:12px;box-shadow:0 4px 32px rgba(126,87,194,0.08);padding:32px;} h1{color:#9c27b0;font-size:1.7rem;margin-bottom:18px;} p{color:#bdbdbd;line-height:1.6;} .btn{display:inline-block;margin:24px 0 0 0;padding:12px 32px;background:#9c27b0;color:#fff;text-decoration:none;border-radius:6px;font-weight:600;font-size:1.1rem;transition:background 0.2s;} .btn:hover{background:#7c1fa3;} .expires{background:#ff9800;color:#fff;padding:8px 12px;border-radius:6px;margin:16px 0;font-size:0.9em;} .footer{margin-top:32px;font-size:0.95em;color:#888;text-align:center;} @media(max-width:600px){.container{padding:16px;}}</style></head><body><div class="container"><h1>Password Reset</h1><p>Dear {customer_name},</p><p>We received a request to reset your password for your <b>{app_name}</b> account.</p><div class="expires"><strong>⚠️ Important:</strong> This link expires on {expires_at}</div><p>Click the button below to reset your password. This link can only be used once.</p><p style="text-align:center;"><a href="{reset_link}" class="btn">Reset Password</a></p><p>If you did not request this, you can safely ignore this email.</p><div class="footer">&copy; {app_name} | All rights reserved.</div></div></body></html>',
        '["customer_name", "reset_link", "app_name", "expires_at"]'
    ),
    (
        'welcome',
        'Welcome to Cleanance Lab!',
        'Dear {customer_name},\n\nWelcome to Cleanance Lab! We are excited to have you as part of our community.\n\nStart shopping now and enjoy our amazing products!\n\nBest regards,\nCleanance Lab Team',
        '["customer_name"]'
    );
-- Create indexes for better performance
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_featured ON products(is_featured);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_cart_items_user ON cart_items(user_id);
CREATE INDEX idx_cart_items_session ON cart_items(session_id);
CREATE INDEX idx_reviews_product ON product_reviews(product_id);
CREATE INDEX idx_reviews_user ON product_reviews(user_id);
CREATE INDEX idx_wishlist_user ON wishlist_items(user_id);