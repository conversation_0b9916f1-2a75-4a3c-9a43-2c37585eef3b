<div class="admin-dashboard">
    <div class="dashboard-header">
        <h1>Admin Dashboard</h1>
        <p>Welcome back, <?= htmlspecialchars($user['first_name']) ?>!</p>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3><?= number_format($stats['totalUsers'] ?? 0) ?></h3>
                <p>Total Users</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-box"></i>
            </div>
            <div class="stat-content">
                <h3><?= number_format($stats['totalProducts'] ?? 0) ?></h3>
                <p>Total Products</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-content">
                <h3><?= number_format($stats['totalOrders'] ?? 0) ?></h3>
                <p>Total Orders</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-content">
                <h3>$<?= number_format($stats['totalRevenue'] ?? 0, 2) ?></h3>
                <p>Total Revenue</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stat-content">
                <h3><?= number_format($stats['todayOrders'] ?? 0) ?></h3>
                <p>Today's Orders</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
                <h3>$<?= number_format($stats['todayRevenue'] ?? 0, 2) ?></h3>
                <p>Today's Revenue</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3><?= number_format($stats['pendingOrders'] ?? 0) ?></h3>
                <p>Pending Orders</p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
                <h3><?= number_format($stats['lowStockProducts'] ?? 0) ?></h3>
                <p>Low Stock Products</p>
            </div>
        </div>
    </div>

    <!-- Charts and Data -->
    <div class="dashboard-content">
        <div class="dashboard-row">
            <!-- Sales Chart -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Sales Overview (Last 30 Days)</h3>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Recent Orders</h3>
                    <a href="<?= UrlHelper::url('/admin/orders') ?>" class="btn btn-sm btn-outline">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr>
                                        <td>#<?= $order['id'] ?></td>
                                        <td><?= htmlspecialchars($order['first_name'] . ' ' . $order['last_name']) ?></td>
                                        <td>$<?= number_format($order['total_amount'], 2) ?></td>
                                        <td>
                                            <span class="status-badge status-<?= $order['status'] ?>">
                                                <?= ucfirst($order['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('M j, Y', strtotime($order['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-row">
            <!-- Top Products -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Top Selling Products</h3>
                    <a href="<?= UrlHelper::url('/admin/products') ?>" class="btn btn-sm btn-outline">View All</a>
                </div>
                <div class="card-body">
                    <div class="top-products">
                        <?php foreach ($topProducts as $product): ?>
                            <div class="product-item">
                                <div class="product-image">
                                    <?php if ($product['image']): ?>
                                        <img src="<?= UrlHelper::url('/uploads/products/' . $product['image']) ?>" alt="<?= htmlspecialchars($product['name']) ?>">
                                    <?php else: ?>
                                        <div class="no-image">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="product-info">
                                    <h4><?= htmlspecialchars($product['name']) ?></h4>
                                    <p>$<?= number_format($product['price'], 2) ?></p>
                                    <span class="sales-count"><?= $product['total_sold'] ?? 0 ?> sold</span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <a href="<?= UrlHelper::url('/admin/products/add') ?>" class="action-btn">
                            <i class="fas fa-plus"></i>
                            <span>Add Product</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/categories/add') ?>" class="action-btn">
                            <i class="fas fa-folder-plus"></i>
                            <span>Add Category</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/orders') ?>" class="action-btn">
                            <i class="fas fa-shopping-cart"></i>
                            <span>View Orders</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/users') ?>" class="action-btn">
                            <i class="fas fa-users"></i>
                            <span>Manage Users</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/reports/sales') ?>" class="action-btn">
                            <i class="fas fa-chart-bar"></i>
                            <span>Sales Report</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/settings') ?>" class="action-btn">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/discount-codes') ?>" class="action-btn">
                            <i class="fas fa-percent"></i>
                            <span>Discount Codes</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/analytics') ?>" class="action-btn">
                            <i class="fas fa-chart-pie"></i>
                            <span>Analytics</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/reports/products') ?>" class="action-btn">
                            <i class="fas fa-boxes"></i>
                            <span>Products Report</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/reports/users') ?>" class="action-btn">
                            <i class="fas fa-user-friends"></i>
                            <span>Users Report</span>
                        </a>
                        <a href="<?= UrlHelper::url('/admin/contact-requests') ?>" class="action-btn">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>Contact Requests</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .admin-dashboard {
        padding: var(--spacing-lg);
        background-color: var(--dark-surface);
        min-height: 100vh;
    }

    .dashboard-header {
        margin-bottom: var(--spacing-xl);
    }

    .dashboard-header h1 {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .dashboard-header p {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .stat-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        transition: all var(--transition-fast);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .stat-icon i {
        font-size: var(--font-size-xl);
        color: white;
    }

    .stat-content h3 {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .stat-content p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin: 0;
    }

    /* Dashboard Content */
    .dashboard-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xl);
    }

    .dashboard-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
    }

    .dashboard-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
    }

    .card-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    /* Table Styles */
    .table-responsive {
        overflow-x: auto;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: var(--spacing-md);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .table th {
        font-weight: 600;
        color: var(--text-primary);
        font-size: var(--font-size-sm);
    }

    .table td {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .status-badge {
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-pending {
        background-color: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
    }

    .status-processing {
        background-color: rgba(59, 130, 246, 0.1);
        color: var(--primary-blue);
    }

    .status-shipped {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
    }

    .status-delivered {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
    }

    .status-cancelled {
        background-color: rgba(239, 68, 68, 0.1);
        color: var(--error-color);
    }

    /* Top Products */
    .top-products {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .product-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        background-color: var(--dark-surface);
        transition: all var(--transition-fast);
    }

    .product-item:hover {
        background-color: var(--border-color);
    }

    .product-image {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-md);
        overflow: hidden;
        flex-shrink: 0;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        width: 100%;
        height: 100%;
        background-color: var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-tertiary);
    }

    .product-info h4 {
        font-size: var(--font-size-sm);
        font-weight: 500;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
    }

    .product-info p {
        font-size: var(--font-size-sm);
        color: var(--primary-purple);
        font-weight: 600;
        margin: 0 0 var(--spacing-xs) 0;
    }

    .sales-count {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
    }

    /* Quick Actions */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-lg);
        background-color: var(--dark-surface);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        text-decoration: none;
        color: var(--text-primary);
        transition: all var(--transition-fast);
    }

    .action-btn:hover {
        background-color: var(--primary-purple);
        color: white;
        transform: translateY(-2px);
    }

    .action-btn i {
        font-size: var(--font-size-xl);
    }

    .action-btn span {
        font-size: var(--font-size-sm);
        font-weight: 500;
    }

    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .btn-sm {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background-color: var(--primary-purple);
        border-color: var(--primary-purple);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .dashboard-row {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .admin-dashboard {
            padding: var(--spacing-md);
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .quick-actions {
            grid-template-columns: 1fr;
        }

        .table-responsive {
            font-size: var(--font-size-xs);
        }
    }

    /* Light Mode Support */
    .light-mode .admin-dashboard {
        background-color: var(--light-surface);
    }

    .light-mode .stat-card,
    .light-mode .dashboard-card {
        background-color: var(--light-card);
        border-color: var(--light-border);
    }

    .light-mode .product-item {
        background-color: var(--light-surface);
    }

    .light-mode .product-item:hover {
        background-color: var(--light-border);
    }

    .light-mode .action-btn {
        background-color: var(--light-surface);
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .action-btn:hover {
        background-color: var(--primary-purple);
        color: white;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sales Chart
        const salesData = <?= json_encode($salesData) ?>;
        const ctx = document.getElementById('salesChart').getContext('2d');

        const labels = salesData.map(item => item.date);
        const revenueData = salesData.map(item => parseFloat(item.revenue) || 0);
        const ordersData = salesData.map(item => parseInt(item.orders) || 0);

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenue ($)',
                    data: revenueData,
                    borderColor: '#7E57C2',
                    backgroundColor: 'rgba(126, 87, 194, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y'
                }, {
                    label: 'Orders',
                    data: ordersData,
                    borderColor: '#9575CD',
                    backgroundColor: 'rgba(149, 117, 205, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Date'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9CA3AF'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Revenue ($)'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9CA3AF'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Orders'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            color: '#9CA3AF'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#9CA3AF'
                        }
                    }
                }
            }
        });
    });
</script>