<div class="auth-container">
    <div class="container">
        <div class="auth-content">
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-flask"></i>
                        <span>Cleanance Lab</span>
                    </div>
                    <h1 class="auth-title">Create Your Account</h1>
                    <p class="auth-subtitle">Join Cleanance Lab and start your shopping journey</p>
                </div>

                <!-- Error/Success Messages -->
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>

                <!-- Registration Form -->
                <form class="auth-form" method="POST" action="<?= UrlHelper::url('/register') ?>" id="registerForm">
                    <input type="hidden" name="csrf_token" value="<?= $this->generateCSRFToken() ?>">

                    <!-- Name Fields -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name" class="form-label">First Name</label>
                            <div class="input-group">
                                <i class="fas fa-user input-icon"></i>
                                <input
                                    type="text"
                                    id="first_name"
                                    name="first_name"
                                    class="form-input"
                                    value="<?= htmlspecialchars($formData['first_name'] ?? '') ?>"
                                    placeholder="Enter your first name"
                                    required>
                            </div>
                            <div class="form-error" id="first_name_error"></div>
                        </div>

                        <div class="form-group">
                            <label for="last_name" class="form-label">Last Name</label>
                            <div class="input-group">
                                <i class="fas fa-user input-icon"></i>
                                <input
                                    type="text"
                                    id="last_name"
                                    name="last_name"
                                    class="form-input"
                                    value="<?= htmlspecialchars($formData['last_name'] ?? '') ?>"
                                    placeholder="Enter your last name"
                                    required>
                            </div>
                            <div class="form-error" id="last_name_error"></div>
                        </div>
                    </div>

                    <!-- Username Field -->
                    <div class="form-group">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <i class="fas fa-at input-icon"></i>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                class="form-input"
                                value="<?= htmlspecialchars($formData['username'] ?? '') ?>"
                                placeholder="Choose a unique username"
                                required>
                        </div>
                        <div class="form-error" id="username_error"></div>
                    </div>

                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <i class="fas fa-envelope input-icon"></i>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                class="form-input"
                                value="<?= htmlspecialchars($formData['email'] ?? '') ?>"
                                placeholder="Enter your email address"
                                required>
                        </div>
                        <div class="form-error" id="email_error"></div>
                    </div>

                    <!-- Password Fields -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    class="form-input"
                                    placeholder="Create a strong password"
                                    required>
                                <button type="button" class="password-toggle" data-target="password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="password_strength">
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strength_fill"></div>
                                </div>
                                <span class="strength-text" id="strength_text">Password strength</span>
                            </div>
                            <div class="form-error" id="password_error"></div>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input
                                    type="password"
                                    id="confirm_password"
                                    name="confirm_password"
                                    class="form-input"
                                    placeholder="Confirm your password"
                                    required>
                                <button type="button" class="password-toggle" data-target="confirm_password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-error" id="confirm_password_error"></div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="terms" name="terms" required>
                            <span class="checkmark"></span>
                            <span class="checkbox-text">
                                I agree to the
                                <a href="<?= UrlHelper::url('/terms-of-service') ?>" target="_blank">Terms of Service</a>
                                and
                                <a href="<?= UrlHelper::url('/privacy-policy') ?>" target="_blank">Privacy Policy</a>
                            </span>
                        </label>
                        <div class="form-error" id="terms_error"></div>
                    </div>

                    <!-- Newsletter Subscription -->
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="newsletter" name="newsletter">
                            <span class="checkmark"></span>
                            <span class="checkbox-text">
                                Subscribe to our newsletter for updates and exclusive offers
                            </span>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary btn-lg auth-submit" id="submitBtn">
                        <span class="btn-text">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </span>
                        <span class="btn-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            Creating Account...
                        </span>
                    </button>
                </form>

                <div class="auth-divider">
                    <span>or continue with</span>
                </div>

                <div class="social-auth">
                    <button class="btn btn-outline social-btn" onclick="socialLogin('google')">
                        <i class="fab fa-google"></i>
                        Continue with Google
                    </button>
                    <button class="btn btn-outline social-btn" onclick="socialLogin('facebook')">
                        <i class="fab fa-facebook-f"></i>
                        Continue with Facebook
                    </button>
                </div>

                <div class="auth-footer">
                    <p>Already have an account?
                        <a href="<?= UrlHelper::url('/login') ?>" class="auth-link">Sign in here</a>
                    </p>
                </div>
            </div>

            <div class="auth-image">
                <div class="auth-visual">
                    <div class="floating-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                    </div>
                    <div class="auth-illustration">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="auth-features">
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Secure Registration</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shipping-fast"></i>
                            <span>Fast Shopping</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-gift"></i>
                            <span>Exclusive Offers</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Auth Container */
    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
        display: flex;
        align-items: center;
        padding: var(--spacing-xl) 0;
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%237E57C2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .auth-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 var(--spacing-lg);
    }

    /* Auth Card */
    .auth-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        box-shadow: var(--shadow-xl);
        backdrop-filter: blur(20px);
        animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Auth Header */
    .auth-header {
        text-align: center;
        margin-bottom: var(--spacing-xl);
    }

    .auth-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--primary-purple);
        margin-bottom: var(--spacing-lg);
    }

    .auth-logo i {
        font-size: var(--font-size-2xl);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {

        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }
    }

    .auth-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .auth-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-base);
    }

    /* Form Styles */
    .auth-form {
        margin-bottom: var(--spacing-xl);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .form-label {
        display: block;
        margin-bottom: var(--spacing-sm);
        font-weight: 500;
        color: var(--text-primary);
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-icon {
        position: absolute;
        left: var(--spacing-md);
        color: var(--text-secondary);
        z-index: 1;
    }

    .form-input {
        width: 100%;
        padding: var(--spacing-md);
        padding-left: 3rem;
        background-color: var(--dark-surface);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all var(--transition-fast);
    }

    .form-input::placeholder {
        color: var(--text-tertiary);
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-purple);
        background-color: var(--dark-surface);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
    }

    .form-input.error {
        border-color: var(--error-color);
        background-color: rgba(239, 68, 68, 0.1);
    }

    .form-input.success {
        border-color: var(--success-color);
        background-color: rgba(16, 185, 129, 0.1);
    }

    /* Password Toggle */
    .password-toggle {
        position: absolute;
        right: var(--spacing-md);
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: var(--spacing-sm);
        transition: color var(--transition-fast);
    }

    .password-toggle:hover {
        color: var(--primary-purple);
    }

    /* Password Strength */
    .password-strength {
        margin-top: var(--spacing-sm);
    }

    .strength-bar {
        height: 4px;
        background-color: var(--border-color);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: var(--spacing-xs);
    }

    .strength-fill {
        height: 100%;
        width: 0%;
        transition: all var(--transition-fast);
        border-radius: 2px;
    }

    .strength-fill.weak {
        width: 25%;
        background-color: var(--error-color);
    }

    .strength-fill.fair {
        width: 50%;
        background-color: var(--warning-color);
    }

    .strength-fill.good {
        width: 75%;
        background-color: var(--success-color);
    }

    .strength-fill.strong {
        width: 100%;
        background-color: var(--success-dark);
    }

    .strength-text {
        font-size: var(--font-size-sm);
        color: var(--text-tertiary);
    }

    /* Checkbox Styles */
    .checkbox-label {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-sm);
        cursor: pointer;
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        line-height: 1.4;
    }

    .checkbox-label input[type="checkbox"] {
        display: none;
    }

    .checkmark {
        width: 20px;
        height: 20px;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-sm);
        position: relative;
        flex-shrink: 0;
        margin-top: 0.125rem;
        transition: all var(--transition-fast);
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark {
        background-color: var(--primary-purple);
        border-color: var(--primary-purple);
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 0.75rem;
        font-weight: bold;
    }

    .checkbox-text a {
        color: var(--primary-purple);
        text-decoration: none;
        transition: color var(--transition-fast);
    }

    .checkbox-text a:hover {
        color: var(--primary-light);
    }

    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--transition-fast);
        position: relative;
        overflow: hidden;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-lg {
        padding: var(--spacing-lg) var(--spacing-xl);
        font-size: var(--font-size-lg);
    }

    .auth-submit {
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }

    .btn-loading {
        display: none;
    }

    .btn.loading .btn-text {
        display: none;
    }

    .btn.loading .btn-loading {
        display: flex;
    }

    /* Auth Divider */
    .auth-divider {
        text-align: center;
        margin: var(--spacing-xl) 0;
        position: relative;
    }

    .auth-divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background-color: var(--border-color);
    }

    .auth-divider span {
        background-color: var(--dark-card);
        padding: 0 var(--spacing-md);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Social Auth */
    .social-auth {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background-color: var(--dark-surface);
        border-color: var(--primary-purple);
        color: var(--primary-purple);
    }

    .social-btn {
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    /* Auth Footer */
    .auth-footer {
        text-align: center;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .auth-link {
        color: var(--primary-purple);
        text-decoration: none;
        font-weight: 600;
        transition: color var(--transition-fast);
    }

    .auth-link:hover {
        color: var(--primary-light);
    }

    /* Auth Image */
    .auth-image {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .auth-visual {
        position: relative;
        width: 100%;
        max-width: 500px;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .floating-shapes {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    .shape {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;
    }

    .shape-1 {
        width: 80px;
        height: 80px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }

    .shape-2 {
        width: 60px;
        height: 60px;
        top: 20%;
        right: 20%;
        animation-delay: 2s;
    }

    .shape-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
    }

    .shape-4 {
        width: 40px;
        height: 40px;
        bottom: 10%;
        right: 10%;
        animation-delay: 1s;
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        50% {
            transform: translateY(-20px) rotate(180deg);
        }
    }

    .auth-illustration {
        width: 200px;
        height: 200px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-xl);
        animation: pulse 2s infinite;
    }

    .auth-illustration i {
        font-size: 4rem;
        color: white;
    }

    .auth-features {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        backdrop-filter: blur(10px);
        animation: slideInRight 0.6s ease-out;
    }

    .feature-item i {
        color: var(--primary-purple);
        font-size: var(--font-size-lg);
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }

        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Error Messages */
    .form-error {
        color: var(--error-color);
        font-size: var(--font-size-sm);
        margin-top: var(--spacing-xs);
        min-height: 1rem;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .form-error::before {
        content: '⚠';
        font-size: 0.75rem;
    }

    /* Alert Messages */
    .alert {
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    .alert-error {
        background-color: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: var(--error-color);
    }

    .alert-success {
        background-color: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: var(--success-color);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .auth-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }

        .auth-image {
            order: -1;
        }

        .auth-visual {
            height: 300px;
        }

        .auth-illustration {
            width: 150px;
            height: 150px;
        }

        .auth-illustration i {
            font-size: 3rem;
        }

        .auth-features {
            position: static;
            margin-top: var(--spacing-lg);
            flex-direction: row;
            justify-content: center;
        }

        .feature-item {
            flex: 1;
            max-width: 200px;
        }
    }

    @media (max-width: 768px) {
        .auth-container {
            padding: var(--spacing-md) 0;
        }

        .auth-content {
            padding: 0 var(--spacing-md);
        }

        .auth-card {
            padding: var(--spacing-xl);
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .social-auth {
            grid-template-columns: 1fr;
        }

        .auth-title {
            font-size: var(--font-size-2xl);
        }

        .auth-features {
            flex-direction: column;
        }

        .feature-item {
            max-width: none;
        }
    }

    /* Light Mode Support */
    .light-mode .auth-container {
        background: linear-gradient(135deg, var(--light-card) 0%, var(--light-surface) 100%);
    }

    .light-mode .auth-card {
        background-color: var(--light-card);
        border-color: var(--light-border);
    }

    .light-mode .form-input {
        background-color: var(--light-surface);
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .form-input:focus {
        background-color: var(--light-surface);
    }

    .light-mode .btn-outline {
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .btn-outline:hover {
        background-color: var(--light-surface);
    }

    .light-mode .feature-item {
        background-color: var(--light-card);
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .auth-divider span {
        background-color: var(--light-card);
    }

    .light-mode .checkmark {
        border-color: var(--light-border);
    }

    .light-mode .strength-bar {
        background-color: var(--light-border);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('registerForm');
        const submitBtn = document.getElementById('submitBtn');
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirm_password');

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;

            if (password.length >= 8) strength += 1;
            if (/[a-z]/.test(password)) strength += 1;
            if (/[A-Z]/.test(password)) strength += 1;
            if (/[0-9]/.test(password)) strength += 1;
            if (/[^A-Za-z0-9]/.test(password)) strength += 1;

            const strengthFill = document.getElementById('strength_fill');
            const strengthText = document.getElementById('strength_text');

            strengthFill.className = 'strength-fill';

            if (strength <= 1) {
                strengthFill.classList.add('weak');
                strengthText.textContent = 'Weak';
            } else if (strength <= 2) {
                strengthFill.classList.add('fair');
                strengthText.textContent = 'Fair';
            } else if (strength <= 3) {
                strengthFill.classList.add('good');
                strengthText.textContent = 'Good';
            } else {
                strengthFill.classList.add('strong');
                strengthText.textContent = 'Strong';
            }
        }

        // Password toggle functionality
        document.querySelectorAll('.password-toggle').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const input = document.getElementById(targetId);
                const icon = this.querySelector('i');

                if (input.type === 'password') {
                    input.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    input.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            });
        });

        // Password strength monitoring
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });

        // Real-time password confirmation check
        confirmPasswordInput.addEventListener('input', function() {
            const password = passwordInput.value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                showError('confirm_password', 'Passwords do not match');
            } else {
                clearError('confirm_password');
            }
        });

        // Form validation
        function validateField(field) {
            const value = field.value.trim();
            const fieldName = field.name;
            let isValid = true;
            let errorMessage = '';

            switch (fieldName) {
                case 'first_name':
                case 'last_name':
                    if (value.length < 2) {
                        isValid = false;
                        errorMessage = 'Name must be at least 2 characters long';
                    }
                    break;

                case 'username':
                    if (value.length < 3) {
                        isValid = false;
                        errorMessage = 'Username must be at least 3 characters long';
                    } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                        isValid = false;
                        errorMessage = 'Username can only contain letters, numbers, and underscores';
                    }
                    break;

                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                    break;

                case 'password':
                    if (value.length < 8) {
                        isValid = false;
                        errorMessage = 'Password must be at least 8 characters long';
                    }
                    break;

                case 'confirm_password':
                    if (value !== passwordInput.value) {
                        isValid = false;
                        errorMessage = 'Passwords do not match';
                    }
                    break;
            }

            if (!isValid) {
                showError(fieldName, errorMessage);
                field.classList.add('error');
                field.classList.remove('success');
            } else {
                clearError(fieldName);
                field.classList.remove('error');
                field.classList.add('success');
            }

            return isValid;
        }

        function showError(fieldName, message) {
            const errorElement = document.getElementById(fieldName + '_error');
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }

        function clearError(fieldName) {
            const errorElement = document.getElementById(fieldName + '_error');
            if (errorElement) {
                errorElement.textContent = '';
                errorElement.style.display = 'none';
            }
        }

        // Real-time validation
        form.querySelectorAll('input[required]').forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('error')) {
                    validateField(this);
                }
            });
        });

        // Form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            let isValid = true;
            form.querySelectorAll('input[required]').forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });

            if (isValid) {
                submitBtn.classList.add('loading');
                form.submit();
            }
        });

        // Social login functions
        window.socialLogin = function(provider) {
            // Implement social login functionality
            console.log('Social login with:', provider);
        };

        // Initialize password strength
        checkPasswordStrength('');
    });
</script>