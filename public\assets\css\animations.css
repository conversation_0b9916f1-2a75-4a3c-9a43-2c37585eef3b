/* ===== ADVANCED ANIMATIONS & MICRO-INTERACTIONS ===== */

/* ===== ENTRANCE ANIMATIONS ===== */

.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
}

.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease-out forwards;
}

.fade-in-down {
    opacity: 0;
    transform: translateY(-30px);
    animation: fadeInDown 0.6s ease-out forwards;
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    animation: fadeInLeft 0.6s ease-out forwards;
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    animation: fadeInRight 0.6s ease-out forwards;
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    animation: scaleIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.slide-in-bottom {
    transform: translateY(100%);
    animation: slideInBottom 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Animation Delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* ===== KEYFRAMES ===== */

@keyframes fadeIn {
    to { opacity: 1; }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInBottom {
    to {
        transform: translateY(0);
    }
}

/* ===== HOVER EFFECTS ===== */

.hover-scale {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-bounce {
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.hover-bounce:hover {
    transform: translateY(-5px);
}

.hover-glow {
    position: relative;
    transition: all 0.3s ease;
}

.hover-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    background: linear-gradient(45deg, var(--primary-purple), var(--primary-purple-light));
    opacity: 0;
    z-index: -1;
    filter: blur(20px);
    transition: opacity 0.3s ease;
}

.hover-glow:hover::before {
    opacity: 0.7;
}

/* ===== LOADING STATES ===== */

.skeleton {
    background: linear-gradient(90deg, var(--dark-surface) 25%, var(--dark-card) 50%, var(--dark-surface) 75%);
    background-size: 200% 100%;
    animation: skeleton 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes skeleton {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-purple);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dots-loading {
    display: inline-flex;
    gap: 4px;
}

.dots-loading span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-purple);
    animation: dotsLoading 1.4s infinite ease-in-out both;
}

.dots-loading span:nth-child(1) { animation-delay: -0.32s; }
.dots-loading span:nth-child(2) { animation-delay: -0.16s; }

@keyframes dotsLoading {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== PROGRESS INDICATORS ===== */

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--dark-surface);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-purple), var(--primary-purple-light));
    border-radius: 2px;
    transition: width 0.3s ease;
    width: var(--progress, 0%);
}

.progress-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-purple) var(--progress, 0%), var(--dark-surface) 0%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--dark-card);
}

/* ===== NOTIFICATION ANIMATIONS ===== */

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--dark-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-3d-md);
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

/* ===== MODAL ANIMATIONS ===== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10000;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--dark-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8) translateY(20px);
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: var(--shadow-3d-xl);
    border: 1px solid var(--border-color);
}

.modal-overlay.active .modal {
    transform: scale(1) translateY(0);
}

/* ===== RIPPLE EFFECT ===== */

.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* ===== SCROLL ANIMATIONS ===== */

.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* ===== PARALLAX EFFECTS ===== */

.parallax {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* ===== RESPONSIVE ANIMATIONS ===== */

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    .hover-lift:hover,
    .hover-tilt:hover,
    .hover-scale:hover,
    .hover-rotate:hover,
    .hover-bounce:hover {
        transform: none;
    }
    
    .card-3d:hover,
    .product-card-3d:hover {
        transform: none;
        box-shadow: var(--shadow-3d-sm);
    }
}
