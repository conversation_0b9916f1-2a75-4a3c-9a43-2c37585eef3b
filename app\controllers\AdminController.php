<?php

require_once __DIR__ . '/BaseController.php';

class AdminController extends BaseController
{

    public function __construct($pdo)
    {
        parent::__construct($pdo);
        $this->requireAdmin();
    }

    public function dashboard()
    {
        try {
            // Get statistics
            $stats = $this->getDashboardStats();

            // Get recent orders
            $recentOrders = $this->getRecentOrders();

            // Get top products
            $topProducts = $this->getTopProducts();

            // Get sales chart data
            $salesData = $this->getSalesChartData();

            // Get additional dashboard data
            $recentReviews = $this->getRecentReviews();
            $lowStockProducts = $this->getLowStockProducts();
            $popularProducts = $this->getPopularWishlistProducts();

            $this->render('admin/dashboard', [
                'stats' => $stats,
                'recentOrders' => $recentOrders,
                'topProducts' => $topProducts,
                'salesData' => $salesData,
                'recentReviews' => $recentReviews,
                'lowStockProducts' => $lowStockProducts,
                'popularProducts' => $popularProducts
            ]);
        } catch (Exception $e) {
            error_log("Dashboard error: " . $e->getMessage());
            $this->render('admin/dashboard', [
                'stats' => [],
                'recentOrders' => [],
                'topProducts' => [],
                'salesData' => []
            ]);
        }
    }

    private function getDashboardStats()
    {
        $stats = [];

        // Total users
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
        $stmt->execute();
        $stats['totalUsers'] = $stmt->fetch()['count'];

        // Total products
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
        $stmt->execute();
        $stats['totalProducts'] = $stmt->fetch()['count'];

        // Total orders
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders");
        $stmt->execute();
        $stats['totalOrders'] = $stmt->fetch()['count'];

        // Total revenue
        $stmt = $this->pdo->prepare("SELECT SUM(total_amount) as total FROM orders WHERE status != 'cancelled'");
        $stmt->execute();
        $stats['totalRevenue'] = $stmt->fetch()['total'] ?? 0;

        // Today's orders
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()");
        $stmt->execute();
        $stats['todayOrders'] = $stmt->fetch()['count'];

        // Today's revenue
        $stmt = $this->pdo->prepare("SELECT SUM(total_amount) as total FROM orders WHERE DATE(created_at) = CURDATE() AND status != 'cancelled'");
        $stmt->execute();
        $stats['todayRevenue'] = $stmt->fetch()['total'] ?? 0;

        // Pending orders
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
        $stmt->execute();
        $stats['pendingOrders'] = $stmt->fetch()['count'];

        // Low stock products
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE stock_quantity <= 10 AND is_active = 1");
        $stmt->execute();
        $stats['lowStockProducts'] = $stmt->fetch()['count'];

        return $stats;
    }

    private function getRecentOrders()
    {
        $stmt = $this->pdo->prepare("
            SELECT o.*, u.first_name, u.last_name, u.email 
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            ORDER BY o.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    private function getTopProducts()
    {
        $stmt = $this->pdo->prepare("
            SELECT p.*, COUNT(oi.id) as order_count, SUM(oi.quantity) as total_sold
            FROM products p 
            LEFT JOIN order_items oi ON p.id = oi.product_id 
            WHERE p.is_active = 1 
            GROUP BY p.id 
            ORDER BY total_sold DESC 
            LIMIT 10
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    private function getSalesChartData()
    {
        $stmt = $this->pdo->prepare("
            SELECT DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders
            FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND status != 'cancelled'
            GROUP BY DATE(created_at) 
            ORDER BY date
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    public function products()
    {
        $page = $_GET['page'] ?? 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        try {
            $stmt = $this->pdo->prepare("
                SELECT p.*, c.name as category_name, pi.image_path as primary_image
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                ORDER BY p.created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            $products = $stmt->fetchAll();

            // Get total count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products");
            $stmt->execute();
            $totalProducts = $stmt->fetch()['count'];
            $totalPages = ceil($totalProducts / $limit);

            $this->render('admin/products', [
                'products' => $products,
                'currentPage' => $page,
                'totalPages' => $totalPages
            ]);
        } catch (Exception $e) {
            error_log("Products error: " . $e->getMessage());
            $this->render('admin/products', [
                'products' => [],
                'currentPage' => 1,
                'totalPages' => 1
            ]);
        }
    }

    public function addProduct()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $this->sanitizeInput($_POST['name'] ?? '');
            $description = $this->sanitizeInput($_POST['description'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $salePrice = floatval($_POST['sale_price'] ?? 0);
            $categoryId = intval($_POST['category_id'] ?? 0);
            $stockQuantity = intval($_POST['stock_quantity'] ?? 0);
            $isActive = isset($_POST['is_active']);
            $featured = isset($_POST['featured']);

            if (empty($name) || $price <= 0 || $categoryId <= 0) {
                $error = 'Name, price, and category are required.';
            } else {
                try {
                    // Generate slug from name
                    $slug = $this->generateSlug($name);

                    // Check if slug already exists
                    $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE slug = ?");
                    $stmt->execute([$slug]);
                    $slugExists = $stmt->fetch()['count'] > 0;

                    if ($slugExists) {
                        // Add timestamp to make slug unique
                        $slug = $slug . '-' . time();
                    }

                    // Start transaction
                    $this->pdo->beginTransaction();

                    // Insert product without image column
                    $stmt = $this->pdo->prepare("
                        INSERT INTO products (name, slug, description, price, sale_price, category_id, stock_quantity, is_active, is_featured, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ");

                    $result = $stmt->execute([$name, $slug, $description, $price, $salePrice, $categoryId, $stockQuantity, $isActive ? 1 : 0, $featured ? 1 : 0]);

                    if ($result) {
                        $productId = $this->pdo->lastInsertId();

                        // Handle image upload if provided
                        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                            try {
                                $imageFileName = $this->uploadFile($_FILES['image'], 'products');

                                // Insert image into product_images table
                                $stmt = $this->pdo->prepare("
                                    INSERT INTO product_images (product_id, image_path, is_primary, created_at) 
                                    VALUES (?, ?, 1, NOW())
                                ");
                                $stmt->execute([$productId, $imageFileName]);
                            } catch (Exception $uploadError) {
                                // Log upload error but don't fail the product creation
                                error_log("Image upload failed for product $productId: " . $uploadError->getMessage());
                            }
                        }

                        // Commit transaction
                        $this->pdo->commit();

                        $success = 'Product added successfully!';
                        // Clear form data after successful submission
                        $_POST = [];
                    } else {
                        $this->pdo->rollBack();
                        $errorInfo = $stmt->errorInfo();
                        $error = 'Failed to add product. Database error: ' . $errorInfo[2];
                        error_log("Add product database error: " . print_r($errorInfo, true));
                    }
                } catch (Exception $e) {
                    if ($this->pdo->inTransaction()) {
                        $this->pdo->rollBack();
                    }
                    error_log("Add product error: " . $e->getMessage());
                    $error = 'Failed to add product: ' . $e->getMessage();
                }
            }
        }

        // Get categories for dropdown
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
            $stmt->execute();
            $categories = $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Categories fetch error: " . $e->getMessage());
            $categories = [];
            if (!isset($error)) {
                $error = 'Failed to load categories. Please try again.';
            }
        }

        $this->render('admin/add-product', [
            'categories' => $categories,
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function editProduct($id)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM products WHERE id = ?");
            $stmt->execute([$id]);
            $product = $stmt->fetch();

            if (!$product) {
                $this->redirect('admin/products');
                return;
            }

            // Get product images
            $stmt = $this->pdo->prepare("SELECT * FROM product_images WHERE product_id = ? ORDER BY is_primary DESC, sort_order ASC");
            $stmt->execute([$id]);
            $productImages = $stmt->fetchAll();

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $name = $this->sanitizeInput($_POST['name'] ?? '');
                $description = $this->sanitizeInput($_POST['description'] ?? '');
                $price = floatval($_POST['price'] ?? 0);
                $salePrice = floatval($_POST['sale_price'] ?? 0);
                $categoryId = intval($_POST['category_id'] ?? 0);
                $stockQuantity = intval($_POST['stock_quantity'] ?? 0);
                $isActive = isset($_POST['is_active']);
                $featured = isset($_POST['featured']);

                if (empty($name) || $price <= 0 || $categoryId <= 0) {
                    $error = 'Name, price, and category are required.';
                } else {
                    // Generate new slug if name changed
                    $slug = $product['slug'];
                    if ($name !== $product['name']) {
                        $slug = $this->generateSlug($name);

                        // Check if new slug already exists
                        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE slug = ? AND id != ?");
                        $stmt->execute([$slug, $id]);
                        $slugExists = $stmt->fetch()['count'] > 0;

                        if ($slugExists) {
                            $slug = $slug . '-' . time();
                        }
                    }

                    // Start transaction
                    $this->pdo->beginTransaction();

                    $stmt = $this->pdo->prepare("
                        UPDATE products 
                        SET name = ?, slug = ?, description = ?, price = ?, sale_price = ?, category_id = ?, stock_quantity = ?, is_active = ?, is_featured = ? 
                        WHERE id = ?
                    ");

                    if ($stmt->execute([$name, $slug, $description, $price, $salePrice, $categoryId, $stockQuantity, $isActive ? 1 : 0, $featured ? 1 : 0, $id])) {
                        // Handle image upload if provided
                        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                            try {
                                $imageFileName = $this->uploadFile($_FILES['image'], 'products');

                                // Insert new image into product_images table
                                $stmt = $this->pdo->prepare("
                                    INSERT INTO product_images (product_id, image_path, is_primary, created_at) 
                                    VALUES (?, ?, 1, NOW())
                                ");
                                $stmt->execute([$id, $imageFileName]);
                            } catch (Exception $uploadError) {
                                error_log("Image upload failed for product $id: " . $uploadError->getMessage());
                            }
                        }

                        // Commit transaction
                        $this->pdo->commit();

                        $success = 'Product updated successfully!';

                        // Refresh product data
                        $stmt = $this->pdo->prepare("SELECT * FROM products WHERE id = ?");
                        $stmt->execute([$id]);
                        $product = $stmt->fetch();

                        // Refresh product images
                        $stmt = $this->pdo->prepare("SELECT * FROM product_images WHERE product_id = ? ORDER BY is_primary DESC, sort_order ASC");
                        $stmt->execute([$id]);
                        $productImages = $stmt->fetchAll();
                    } else {
                        $this->pdo->rollBack();
                        $error = 'Failed to update product. Please try again.';
                    }
                }
            }
        } catch (Exception $e) {
            if ($this->pdo->inTransaction()) {
                $this->pdo->rollBack();
            }
            error_log("Edit product error: " . $e->getMessage());
            $error = 'Failed to update product. Please try again.';
        }

        // Get categories for dropdown
        $stmt = $this->pdo->prepare("SELECT * FROM categories WHERE is_active = 1 ORDER BY name");
        $stmt->execute();
        $categories = $stmt->fetchAll();

        $this->render('admin/edit-product', [
            'product' => $product ?? null,
            'productImages' => $productImages ?? [],
            'categories' => $categories,
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function deleteProduct($id)
    {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM products WHERE id = ?");
            $stmt->execute([$id]);
            $success = 'Product deleted successfully!';
        } catch (Exception $e) {
            error_log("Delete product error: " . $e->getMessage());
            $error = 'Failed to delete product. Please try again.';
        }

        $this->redirect('admin/products');
    }

    public function orders()
    {
        $page = $_GET['page'] ?? 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        try {
            $stmt = $this->pdo->prepare("
                SELECT o.*, u.first_name, u.last_name, u.email 
                FROM orders o 
                JOIN users u ON o.user_id = u.id 
                ORDER BY o.created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            $orders = $stmt->fetchAll();

            // Get total count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders");
            $stmt->execute();
            $totalOrders = $stmt->fetch()['count'];
            $totalPages = ceil($totalOrders / $limit);

            $this->render('admin/orders', [
                'orders' => $orders,
                'currentPage' => $page,
                'totalPages' => $totalPages
            ]);
        } catch (Exception $e) {
            error_log("Orders error: " . $e->getMessage());
            $this->render('admin/orders', [
                'orders' => [],
                'currentPage' => 1,
                'totalPages' => 1
            ]);
        }
    }

    public function orderDetail($id)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT o.*, u.first_name, u.last_name, u.email 
                FROM orders o 
                JOIN users u ON o.user_id = u.id 
                WHERE o.id = ?
            ");
            $stmt->execute([$id]);
            $order = $stmt->fetch();

            if (!$order) {
                $this->redirect('admin/orders');
                return;
            }

            // Get order items
            $stmt = $this->pdo->prepare("
                SELECT oi.*, p.name, p.image 
                FROM order_items oi 
                JOIN products p ON oi.product_id = p.id 
                WHERE oi.order_id = ?
            ");
            $stmt->execute([$id]);
            $orderItems = $stmt->fetchAll();

            $this->render('admin/order-detail', [
                'order' => $order,
                'orderItems' => $orderItems
            ]);
        } catch (Exception $e) {
            error_log("Order detail error: " . $e->getMessage());
            $this->redirect('admin/orders');
        }
    }

    // updateOrderStatus method moved to line 1083 for AJAX functionality

    public function users()
    {
        $page = $_GET['page'] ?? 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM users 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            $users = $stmt->fetchAll();

            // Get total count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM users");
            $stmt->execute();
            $totalUsers = $stmt->fetch()['count'];
            $totalPages = ceil($totalUsers / $limit);

            $this->render('admin/users', [
                'users' => $users,
                'currentPage' => $page,
                'totalPages' => $totalPages
            ]);
        } catch (Exception $e) {
            error_log("Users error: " . $e->getMessage());
            $this->render('admin/users', [
                'users' => [],
                'currentPage' => 1,
                'totalPages' => 1
            ]);
        }
    }

    public function userDetail($id)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$id]);
            $user = $stmt->fetch();

            if (!$user) {
                $this->redirect('admin/users');
                return;
            }

            // Get user orders
            $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC");
            $stmt->execute([$id]);
            $orders = $stmt->fetchAll();

            $this->render('admin/user-detail', [
                'user' => $user,
                'orders' => $orders
            ]);
        } catch (Exception $e) {
            error_log("User detail error: " . $e->getMessage());
            $this->redirect('admin/users');
        }
    }

    public function toggleUserStatus($id)
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $stmt = $this->pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$id]);
                $success = 'User status updated successfully!';
            } catch (Exception $e) {
                error_log("Toggle user status error: " . $e->getMessage());
                $error = 'Failed to update user status. Please try again.';
            }
        }

        $this->redirect('admin/users/' . $id);
    }

    public function categories()
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM categories ORDER BY name");
            $stmt->execute();
            $categories = $stmt->fetchAll();

            $this->render('admin/categories', ['categories' => $categories]);
        } catch (Exception $e) {
            error_log("Categories error: " . $e->getMessage());
            $this->render('admin/categories', ['categories' => []]);
        }
    }

    public function addCategory()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $this->sanitizeInput($_POST['name'] ?? '');
            $description = $this->sanitizeInput($_POST['description'] ?? '');
            $isActive = isset($_POST['is_active']);

            if (empty($name)) {
                $error = 'Category name is required.';
            } else {
                try {
                    // Generate slug from name
                    $slug = $this->generateSlug($name);

                    $stmt = $this->pdo->prepare("
                        INSERT INTO categories (name, slug, description, is_active, created_at) 
                        VALUES (?, ?, ?, ?, NOW())
                    ");

                    if ($stmt->execute([$name, $slug, $description, $isActive ? 1 : 0])) {
                        $success = 'Category added successfully!';
                        // Clear form data after successful submission
                        $_POST = [];
                    } else {
                        $error = 'Failed to add category. Please try again.';
                    }
                } catch (Exception $e) {
                    error_log("Add category error: " . $e->getMessage());
                    $error = 'Failed to add category. Please try again.';
                }
            }
        }

        $this->render('admin/add-category', [
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function editCategory($id)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM categories WHERE id = ?");
            $stmt->execute([$id]);
            $category = $stmt->fetch();

            if (!$category) {
                $this->redirect('admin/categories');
                return;
            }

            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $name = $this->sanitizeInput($_POST['name'] ?? '');
                $description = $this->sanitizeInput($_POST['description'] ?? '');
                $isActive = isset($_POST['is_active']);

                if (empty($name)) {
                    $error = 'Category name is required.';
                } else {
                    // Generate new slug if name changed
                    $slug = $category['slug'];
                    if ($name !== $category['name']) {
                        $slug = $this->generateSlug($name);
                    }

                    $stmt = $this->pdo->prepare("
                        UPDATE categories 
                        SET name = ?, slug = ?, description = ?, is_active = ? 
                        WHERE id = ?
                    ");

                    if ($stmt->execute([$name, $slug, $description, $isActive ? 1 : 0, $id])) {
                        $success = 'Category updated successfully!';
                        // Refresh category data
                        $stmt = $this->pdo->prepare("SELECT * FROM categories WHERE id = ?");
                        $stmt->execute([$id]);
                        $category = $stmt->fetch();
                    } else {
                        $error = 'Failed to update category. Please try again.';
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Edit category error: " . $e->getMessage());
            $error = 'Failed to update category. Please try again.';
        }

        $this->render('admin/edit-category', [
            'category' => $category ?? null,
            'error' => $error ?? null,
            'success' => $success ?? null
        ]);
    }

    public function deleteCategory($id)
    {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM categories WHERE id = ?");
            $stmt->execute([$id]);
            $success = 'Category deleted successfully!';
        } catch (Exception $e) {
            error_log("Delete category error: " . $e->getMessage());
            $error = 'Failed to delete category. Please try again.';
        }

        $this->redirect('admin/categories');
    }

    public function salesReport()
    {
        $start = $_GET['start_date'] ?? null;
        $end = $_GET['end_date'] ?? null;
        $where = '';
        $params = [];
        if ($start && $end) {
            $where = 'WHERE created_at BETWEEN ? AND ?';
            $params = [$start, $end];
        }
        $stmt = $this->pdo->prepare("SELECT * FROM orders $where ORDER BY created_at DESC");
        $stmt->execute($params);
        $sales = $stmt->fetchAll();
        $totalRevenue = 0;
        foreach ($sales as $sale) {
            $totalRevenue += $sale['total_amount'];
        }
        // Add customer name for each sale
        foreach ($sales as &$sale) {
            $userStmt = $this->pdo->prepare('SELECT first_name, last_name FROM users WHERE id = ?');
            $userStmt->execute([$sale['user_id']]);
            $user = $userStmt->fetch();
            $sale['customer_name'] = $user ? $user['first_name'] . ' ' . $user['last_name'] : 'Guest';
        }
        $this->render('admin/sales-report', ['sales' => $sales, 'totalRevenue' => $totalRevenue]);
    }

    public function productsReport()
    {
        $stmt = $this->pdo->query('SELECT p.name, SUM(oi.quantity) as total_sold, SUM(oi.total_price) as revenue FROM products p LEFT JOIN order_items oi ON p.id = oi.product_id GROUP BY p.id');
        $products = $stmt->fetchAll();
        $this->render('admin/products-report', ['products' => $products]);
    }

    public function usersReport()
    {
        $stmt = $this->pdo->query('SELECT u.id, u.first_name, u.last_name, u.email, COUNT(o.id) as orders, SUM(o.total_amount) as total_spent FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id');
        $users = $stmt->fetchAll();
        foreach ($users as &$user) {
            $user['name'] = $user['first_name'] . ' ' . $user['last_name'];
        }
        $this->render('admin/users-report', ['users' => $users]);
    }

    public function settings()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $fields = ['site_name', 'site_description', 'currency', 'tax_rate', 'shipping_cost', 'enable_reviews', 'enable_wishlist', 'enable_discount_codes'];
            foreach ($fields as $field) {
                $value = $this->sanitizeInput($_POST[$field] ?? '');
                $stmt = $this->pdo->prepare('UPDATE settings SET setting_value=? WHERE setting_key=?');
                $stmt->execute([$value, $field]);
            }
        }
        $stmt = $this->pdo->query('SELECT setting_key, setting_value FROM settings');
        $settings = [];
        foreach ($stmt->fetchAll() as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        $this->render('admin/settings', ['settings' => $settings]);
    }

    public function analytics()
    {
        try {
            $days = intval($_GET['days'] ?? 30);

            // Get metrics
            $metrics = $this->getAnalyticsMetrics($days);

            // Get top products
            $topProducts = $this->getTopProductsForAnalytics($days);

            // Get top categories
            $topCategories = $this->getTopCategoriesForAnalytics($days);

            // Get recent orders
            $recentOrders = $this->getRecentOrdersForAnalytics($days);

            $this->render('admin/analytics', [
                'metrics' => $metrics,
                'top_products' => $topProducts,
                'top_categories' => $topCategories,
                'recent_orders' => $recentOrders,
                'days' => $days
            ]);
        } catch (Exception $e) {
            error_log("Analytics error: " . $e->getMessage());
            $this->render('admin/analytics', [
                'metrics' => [],
                'top_products' => [],
                'top_categories' => [],
                'recent_orders' => [],
                'days' => $days
            ]);
        }
    }

    private function getAnalyticsMetrics($days)
    {
        $metrics = [];

        // Total orders
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        $metrics['total_orders'] = $stmt->fetch()['count'];

        // Previous period orders
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days * 2, $days]);
        $prevOrders = $stmt->fetch()['count'];
        $metrics['orders_change'] = $prevOrders > 0 ? (($metrics['total_orders'] - $prevOrders) / $prevOrders) * 100 : 0;

        // Total revenue
        $stmt = $this->pdo->prepare("
            SELECT SUM(total_amount) as total FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND status != 'cancelled'
        ");
        $stmt->execute([$days]);
        $metrics['total_revenue'] = $stmt->fetch()['total'] ?? 0;

        // Previous period revenue
        $stmt = $this->pdo->prepare("
            SELECT SUM(total_amount) as total FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY) AND status != 'cancelled'
        ");
        $stmt->execute([$days * 2, $days]);
        $prevRevenue = $stmt->fetch()['total'] ?? 0;
        $metrics['revenue_change'] = $prevRevenue > 0 ? (($metrics['total_revenue'] - $prevRevenue) / $prevRevenue) * 100 : 0;

        // Total customers
        $stmt = $this->pdo->prepare("
            SELECT COUNT(DISTINCT user_id) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        $metrics['total_customers'] = $stmt->fetch()['count'];

        // Previous period customers
        $stmt = $this->pdo->prepare("
            SELECT COUNT(DISTINCT user_id) as count FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days * 2, $days]);
        $prevCustomers = $stmt->fetch()['count'];
        $metrics['customers_change'] = $prevCustomers > 0 ? (($metrics['total_customers'] - $prevCustomers) / $prevCustomers) * 100 : 0;

        // Total products
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
        $stmt->execute();
        $metrics['total_products'] = $stmt->fetch()['count'];

        // Average order value
        $stmt = $this->pdo->prepare("
            SELECT AVG(total_amount) as avg FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND status != 'cancelled'
        ");
        $stmt->execute([$days]);
        $metrics['avg_order_value'] = $stmt->fetch()['avg'] ?? 0;

        // Top customer
        $stmt = $this->pdo->prepare("
            SELECT u.first_name, u.last_name, SUM(o.total_amount) as total_spent
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND o.status != 'cancelled'
            GROUP BY o.user_id 
            ORDER BY total_spent DESC 
            LIMIT 1
        ");
        $stmt->execute([$days]);
        $topCustomer = $stmt->fetch();
        $metrics['top_customer'] = $topCustomer ? $topCustomer['first_name'] . ' ' . $topCustomer['last_name'] : 'N/A';

        return $metrics;
    }

    private function getTopProductsForAnalytics($days)
    {
        $stmt = $this->pdo->prepare("
            SELECT p.*, c.name as category_name, 
                   COUNT(oi.id) as sales_count, 
                   SUM(oi.quantity * oi.price) as revenue
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN order_items oi ON p.id = oi.product_id 
            LEFT JOIN orders o ON oi.order_id = o.id 
            WHERE p.is_active = 1 
            AND (o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) OR o.id IS NULL)
            GROUP BY p.id 
            ORDER BY revenue DESC 
            LIMIT 10
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }

    private function getTopCategoriesForAnalytics($days)
    {
        $stmt = $this->pdo->prepare("
            SELECT c.*, 
                   COUNT(oi.id) as sales_count, 
                   SUM(oi.quantity * oi.price) as revenue,
                   COUNT(DISTINCT p.id) as product_count
            FROM categories c 
            LEFT JOIN products p ON c.id = p.category_id 
            LEFT JOIN order_items oi ON p.id = oi.product_id 
            LEFT JOIN orders o ON oi.order_id = o.id 
            WHERE c.is_active = 1 
            AND (o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) OR o.id IS NULL)
            GROUP BY c.id 
            ORDER BY revenue DESC 
            LIMIT 10
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }

    private function getRecentOrdersForAnalytics($days)
    {
        $stmt = $this->pdo->prepare("
            SELECT o.*, u.first_name, u.last_name 
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ORDER BY o.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }

    public function exportOrders()
    {
        $status = $_GET['status'] ?? '';
        $date = $_GET['date'] ?? '';
        $startDate = $_GET['start_date'] ?? '';
        $endDate = $_GET['end_date'] ?? '';

        try {
            $where = [];
            $params = [];

            if ($status) {
                $where[] = "o.status = ?";
                $params[] = $status;
            }

            if ($date) {
                switch ($date) {
                    case 'today':
                        $where[] = "DATE(o.created_at) = CURDATE()";
                        break;
                    case 'week':
                        $where[] = "o.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                        break;
                    case 'month':
                        $where[] = "o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                        break;
                    case 'year':
                        $where[] = "o.created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)";
                        break;
                }
            }

            if ($startDate && $endDate) {
                $where[] = "o.created_at BETWEEN ? AND ?";
                $params[] = $startDate . ' 00:00:00';
                $params[] = $endDate . ' 23:59:59';
            }

            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

            $stmt = $this->pdo->prepare("
                SELECT o.*, u.first_name, u.last_name, u.email 
                FROM orders o 
                JOIN users u ON o.user_id = u.id 
                $whereClause
                ORDER BY o.created_at DESC
            ");
            $stmt->execute($params);
            $orders = $stmt->fetchAll();

            // Generate CSV
            $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            $output = fopen('php://output', 'w');
            fputcsv($output, ['Order ID', 'Customer', 'Email', 'Total', 'Status', 'Payment Method', 'Date']);

            foreach ($orders as $order) {
                fputcsv($output, [
                    $order['id'],
                    $order['first_name'] . ' ' . $order['last_name'],
                    $order['email'],
                    $order['total_amount'],
                    $order['status'],
                    $order['payment_method'],
                    $order['created_at']
                ]);
            }

            fclose($output);
            exit;
        } catch (Exception $e) {
            error_log("Export orders error: " . $e->getMessage());
            header('HTTP/1.1 500 Internal Server Error');
            echo 'Export failed';
        }
    }

    public function exportUsers()
    {
        $role = $_GET['role'] ?? '';
        $status = $_GET['status'] ?? '';
        $date = $_GET['date'] ?? '';

        try {
            $where = [];
            $params = [];

            if ($role) {
                $where[] = "u.is_admin = ?";
                $params[] = $role === 'admin' ? 1 : 0;
            }

            if ($status) {
                $where[] = "u.is_active = ?";
                $params[] = $status;
            }

            if ($date) {
                switch ($date) {
                    case 'today':
                        $where[] = "DATE(u.created_at) = CURDATE()";
                        break;
                    case 'week':
                        $where[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                        break;
                    case 'month':
                        $where[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                        break;
                    case 'year':
                        $where[] = "u.created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)";
                        break;
                }
            }

            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

            $stmt = $this->pdo->prepare("
                SELECT u.*, 
                       COUNT(o.id) as order_count,
                       SUM(o.total_amount) as total_spent
                FROM users u 
                LEFT JOIN orders o ON u.id = o.user_id 
                $whereClause
                GROUP BY u.id 
                ORDER BY u.created_at DESC
            ");
            $stmt->execute($params);
            $users = $stmt->fetchAll();

            // Generate CSV
            $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            $output = fopen('php://output', 'w');
            fputcsv($output, ['ID', 'Name', 'Email', 'Role', 'Orders', 'Total Spent', 'Status', 'Joined']);

            foreach ($users as $user) {
                fputcsv($output, [
                    $user['id'],
                    $user['first_name'] . ' ' . $user['last_name'],
                    $user['email'],
                    $user['is_admin'] ? 'Admin' : 'User',
                    $user['order_count'],
                    $user['total_spent'] ?? 0,
                    $user['is_active'] ? 'Active' : 'Inactive',
                    $user['created_at']
                ]);
            }

            fclose($output);
            exit;
        } catch (Exception $e) {
            error_log("Export users error: " . $e->getMessage());
            header('HTTP/1.1 500 Internal Server Error');
            echo 'Export failed';
        }
    }

    public function toggleProductStatus($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = $input['status'] ?? false;

            $stmt = $this->pdo->prepare("UPDATE products SET is_active = ? WHERE id = ?");
            $result = $stmt->execute([$status ? 1 : 0, $id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Toggle product status error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function toggleCategoryStatus($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = $input['status'] ?? false;

            $stmt = $this->pdo->prepare("UPDATE categories SET is_active = ? WHERE id = ?");
            $result = $stmt->execute([$status ? 1 : 0, $id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Toggle category status error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function toggleUserRole($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $role = $input['role'] ?? 'user';

            $stmt = $this->pdo->prepare("UPDATE users SET is_admin = ? WHERE id = ?");
            $result = $stmt->execute([$role === 'admin' ? 1 : 0, $id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Toggle user role error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function deleteUser($id)
    {
        try {
            // Check if user has orders
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE user_id = ?");
            $stmt->execute([$id]);
            $orderCount = $stmt->fetch()['count'];

            if ($orderCount > 0) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Cannot delete user with existing orders']);
                return;
            }

            $stmt = $this->pdo->prepare("DELETE FROM users WHERE id = ?");
            $result = $stmt->execute([$id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Delete user error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function cancelOrder($id)
    {
        try {
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'cancelled' WHERE id = ?");
            $result = $stmt->execute([$id]);

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Cancel order error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function updateOrderStatus($id)
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = $input['status'] ?? '';
            $note = $input['note'] ?? '';

            if (empty($status)) {
                throw new Exception('Status is required');
            }

            $stmt = $this->pdo->prepare("UPDATE orders SET status = ? WHERE id = ?");
            $result = $stmt->execute([$status, $id]);

            // Add status note if provided
            if ($result && !empty($note)) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO order_notes (order_id, note, created_at) 
                    VALUES (?, ?, NOW())
                ");
                $stmt->execute([$id, $note]);
            }

            header('Content-Type: application/json');
            echo json_encode(['success' => $result]);
        } catch (Exception $e) {
            error_log("Update order status error: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function exportAnalytics()
    {
        $days = intval($_GET['days'] ?? 30);

        try {
            $metrics = $this->getAnalyticsMetrics($days);
            $topProducts = $this->getTopProductsForAnalytics($days);
            $topCategories = $this->getTopCategoriesForAnalytics($days);

            $filename = 'analytics_export_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            $output = fopen('php://output', 'w');

            // Metrics section
            fputcsv($output, ['Analytics Report - Last ' . $days . ' Days']);
            fputcsv($output, []);
            fputcsv($output, ['Metric', 'Value', 'Change (%)']);
            fputcsv($output, ['Total Orders', $metrics['total_orders'], $metrics['orders_change']]);
            fputcsv($output, ['Total Revenue', '$' . number_format($metrics['total_revenue'], 2), $metrics['revenue_change']]);
            fputcsv($output, ['Total Customers', $metrics['total_customers'], $metrics['customers_change']]);
            fputcsv($output, ['Average Order Value', '$' . number_format($metrics['avg_order_value'], 2), '']);

            fputcsv($output, []);
            fputcsv($output, ['Top Products']);
            fputcsv($output, ['Product', 'Category', 'Sales Count', 'Revenue']);
            foreach ($topProducts as $product) {
                fputcsv($output, [
                    $product['name'],
                    $product['category_name'] ?? 'Uncategorized',
                    $product['sales_count'],
                    '$' . number_format($product['revenue'] ?? 0, 2)
                ]);
            }

            fputcsv($output, []);
            fputcsv($output, ['Top Categories']);
            fputcsv($output, ['Category', 'Sales Count', 'Revenue', 'Products']);
            foreach ($topCategories as $category) {
                fputcsv($output, [
                    $category['name'],
                    $category['sales_count'],
                    '$' . number_format($category['revenue'] ?? 0, 2),
                    $category['product_count']
                ]);
            }

            fclose($output);
            exit;
        } catch (Exception $e) {
            error_log("Export analytics error: " . $e->getMessage());
            header('HTTP/1.1 500 Internal Server Error');
            echo 'Export failed';
        }
    }

    // Contact Requests Management
    public function contactRequests()
    {
        $stmt = $this->pdo->prepare("SELECT * FROM contact_requests ORDER BY created_at DESC");
        $stmt->execute();
        $requests = $stmt->fetchAll();
        $this->render('admin/contact-requests', ['requests' => $requests]);
    }

    public function viewContactRequest($id)
    {
        $stmt = $this->pdo->prepare("SELECT * FROM contact_requests WHERE id = ?");
        $stmt->execute([$id]);
        $request = $stmt->fetch();
        if ($request) {
            // Mark as read if not already
            if ($request['status'] === 'new') {
                $update = $this->pdo->prepare("UPDATE contact_requests SET status = 'read' WHERE id = ?");
                $update->execute([$id]);
                $request['status'] = 'read';
            }
            $this->render('admin/contact-request-view', ['request' => $request]);
        } else {
            $this->redirect('/admin/contact-requests');
        }
    }

    public function archiveContactRequest($id)
    {
        $stmt = $this->pdo->prepare("UPDATE contact_requests SET status = 'archived' WHERE id = ?");
        $stmt->execute([$id]);
        $this->redirect('/admin/contact-requests');
    }

    public function deleteContactRequest($id)
    {
        $stmt = $this->pdo->prepare("DELETE FROM contact_requests WHERE id = ?");
        $stmt->execute([$id]);
        $this->redirect('/admin/contact-requests');
    }

    // Discount Codes List
    public function discountCodes()
    {
        $stmt = $this->pdo->query('SELECT * FROM discount_codes ORDER BY created_at DESC');
        $discountCodes = $stmt->fetchAll();
        $this->render('admin/discount-codes', ['discountCodes' => $discountCodes]);
    }

    // Add Discount Code
    public function addDiscountCode()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $code = $this->sanitizeInput($_POST['code'] ?? '');
            $type = $this->sanitizeInput($_POST['discount_type'] ?? 'percentage');
            $value = floatval($_POST['discount_value'] ?? 0);
            $min = floatval($_POST['minimum_order_amount'] ?? 0);
            $max = floatval($_POST['maximum_discount'] ?? 0);
            $isActive = isset($_POST['is_active']) ? intval($_POST['is_active']) : 1;
            $stmt = $this->pdo->prepare("INSERT INTO discount_codes (code, discount_type, discount_value, minimum_order_amount, maximum_discount, is_active) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([$code, $type, $value, $min, $max, $isActive]);
            $this->redirect('/admin/discount-codes');
        }
        $this->render('admin/add-discount-code');
    }

    // Edit Discount Code
    public function editDiscountCode($id)
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $code = $this->sanitizeInput($_POST['code'] ?? '');
            $type = $this->sanitizeInput($_POST['discount_type'] ?? 'percentage');
            $value = floatval($_POST['discount_value'] ?? 0);
            $min = floatval($_POST['minimum_order_amount'] ?? 0);
            $max = floatval($_POST['maximum_discount'] ?? 0);
            $isActive = isset($_POST['is_active']) ? intval($_POST['is_active']) : 1;
            $stmt = $this->pdo->prepare("UPDATE discount_codes SET code=?, discount_type=?, discount_value=?, minimum_order_amount=?, maximum_discount=?, is_active=? WHERE id=?");
            $stmt->execute([$code, $type, $value, $min, $max, $isActive, $id]);
            $this->redirect('/admin/discount-codes');
        }
        $stmt = $this->pdo->prepare('SELECT * FROM discount_codes WHERE id = ?');
        $stmt->execute([$id]);
        $discountCode = $stmt->fetch();
        $this->render('admin/edit-discount-code', ['discountCode' => $discountCode]);
    }

    private function getRecentReviews($limit = 5)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT pr.*, p.name as product_name, u.first_name, u.last_name
                FROM product_reviews pr
                JOIN products p ON pr.product_id = p.id
                JOIN users u ON pr.user_id = u.id
                ORDER BY pr.created_at DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error fetching recent reviews: " . $e->getMessage());
            return [];
        }
    }

    private function getLowStockProducts($threshold = 10)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT id, name, stock_quantity, price
                FROM products
                WHERE stock_quantity <= ? AND is_active = 1
                ORDER BY stock_quantity ASC
                LIMIT 10
            ");
            $stmt->execute([$threshold]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error fetching low stock products: " . $e->getMessage());
            return [];
        }
    }

    private function getPopularWishlistProducts($limit = 5)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT p.id, p.name, p.price, COUNT(wi.id) as wishlist_count
                FROM products p
                JOIN wishlist_items wi ON p.id = wi.product_id
                WHERE p.is_active = 1
                GROUP BY p.id
                ORDER BY wishlist_count DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error fetching popular wishlist products: " . $e->getMessage());
            return [];
        }
    }
}
