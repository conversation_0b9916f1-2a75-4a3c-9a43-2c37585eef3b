<main class="products-main">
    <?php if (empty($products)): ?>
        <div class="no-products">
            <div class="no-products-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3>No products found</h3>
            <p>Try adjusting your search or filter criteria</p>
            <a href="<?= APP_URL ?>/products" class="btn btn-primary">View All Products</a>
        </div>
    <?php else: ?>
        <div class="products-grid" id="productsGrid">
            <?php foreach ($products as $product): ?>
                <div class="product-card fade-in">
                    <div class="product-image">
                        <img src="<?= UrlHelper::url('/uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                            alt="<?= htmlspecialchars($product['name']) ?>"
                            loading="lazy">
                        <?php if ($product['sale_price']): ?>
                            <div class="product-badge">Sale</div>
                        <?php endif; ?>
                        <?php if ($product['stock_quantity'] <= 0): ?>
                            <div class="product-badge out-of-stock">Out of Stock</div>
                        <?php endif; ?>
                        <div class="product-overlay">
                            <button class="btn btn-primary btn-sm quick-view" data-product-id="<?= $product['id'] ?>">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm add-to-wishlist" data-product-id="<?= $product['id'] ?>">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-content">
                        <div class="product-category"><?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?></div>
                        <h3 class="product-title">
                            <a href="<?= APP_URL ?>/product/<?= htmlspecialchars($product['slug']) ?>">
                                <?= htmlspecialchars($product['name']) ?>
                            </a>
                        </h3>

                        <div class="product-price">
                            <span class="product-price-current">
                                $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                            </span>
                            <?php if ($product['sale_price']): ?>
                                <span class="product-price-original">
                                    $<?= number_format($product['price'], 2) ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="product-actions">
                            <?php if ($product['stock_quantity'] > 0): ?>
                                <button class="btn btn-primary add-to-cart"
                                    data-product-id="<?= $product['id'] ?>"
                                    data-quantity="1">
                                    <i class="fas fa-shopping-cart"></i>
                                    Add to Cart
                                </button>
                            <?php else: ?>
                                <button class="btn btn-outline" disabled>
                                    <i class="fas fa-times"></i>
                                    Out of Stock
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($currentPage > 1): ?>
                    <a href="<?= buildPaginationUrl($currentPage - 1) ?>" class="pagination-btn">
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </a>
                <?php endif; ?>

                <div class="pagination-numbers">
                    <?php
                    $start = max(1, $currentPage - 2);
                    $end = min($totalPages, $currentPage + 2);

                    if ($start > 1): ?>
                        <a href="<?= buildPaginationUrl(1) ?>" class="pagination-btn">1</a>
                        <?php if ($start > 2): ?>
                            <span class="pagination-ellipsis">...</span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $start; $i <= $end; $i++): ?>
                        <a href="<?= buildPaginationUrl($i) ?>"
                            class="pagination-btn <?= $i === $currentPage ? 'active' : '' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($end < $totalPages): ?>
                        <?php if ($end < $totalPages - 1): ?>
                            <span class="pagination-ellipsis">...</span>
                        <?php endif; ?>
                        <a href="<?= buildPaginationUrl($totalPages) ?>" class="pagination-btn"><?= $totalPages ?></a>
                    <?php endif; ?>
                </div>

                <?php if ($currentPage < $totalPages): ?>
                    <a href="<?= buildPaginationUrl($currentPage + 1) ?>" class="pagination-btn">
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</main>