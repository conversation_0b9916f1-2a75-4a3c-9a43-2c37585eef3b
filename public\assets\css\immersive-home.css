/* Immersive Homepage Styles - Inspired by <PERSON><PERSON><PERSON>.be */

/* CSS Variables */
:root {
    --bg-primary: #0a0a0a;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #666666;
    --accent-purple: #7E57C2;
    --accent-purple-dark: #5E35B1;
    --accent-purple-light: #B39DDB;
    --accent-blue: #2196F3;
    --accent-cyan: #00BCD4;
    --border-color: #333333;
    --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 20px rgba(126, 87, 194, 0.3);
    --gradient-primary: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));
    --gradient-secondary: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    --font-primary: 'Inter', -apple-system, BlinkMacS<PERSON><PERSON><PERSON><PERSON>, 'Segoe UI', sans-serif;
    --font-display: 'Clash Display', 'Inter', sans-serif;
}

/* Light Mode Override */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #1a1a1a;
    --text-secondary: #4a4a4a;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
}

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    line-height: 1.6;
}

/* Three.js Canvas */
#three-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

/* Immersive Header */
.immersive-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1.5rem 2rem;
    background: rgba(10, 10, 10, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.immersive-header.scrolled {
    background: rgba(10, 10, 10, 0.95);
    padding: 1rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.logo-text {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Menu Toggle */
.menu-toggle {
    background: none;
    border: none;
    cursor: pointer;
    width: 30px;
    height: 30px;
    position: relative;
    z-index: 1001;
}

.menu-toggle span {
    display: block;
    width: 100%;
    height: 2px;
    background: var(--text-primary);
    margin: 6px 0;
    transition: all 0.3s ease;
    transform-origin: center;
}

.menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Fullscreen Menu */
.fullscreen-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-menu.active {
    opacity: 1;
    visibility: visible;
}

.menu-content {
    text-align: center;
}

.menu-links {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.menu-link {
    font-family: var(--font-display);
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.menu-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
}

.menu-link:hover {
    color: var(--accent-purple);
    transform: translateX(20px);
}

.menu-link:hover::before {
    left: 0;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 2rem;
}

.hero-content {
    max-width: 1400px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 0.9;
    margin-bottom: 2rem;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(50px);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 500px;
    opacity: 0;
    transform: translateY(30px);
}

.hero-actions {
    opacity: 0;
    transform: translateY(30px);
}

/* CTA Button */
.cta-button {
    display: inline-flex;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--gradient-primary);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover .button-glow {
    left: 100%;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 500px;
}

.floating-products {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-product {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-dark);
    transition: all 0.3s ease;
}

.floating-product:nth-child(1) {
    top: 20%;
    left: 20%;
    animation: float1 6s ease-in-out infinite;
}

.floating-product:nth-child(2) {
    top: 60%;
    right: 20%;
    animation: float2 8s ease-in-out infinite;
}

.floating-product:nth-child(3) {
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    animation: float3 7s ease-in-out infinite;
}

.floating-product img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    padding: 0.5rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.floating-product:hover .product-info {
    transform: translateY(0);
}

.product-info h4 {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.product-info p {
    font-size: 0.7rem;
    color: var(--accent-purple);
    font-weight: 600;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: var(--text-secondary);
    position: relative;
    animation: scrollBounce 2s infinite;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--text-secondary);
    border-bottom: 2px solid var(--text-secondary);
    transform: rotate(45deg);
}

/* Sections */
section {
    padding: 6rem 2rem;
    position: relative;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Featured Products */
.featured-section {
    background: var(--bg-secondary);
}

.products-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.showcase-item {
    perspective: 1000px;
}

.product-card-3d {
    width: 100%;
    height: 400px;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.6s ease;
}

.product-card-3d:hover {
    transform: rotateY(180deg);
}

.card-inner {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
}

.card-front,
.card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 20px;
    overflow: hidden;
    background: var(--bg-tertiary);
    box-shadow: var(--shadow-dark);
}

.card-back {
    transform: rotateY(180deg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.product-image {
    width: 100%;
    height: 70%;
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.sale-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--accent-purple);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.product-info {
    padding: 1.5rem;
}

.product-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.current-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--accent-purple);
}

.original-price {
    font-size: 1rem;
    color: var(--text-muted);
    text-decoration: line-through;
}

.product-details {
    text-align: center;
}

.product-details h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.description {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.add-to-cart-btn,
.view-details-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
}

.add-to-cart-btn {
    background: var(--gradient-primary);
    color: white;
}

.view-details-btn {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.view-details-btn:hover {
    border-color: var(--accent-purple);
    color: var(--accent-purple);
}

/* Categories */
.categories-section {
    background: var(--bg-primary);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background: var(--bg-tertiary);
    border-radius: 20px;
    padding: 2rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-glow);
}

.category-visual {
    position: relative;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.category-icon {
    font-size: 3rem;
    color: var(--accent-purple);
    z-index: 2;
}

.category-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, var(--accent-purple) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.1;
    animation: particleFloat 10s linear infinite;
}

.category-content h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.category-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Latest Products */
.latest-section {
    background: var(--bg-secondary);
}

.latest-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.latest-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    aspect-ratio: 1;
}

.product-preview {
    width: 100%;
    height: 100%;
    position: relative;
}

.product-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.latest-item:hover .preview-overlay {
    opacity: 1;
}

.latest-item:hover .product-preview img {
    transform: scale(1.1);
}

.overlay-content {
    text-align: center;
    color: white;
}

.overlay-content h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.overlay-content .price {
    color: var(--accent-purple);
    font-weight: 600;
    margin-bottom: 1rem;
}

.overlay-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.quick-add,
.quick-view {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: var(--accent-purple);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.quick-add:hover,
.quick-view:hover {
    transform: scale(1.1);
    background: var(--accent-purple-dark);
}

/* CTA Section */
.cta-section {
    background: var(--gradient-primary);
    text-align: center;
    color: white;
}

.cta-content h2 {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.cta-actions {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-button.primary {
    background: white;
    color: var(--accent-purple);
}

.cta-button.secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-button.secondary:hover {
    background: white;
    color: var(--accent-purple);
}

/* Footer */
.immersive-footer {
    background: var(--bg-primary);
    padding: 4rem 2rem 2rem;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul a:hover {
    color: var(--accent-purple);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--accent-purple);
    color: white;
    transform: translateY(-2px);
}

.newsletter-form {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 25px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    outline: none;
}

.newsletter-form input:focus {
    border-color: var(--accent-purple);
}

.newsletter-form button {
    padding: 0.75rem 1.5rem;
    background: var(--accent-purple);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-form button:hover {
    background: var(--accent-purple-dark);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    margin-bottom: 2rem;
}

.loading-logo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.loading-progress {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
}

.loading-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Animations */
@keyframes float1 {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

@keyframes float2 {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-15px) rotate(-5deg);
    }
}

@keyframes float3 {

    0%,
    100% {
        transform: translateX(-50%) translateY(0px);
    }

    50% {
        transform: translateX(-50%) translateY(-25px);
    }
}

@keyframes scrollBounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(10px);
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-20px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-visual {
        height: 300px;
    }

    .floating-product {
        width: 80px;
        height: 80px;
    }

    .menu-link {
        font-size: 2rem;
    }

    .products-showcase {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .latest-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .newsletter-form {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .immersive-header {
        padding: 1rem;
    }

    section {
        padding: 4rem 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .floating-product {
        width: 60px;
        height: 60px;
    }
}