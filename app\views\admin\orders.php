<div class="admin-container">
    <div class="admin-header">
        <h1>Order Management</h1>
        <div class="header-actions">
            <button class="btn btn-secondary" id="exportOrders">
                <i class="fas fa-download"></i> Export Orders
            </button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="admin-filters">
        <div class="filter-row">
            <div class="search-box">
                <input type="text" id="orderSearch" placeholder="Search orders by ID, customer name, or email..." class="form-input">
                <i class="fas fa-search"></i>
            </div>

            <div class="filter-group">
                <select id="statusFilter" class="form-select">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="refunded">Refunded</option>
                </select>

                <select id="dateFilter" class="form-select">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="year">This Year</option>
                </select>

                <input type="date" id="startDate" class="form-input" placeholder="Start Date">
                <input type="date" id="endDate" class="form-input" placeholder="End Date">
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="admin-table-container">
        <table class="admin-table">
            <thead>
                <tr>
                    <th width="80">Order ID</th>
                    <th>Customer</th>
                    <th>Products</th>
                    <th>Total</th>
                    <th>Status</th>
                    <th>Payment</th>
                    <th>Date</th>
                    <th width="150">Actions</th>
                </tr>
            </thead>
            <tbody id="ordersTableBody">
                <?php if (!empty($orders)): ?>
                    <?php foreach ($orders as $order): ?>
                        <tr data-order-id="<?= $order['id'] ?>">
                            <td>
                                <strong>#<?= $order['id'] ?></strong>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <strong><?= htmlspecialchars($order['customer_name']) ?></strong>
                                    <small><?= htmlspecialchars($order['customer_email']) ?></small>
                                </div>
                            </td>
                            <td>
                                <div class="products-info">
                                    <span class="product-count"><?= $order['item_count'] ?> items</span>
                                    <small><?= htmlspecialchars(substr($order['product_names'], 0, 50)) ?>...</small>
                                </div>
                            </td>
                            <td>
                                <div class="total-info">
                                    <strong>$<?= number_format($order['total_amount'], 2) ?></strong>
                                    <?php if ($order['discount_amount'] > 0): ?>
                                        <small class="discount">-$<?= number_format($order['discount_amount'], 2) ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-<?= $order['status'] ?>">
                                    <?= ucfirst($order['status']) ?>
                                </span>
                            </td>
                            <td>
                                <div class="payment-info">
                                    <span class="payment-method"><?= ucfirst($order['payment_method']) ?></span>
                                    <span class="payment-status <?= $order['payment_status'] ?>">
                                        <?= ucfirst($order['payment_status']) ?>
                                    </span>
                                </div>
                            </td>
                            <td><?= date('M j, Y H:i', strtotime($order['created_at'])) ?></td>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?= UrlHelper::url('/admin/orders/' . $order['id']) ?>"
                                        class="btn btn-sm btn-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-secondary update-status"
                                        data-id="<?= $order['id'] ?>"
                                        data-status="<?= $order['status'] ?>"
                                        title="Update Status">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger cancel-order"
                                        data-id="<?= $order['id'] ?>"
                                        data-status="<?= $order['status'] ?>"
                                        title="Cancel Order">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8" class="no-data">
                            <div class="empty-state">
                                <i class="fas fa-shopping-cart"></i>
                                <h3>No Orders Found</h3>
                                <p>Orders will appear here once customers start placing them.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if (isset($pagination) && $pagination['totalPages'] > 1): ?>
        <div class="pagination">
            <?php if ($pagination['currentPage'] > 1): ?>
                <a href="?page=<?= $pagination['currentPage'] - 1 ?>" class="page-link">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            <?php endif; ?>

            <?php for ($i = max(1, $pagination['currentPage'] - 2); $i <= min($pagination['totalPages'], $pagination['currentPage'] + 2); $i++): ?>
                <a href="?page=<?= $i ?>" class="page-link <?= $i == $pagination['currentPage'] ? 'active' : '' ?>">
                    <?= $i ?>
                </a>
            <?php endfor; ?>

            <?php if ($pagination['currentPage'] < $pagination['totalPages']): ?>
                <a href="?page=<?= $pagination['currentPage'] + 1 ?>" class="page-link">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Update Order Status</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form id="statusForm">
                <div class="form-group">
                    <label for="newStatus">New Status:</label>
                    <select id="newStatus" name="status" class="form-select" required>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="refunded">Refunded</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="statusNote">Note (optional):</label>
                    <textarea id="statusNote" name="note" class="form-textarea" rows="3" placeholder="Add a note about this status change..."></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelStatus">Cancel</button>
            <button type="button" class="btn btn-primary" id="saveStatus">Update Status</button>
        </div>
    </div>
</div>

<style>
    .admin-container {
        padding: 2rem;
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .admin-header h1 {
        color: var(--text-primary);
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 1rem;
    }

    .admin-filters {
        background: var(--card-bg);
        padding: 1.5rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .search-box {
        position: relative;
        flex: 1;
        min-width: 300px;
    }

    .search-box input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
    }

    .search-box i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }

    .filter-group {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .form-select,
    .form-input {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
        min-width: 120px;
    }

    .admin-table-container {
        background: var(--card-bg);
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
    }

    .admin-table th,
    .admin-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--bg-secondary);
        font-weight: 600;
        color: var(--text-primary);
    }

    .customer-info strong {
        display: block;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .customer-info small {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .products-info .product-count {
        display: block;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .products-info small {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .total-info strong {
        display: block;
        color: var(--text-primary);
        font-size: 1.1rem;
    }

    .total-info .discount {
        color: #e74c3c;
        font-size: 0.875rem;
    }

    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .status-processing {
        background: #cce5ff;
        color: #004085;
    }

    .status-shipped {
        background: #d1ecf1;
        color: #0c5460;
    }

    .status-delivered {
        background: #d4edda;
        color: #155724;
    }

    .status-cancelled {
        background: #f8d7da;
        color: #721c24;
    }

    .status-refunded {
        background: #e2e3e5;
        color: #383d41;
    }

    .payment-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .payment-method {
        font-weight: 600;
        color: var(--text-primary);
    }

    .payment-status {
        font-size: 0.875rem;
        padding: 0.125rem 0.25rem;
        border-radius: 3px;
    }

    .payment-status.paid {
        background: #d4edda;
        color: #155724;
    }

    .payment-status.pending {
        background: #fff3cd;
        color: #856404;
    }

    .payment-status.failed {
        background: #f8d7da;
        color: #721c24;
    }

    .action-buttons {
        display: flex;
        gap: 0.25rem;
    }

    .btn-sm {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-state h3 {
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .empty-state p {
        margin-bottom: 1.5rem;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 2rem;
    }

    .page-link {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        color: var(--text-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .page-link:hover {
        background: var(--bg-secondary);
    }

    .page-link.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
        background-color: var(--card-bg);
        margin: 5% auto;
        padding: 0;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .modal-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        color: var(--text-primary);
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1.5rem;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    .form-textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--bg-primary);
        color: var(--text-primary);
        resize: vertical;
    }

    @media (max-width: 768px) {
        .admin-container {
            padding: 1rem;
        }

        .admin-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .filter-row {
            flex-direction: column;
            align-items: stretch;
        }

        .search-box {
            min-width: auto;
        }

        .filter-group {
            flex-direction: column;
        }

        .admin-table {
            font-size: 0.875rem;
        }

        .admin-table th,
        .admin-table td {
            padding: 0.5rem;
        }

        .modal-content {
            width: 95%;
            margin: 10% auto;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('orderSearch');
        const statusFilter = document.getElementById('statusFilter');
        const dateFilter = document.getElementById('dateFilter');
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');

        function filterOrders() {
            const searchTerm = searchInput.value.toLowerCase();
            const status = statusFilter.value;
            const dateRange = dateFilter.value;

            const rows = document.querySelectorAll('#ordersTableBody tr');

            rows.forEach(row => {
                const orderId = row.querySelector('td:nth-child(1) strong').textContent.toLowerCase();
                const customerName = row.querySelector('.customer-info strong').textContent.toLowerCase();
                const customerEmail = row.querySelector('.customer-info small').textContent.toLowerCase();
                const statusCell = row.querySelector('.status-badge').textContent.toLowerCase();

                let show = true;

                // Search filter
                if (searchTerm && !orderId.includes(searchTerm) && !customerName.includes(searchTerm) && !customerEmail.includes(searchTerm)) {
                    show = false;
                }

                // Status filter
                if (status && statusCell !== status) {
                    show = false;
                }

                row.style.display = show ? '' : 'none';
            });
        }

        searchInput.addEventListener('input', filterOrders);
        statusFilter.addEventListener('change', filterOrders);
        dateFilter.addEventListener('change', filterOrders);

        // Status update modal
        const statusModal = document.getElementById('statusModal');
        const statusForm = document.getElementById('statusForm');
        const newStatus = document.getElementById('newStatus');
        const statusNote = document.getElementById('statusNote');
        const cancelStatus = document.getElementById('cancelStatus');
        const saveStatus = document.getElementById('saveStatus');

        let currentOrderId = null;

        function openStatusModal(orderId, currentStatus) {
            currentOrderId = orderId;
            newStatus.value = currentStatus;
            statusNote.value = '';
            statusModal.style.display = 'block';
        }

        function closeStatusModal() {
            statusModal.style.display = 'none';
            currentOrderId = null;
        }

        // Update status buttons
        document.querySelectorAll('.update-status').forEach(btn => {
            btn.addEventListener('click', function() {
                const orderId = this.dataset.id;
                const currentStatus = this.dataset.status;
                openStatusModal(orderId, currentStatus);
            });
        });

        // Cancel order buttons
        document.querySelectorAll('.cancel-order').forEach(btn => {
            btn.addEventListener('click', function() {
                const orderId = this.dataset.id;
                const currentStatus = this.dataset.status;

                if (currentStatus === 'cancelled') {
                    alert('This order is already cancelled.');
                    return;
                }

                if (confirm('Are you sure you want to cancel this order?')) {
                    fetch(`/admin/orders/${orderId}/cancel`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('Failed to cancel order');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred');
                        });
                }
            });
        });

        // Modal event listeners
        cancelStatus.addEventListener('click', closeStatusModal);

        saveStatus.addEventListener('click', function() {
            if (!currentOrderId) return;

            const formData = {
                status: newStatus.value,
                note: statusNote.value
            };

            fetch(`/admin/orders/${currentOrderId}/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        closeStatusModal();
                        location.reload();
                    } else {
                        alert('Failed to update order status');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred');
                });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === statusModal) {
                closeStatusModal();
            }
        });

        // Export orders
        document.getElementById('exportOrders').addEventListener('click', function() {
            const params = new URLSearchParams();
            if (statusFilter.value) params.append('status', statusFilter.value);
            if (dateFilter.value) params.append('date', dateFilter.value);
            if (startDate.value) params.append('start_date', startDate.value);
            if (endDate.value) params.append('end_date', endDate.value);

            window.open(`/admin/orders/export?${params.toString()}`, '_blank');
        });
    });
</script>