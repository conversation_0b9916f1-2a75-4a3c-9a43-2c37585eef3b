<?php

/**
 * Product Comparison Helper Class
 * Handles product comparison functionality
 */
class ComparisonHelper
{
    private static $pdo;
    private static $maxCompareItems = 4;

    /**
     * Initialize comparison helper
     */
    public static function init($pdo)
    {
        self::$pdo = $pdo;
    }

    /**
     * Add product to comparison
     */
    public static function addToComparison($userId, $productId)
    {
        try {
            // Check if product already in comparison
            $stmt = self::$pdo->prepare("
                SELECT id FROM product_comparisons 
                WHERE user_id = ? AND product_id = ?
            ");
            $stmt->execute([$userId, $productId]);
            
            if ($stmt->fetch()) {
                return ['success' => false, 'error' => 'Product already in comparison'];
            }

            // Check comparison limit
            $count = self::getComparisonCount($userId);
            if ($count >= self::$maxCompareItems) {
                return ['success' => false, 'error' => 'Maximum ' . self::$maxCompareItems . ' products can be compared'];
            }

            // Add to comparison
            $stmt = self::$pdo->prepare("
                INSERT INTO product_comparisons (user_id, product_id, created_at)
                VALUES (?, ?, NOW())
            ");
            
            if ($stmt->execute([$userId, $productId])) {
                return ['success' => true, 'message' => 'Product added to comparison'];
            } else {
                return ['success' => false, 'error' => 'Failed to add to comparison'];
            }
        } catch (PDOException $e) {
            error_log("Error adding to comparison: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Remove product from comparison
     */
    public static function removeFromComparison($userId, $productId)
    {
        try {
            $stmt = self::$pdo->prepare("
                DELETE FROM product_comparisons 
                WHERE user_id = ? AND product_id = ?
            ");
            
            if ($stmt->execute([$userId, $productId])) {
                return ['success' => true, 'message' => 'Product removed from comparison'];
            } else {
                return ['success' => false, 'error' => 'Failed to remove from comparison'];
            }
        } catch (PDOException $e) {
            error_log("Error removing from comparison: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Get user's comparison list
     */
    public static function getComparisonList($userId)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT pc.*, p.*, c.name as category_name, pi.image_path as primary_image,
                       AVG(pr.rating) as average_rating, COUNT(pr.id) as review_count
                FROM product_comparisons pc
                JOIN products p ON pc.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.is_approved = 1
                WHERE pc.user_id = ? AND p.is_active = 1
                GROUP BY p.id
                ORDER BY pc.created_at ASC
            ");
            $stmt->execute([$userId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error fetching comparison list: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get comparison count for user
     */
    public static function getComparisonCount($userId)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT COUNT(*) as count
                FROM product_comparisons pc
                JOIN products p ON pc.product_id = p.id
                WHERE pc.user_id = ? AND p.is_active = 1
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            
            return $result['count'];
        } catch (PDOException $e) {
            error_log("Error getting comparison count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Clear user's comparison list
     */
    public static function clearComparison($userId)
    {
        try {
            $stmt = self::$pdo->prepare("DELETE FROM product_comparisons WHERE user_id = ?");
            
            if ($stmt->execute([$userId])) {
                return ['success' => true, 'message' => 'Comparison list cleared'];
            } else {
                return ['success' => false, 'error' => 'Failed to clear comparison'];
            }
        } catch (PDOException $e) {
            error_log("Error clearing comparison: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Get detailed comparison data
     */
    public static function getComparisonData($userId)
    {
        $products = self::getComparisonList($userId);
        
        if (empty($products)) {
            return ['products' => [], 'attributes' => [], 'comparison_matrix' => []];
        }

        // Get product attributes
        $productIds = array_column($products, 'id');
        $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
        
        try {
            $stmt = self::$pdo->prepare("
                SELECT product_id, attribute_name, attribute_value
                FROM product_attributes
                WHERE product_id IN ($placeholders)
                ORDER BY attribute_name
            ");
            $stmt->execute($productIds);
            $attributes = $stmt->fetchAll();

            // Organize attributes by product
            $productAttributes = [];
            foreach ($attributes as $attr) {
                $productAttributes[$attr['product_id']][$attr['attribute_name']] = $attr['attribute_value'];
            }

            // Get all unique attribute names
            $allAttributes = array_unique(array_column($attributes, 'attribute_name'));
            
            // Add standard product fields to comparison
            $standardFields = [
                'name' => 'Product Name',
                'price' => 'Price',
                'sale_price' => 'Sale Price',
                'stock_quantity' => 'Stock',
                'average_rating' => 'Rating',
                'review_count' => 'Reviews',
                'category_name' => 'Category'
            ];

            // Build comparison matrix
            $comparisonMatrix = [];
            
            // Add standard fields
            foreach ($standardFields as $field => $label) {
                $row = ['attribute' => $label, 'type' => 'standard'];
                foreach ($products as $product) {
                    $value = $product[$field];
                    
                    // Format values
                    if ($field === 'price' || $field === 'sale_price') {
                        $value = $value ? '$' . number_format($value, 2) : 'N/A';
                    } elseif ($field === 'average_rating') {
                        $value = $value ? round($value, 1) . '/5' : 'No rating';
                    } elseif ($field === 'review_count') {
                        $value = $value . ' reviews';
                    }
                    
                    $row['values'][$product['id']] = $value;
                }
                $comparisonMatrix[] = $row;
            }

            // Add custom attributes
            foreach ($allAttributes as $attrName) {
                $row = ['attribute' => $attrName, 'type' => 'custom'];
                foreach ($products as $product) {
                    $row['values'][$product['id']] = $productAttributes[$product['id']][$attrName] ?? 'N/A';
                }
                $comparisonMatrix[] = $row;
            }

            return [
                'products' => $products,
                'attributes' => $allAttributes,
                'comparison_matrix' => $comparisonMatrix
            ];

        } catch (PDOException $e) {
            error_log("Error getting comparison data: " . $e->getMessage());
            return ['products' => $products, 'attributes' => [], 'comparison_matrix' => []];
        }
    }

    /**
     * Check if product is in comparison
     */
    public static function isInComparison($userId, $productId)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT COUNT(*) as count
                FROM product_comparisons 
                WHERE user_id = ? AND product_id = ?
            ");
            $stmt->execute([$userId, $productId]);
            $result = $stmt->fetch();
            
            return $result['count'] > 0;
        } catch (PDOException $e) {
            error_log("Error checking comparison: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get comparison suggestions (similar products)
     */
    public static function getComparisonSuggestions($userId, $limit = 5)
    {
        try {
            // Get categories of products in comparison
            $stmt = self::$pdo->prepare("
                SELECT DISTINCT p.category_id
                FROM product_comparisons pc
                JOIN products p ON pc.product_id = p.id
                WHERE pc.user_id = ?
            ");
            $stmt->execute([$userId]);
            $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (empty($categories)) {
                return [];
            }

            $placeholders = str_repeat('?,', count($categories) - 1) . '?';
            
            $stmt = self::$pdo->prepare("
                SELECT p.*, c.name as category_name, pi.image_path as primary_image,
                       AVG(pr.rating) as average_rating
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                LEFT JOIN product_reviews pr ON p.id = pr.product_id AND pr.is_approved = 1
                WHERE p.category_id IN ($placeholders) 
                AND p.is_active = 1
                AND p.id NOT IN (
                    SELECT product_id FROM product_comparisons WHERE user_id = ?
                )
                GROUP BY p.id
                ORDER BY average_rating DESC, p.created_at DESC
                LIMIT ?
            ");
            
            $params = array_merge($categories, [$userId, $limit]);
            $stmt->execute($params);
            
            return $stmt->fetchAll();

        } catch (PDOException $e) {
            error_log("Error getting comparison suggestions: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Generate comparison report
     */
    public static function generateComparisonReport($userId)
    {
        $comparisonData = self::getComparisonData($userId);
        $products = $comparisonData['products'];
        
        if (empty($products)) {
            return ['success' => false, 'error' => 'No products to compare'];
        }

        $report = [
            'total_products' => count($products),
            'price_range' => [
                'min' => min(array_column($products, 'price')),
                'max' => max(array_column($products, 'price')),
                'average' => array_sum(array_column($products, 'price')) / count($products)
            ],
            'best_rated' => null,
            'most_reviewed' => null,
            'best_value' => null,
            'recommendations' => []
        ];

        // Find best rated product
        $bestRating = 0;
        foreach ($products as $product) {
            if ($product['average_rating'] > $bestRating) {
                $bestRating = $product['average_rating'];
                $report['best_rated'] = $product;
            }
        }

        // Find most reviewed product
        $mostReviews = 0;
        foreach ($products as $product) {
            if ($product['review_count'] > $mostReviews) {
                $mostReviews = $product['review_count'];
                $report['most_reviewed'] = $product;
            }
        }

        // Find best value (highest rating per dollar)
        $bestValue = 0;
        foreach ($products as $product) {
            if ($product['price'] > 0 && $product['average_rating'] > 0) {
                $value = $product['average_rating'] / $product['price'];
                if ($value > $bestValue) {
                    $bestValue = $value;
                    $report['best_value'] = $product;
                }
            }
        }

        // Generate recommendations
        if ($report['best_rated']) {
            $report['recommendations'][] = "Best rated: " . $report['best_rated']['name'] . " ({$report['best_rated']['average_rating']}/5)";
        }
        
        if ($report['best_value'] && $report['best_value']['id'] !== $report['best_rated']['id']) {
            $report['recommendations'][] = "Best value: " . $report['best_value']['name'];
        }

        return ['success' => true, 'report' => $report];
    }

    /**
     * Get maximum comparison items allowed
     */
    public static function getMaxCompareItems()
    {
        return self::$maxCompareItems;
    }
}
