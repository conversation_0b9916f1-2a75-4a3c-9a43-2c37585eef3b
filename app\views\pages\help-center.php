<div class="help-center">
    <div class="container">
        <!-- <PERSON> Header -->
        <div class="page-header">
            <h1>Help Center</h1>
            <p>Find answers to common questions and get the support you need</p>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-container">
                <input type="text" id="helpSearch" class="search-input" placeholder="Search for help articles...">
                <button class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="quick-links">
            <h2>Quick Links</h2>
            <div class="links-grid">
                <a href="#orders" class="quick-link-card">
                    <i class="fas fa-shopping-bag"></i>
                    <h3>Orders & Shipping</h3>
                    <p>Track orders, shipping info, delivery</p>
                </a>
                <a href="#returns" class="quick-link-card">
                    <i class="fas fa-undo"></i>
                    <h3>Returns & Refunds</h3>
                    <p>Return policy, refund process</p>
                </a>
                <a href="#account" class="quick-link-card">
                    <i class="fas fa-user"></i>
                    <h3>Account & Settings</h3>
                    <p>Account management, preferences</p>
                </a>
                <a href="#payments" class="quick-link-card">
                    <i class="fas fa-credit-card"></i>
                    <h3>Payments & Billing</h3>
                    <p>Payment methods, billing issues</p>
                </a>
                <a href="#products" class="quick-link-card">
                    <i class="fas fa-box"></i>
                    <h3>Products & Inventory</h3>
                    <p>Product information, availability</p>
                </a>
                <a href="#technical" class="quick-link-card">
                    <i class="fas fa-tools"></i>
                    <h3>Technical Support</h3>
                    <p>Website issues, troubleshooting</p>
                </a>
            </div>
        </div>

        <!-- FAQ Sections -->
        <div class="faq-sections">
            <!-- Orders & Shipping -->
            <div id="orders" class="faq-section">
                <h2>Orders & Shipping</h2>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>How can I track my order?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>You can track your order in several ways:</p>
                            <ul>
                                <li>Log into your account and visit "My Orders"</li>
                                <li>Use the tracking number from your order confirmation email</li>
                                <li>Contact our customer service team</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>How long does shipping take?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Shipping times vary by location and method:</p>
                            <ul>
                                <li>Standard shipping: 3-5 business days</li>
                                <li>Express shipping: 1-2 business days</li>
                                <li>International shipping: 7-14 business days</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>Can I change or cancel my order?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Orders can be modified or cancelled within 2 hours of placement. After that, please contact our customer service team immediately for assistance.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Returns & Refunds -->
            <div id="returns" class="faq-section">
                <h2>Returns & Refunds</h2>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>What is your return policy?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>We offer a 30-day return policy for most items. Products must be:</p>
                            <ul>
                                <li>In their original condition</li>
                                <li>In original packaging</li>
                                <li>Returned within 30 days of delivery</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>How do I start a return?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To start a return:</p>
                            <ol>
                                <li>Log into your account</li>
                                <li>Go to "My Orders"</li>
                                <li>Select the order you want to return</li>
                                <li>Click "Start Return"</li>
                                <li>Follow the instructions to print a return label</li>
                            </ol>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>When will I receive my refund?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Refunds are typically processed within 3-5 business days after we receive your return. The time to appear in your account depends on your bank or credit card provider.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account & Settings -->
            <div id="account" class="faq-section">
                <h2>Account & Settings</h2>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>How do I reset my password?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To reset your password:</p>
                            <ol>
                                <li>Go to the login page</li>
                                <li>Click "Forgot Password"</li>
                                <li>Enter your email address</li>
                                <li>Check your email for reset instructions</li>
                                <li>Follow the link to create a new password</li>
                            </ol>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>How do I update my account information?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>You can update your account information by:</p>
                            <ol>
                                <li>Logging into your account</li>
                                <li>Going to "Account Settings"</li>
                                <li>Editing the information you want to change</li>
                                <li>Saving your changes</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payments & Billing -->
            <div id="payments" class="faq-section">
                <h2>Payments & Billing</h2>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>What payment methods do you accept?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>We accept the following payment methods:</p>
                            <ul>
                                <li>Credit cards (Visa, MasterCard, American Express)</li>
                                <li>Debit cards</li>
                                <li>PayPal</li>
                                <li>Apple Pay</li>
                                <li>Google Pay</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>Is my payment information secure?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, we use industry-standard SSL encryption to protect your payment information. We never store your full credit card details on our servers.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products & Inventory -->
            <div id="products" class="faq-section">
                <h2>Products & Inventory</h2>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>How do I know if a product is in stock?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Product availability is shown on each product page. If an item is out of stock, you can usually sign up for email notifications when it becomes available again.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>Do you offer product warranties?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Most products come with manufacturer warranties. Warranty information is listed on individual product pages. We also offer extended warranty options for many items.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Support -->
            <div id="technical" class="faq-section">
                <h2>Technical Support</h2>
                <div class="faq-list">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>The website is not loading properly</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Try these troubleshooting steps:</p>
                            <ol>
                                <li>Clear your browser cache and cookies</li>
                                <li>Try a different browser</li>
                                <li>Check your internet connection</li>
                                <li>Disable browser extensions</li>
                                <li>Contact our technical support if the issue persists</li>
                            </ol>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>I can't log into my account</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>If you're having trouble logging in:</p>
                            <ol>
                                <li>Make sure you're using the correct email address</li>
                                <li>Check that Caps Lock is off</li>
                                <li>Try resetting your password</li>
                                <li>Clear your browser cache</li>
                                <li>Contact customer support for assistance</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="contact-support">
            <div class="support-card">
                <h2>Still Need Help?</h2>
                <p>Our customer service team is here to help you with any questions or concerns.</p>
                <div class="support-options">
                    <a href="<?= UrlHelper::url('/contact') ?>" class="btn btn-primary">
                        <i class="fas fa-envelope"></i>
                        Contact Us
                    </a>
                    <a href="tel:+15551234567" class="btn btn-outline">
                        <i class="fas fa-phone"></i>
                        Call Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .help-center {
        padding: var(--spacing-xl) 0;
        background-color: var(--dark-surface);
        min-height: 100vh;
    }

    .page-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .page-header h1 {
        font-size: var(--font-size-4xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-header p {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Search Section */
    .search-section {
        margin-bottom: var(--spacing-2xl);
    }

    .search-container {
        max-width: 600px;
        margin: 0 auto;
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-lg) var(--spacing-xl);
        padding-right: 60px;
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        transition: all var(--transition-fast);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
    }

    .search-btn {
        position: absolute;
        right: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
        cursor: pointer;
        transition: color var(--transition-fast);
    }

    .search-btn:hover {
        color: var(--primary-purple);
    }

    /* Quick Links */
    .quick-links {
        margin-bottom: var(--spacing-3xl);
    }

    .quick-links h2 {
        text-align: center;
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xl);
    }

    .links-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .quick-link-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-decoration: none;
        color: var(--text-primary);
        transition: all var(--transition-fast);
        text-align: center;
    }

    .quick-link-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-purple);
    }

    .quick-link-card i {
        font-size: var(--font-size-3xl);
        color: var(--primary-purple);
        margin-bottom: var(--spacing-md);
    }

    .quick-link-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    .quick-link-card p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin: 0;
    }

    /* FAQ Sections */
    .faq-sections {
        margin-bottom: var(--spacing-3xl);
    }

    .faq-section {
        margin-bottom: var(--spacing-2xl);
    }

    .faq-section h2 {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 2px solid var(--border-color);
    }

    .faq-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .faq-item {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
        transition: all var(--transition-fast);
    }

    .faq-item:hover {
        box-shadow: var(--shadow-lg);
    }

    .faq-question {
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        transition: background-color var(--transition-fast);
    }

    .faq-question:hover {
        background-color: var(--dark-surface);
    }

    .faq-question h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .faq-question i {
        color: var(--text-secondary);
        transition: transform var(--transition-fast);
    }

    .faq-item.active .faq-question i {
        transform: rotate(180deg);
    }

    .faq-answer {
        padding: 0 var(--spacing-lg);
        max-height: 0;
        overflow: hidden;
        transition: all var(--transition-fast);
    }

    .faq-item.active .faq-answer {
        padding: 0 var(--spacing-lg) var(--spacing-lg);
        max-height: 500px;
    }

    .faq-answer p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: var(--spacing-md);
    }

    .faq-answer ul,
    .faq-answer ol {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-left: var(--spacing-lg);
    }

    .faq-answer li {
        margin-bottom: var(--spacing-sm);
    }

    /* Contact Support */
    .contact-support {
        text-align: center;
    }

    .support-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        max-width: 600px;
        margin: 0 auto;
    }

    .support-card h2 {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .support-card p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
    }

    .support-options {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background-color: var(--dark-surface);
        border-color: var(--primary-purple);
        color: var(--primary-purple);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .help-center {
            padding: var(--spacing-lg) 0;
        }

        .page-header h1 {
            font-size: var(--font-size-3xl);
        }

        .links-grid {
            grid-template-columns: 1fr;
        }

        .support-options {
            flex-direction: column;
        }

        .search-input {
            padding: var(--spacing-md) var(--spacing-lg);
            padding-right: 50px;
            font-size: var(--font-size-base);
        }
    }

    /* Light Mode Support */
    .light-mode .help-center {
        background-color: var(--light-surface);
    }

    .light-mode .quick-link-card,
    .light-mode .faq-item,
    .light-mode .support-card {
        background-color: var(--light-card);
        border-color: var(--light-border);
    }

    .light-mode .search-input {
        background-color: var(--light-card);
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .search-input:focus {
        background-color: var(--light-card);
    }

    .light-mode .faq-question:hover {
        background-color: var(--light-surface);
    }

    .light-mode .btn-outline {
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .btn-outline:hover {
        background-color: var(--light-surface);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // FAQ Toggle Functionality
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            question.addEventListener('click', () => {
                const isActive = item.classList.contains('active');

                // Close all other FAQ items
                faqItems.forEach(otherItem => {
                    otherItem.classList.remove('active');
                });

                // Toggle current item
                if (!isActive) {
                    item.classList.add('active');
                }
            });
        });

        // Search Functionality
        const searchInput = document.getElementById('helpSearch');
        const faqSections = document.querySelectorAll('.faq-section');

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            faqSections.forEach(section => {
                const faqItems = section.querySelectorAll('.faq-item');
                let hasMatch = false;

                faqItems.forEach(item => {
                    const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
                    const answer = item.querySelector('.faq-answer').textContent.toLowerCase();

                    if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                        item.style.display = 'block';
                        hasMatch = true;
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Show/hide section based on matches
                section.style.display = hasMatch ? 'block' : 'none';
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    });
</script>