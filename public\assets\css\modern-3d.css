/* ===== MODERN 3D DESIGN SYSTEM ===== */

/* Enhanced CSS Variables for 3D Effects */
:root {
    /* 3D Transform Variables */
    --perspective: 1000px;
    --rotate-x: 0deg;
    --rotate-y: 0deg;
    --rotate-z: 0deg;
    --translate-z: 0px;
    --scale-3d: 1;

    /* Enhanced Shadows for 3D */
    --shadow-3d-sm: 0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-3d-md: 0 4px 8px rgba(0, 0, 0, 0.12), 0 16px 32px rgba(0, 0, 0, 0.12);
    --shadow-3d-lg: 0 8px 16px rgba(0, 0, 0, 0.15), 0 32px 64px rgba(0, 0, 0, 0.15);
    --shadow-3d-xl: 0 16px 32px rgba(0, 0, 0, 0.2), 0 64px 128px rgba(0, 0, 0, 0.2);

    /* Glassmorphism Variables */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-backdrop: blur(20px) saturate(180%);

    /* Neumorphism Variables */
    --neu-light: rgba(255, 255, 255, 0.1);
    --neu-dark: rgba(0, 0, 0, 0.2);
    --neu-inset-light: inset 2px 2px 4px var(--neu-dark);
    --neu-inset-dark: inset -2px -2px 4px var(--neu-light);

    /* Animation Variables */
    --bounce-timing: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --smooth-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --elastic-timing: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ===== 3D UTILITY CLASSES ===== */

/* 3D Container */
.perspective {
    perspective: var(--perspective);
    perspective-origin: center center;
}

.preserve-3d {
    transform-style: preserve-3d;
}

/* 3D Transforms */
.transform-3d {
    transform: perspective(var(--perspective)) rotateX(var(--rotate-x)) rotateY(var(--rotate-y)) rotateZ(var(--rotate-z)) translateZ(var(--translate-z)) scale3d(var(--scale-3d), var(--scale-3d), var(--scale-3d));
}

.hover-lift {
    transition: transform 0.3s var(--smooth-timing), box-shadow 0.3s var(--smooth-timing);
}

.hover-lift:hover {
    transform: translateY(-8px) translateZ(20px);
    box-shadow: var(--shadow-3d-lg);
}

.hover-tilt {
    transition: transform 0.3s var(--smooth-timing);
}

.hover-tilt:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(-5deg) translateZ(10px);
}

/* ===== 3D CARD COMPONENTS ===== */

.card-3d {
    background: var(--dark-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    position: relative;
    transform-style: preserve-3d;
    transition: all 0.3s var(--smooth-timing);
    box-shadow: var(--shadow-3d-sm);
    border: 1px solid var(--border-color);
}

.card-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--neu-light), transparent);
    border-radius: var(--radius-lg);
    opacity: 0;
    transition: opacity 0.3s var(--smooth-timing);
    pointer-events: none;
}

.card-3d:hover::before {
    opacity: 1;
}

.card-3d:hover {
    transform: translateY(-5px) rotateX(2deg) rotateY(-2deg);
    box-shadow: var(--shadow-3d-md);
}

/* Product Card 3D */
.product-card-3d {
    background: var(--dark-card);
    border-radius: var(--radius-xl);
    overflow: hidden;
    position: relative;
    transform-style: preserve-3d;
    transition: all 0.4s var(--smooth-timing);
    box-shadow: var(--shadow-3d-sm);
    border: 1px solid var(--border-color);
}

.product-card-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
            rgba(126, 87, 194, 0.1) 0%,
            transparent 50%,
            rgba(126, 87, 194, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s var(--smooth-timing);
    pointer-events: none;
    z-index: 1;
}

.product-card-3d:hover::before {
    opacity: 1;
}

.product-card-3d:hover {
    transform: translateY(-10px) rotateX(3deg) rotateY(-3deg) scale(1.02);
    box-shadow: var(--shadow-3d-lg);
}

.product-card-3d .product-image {
    position: relative;
    overflow: hidden;
    height: 200px;
    background: var(--dark-surface);
}

.product-card-3d .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s var(--smooth-timing);
}

.product-card-3d:hover .product-image img {
    transform: scale(1.1) rotateZ(1deg);
}

/* ===== 3D BUTTONS ===== */

.btn-3d {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s var(--smooth-timing);
    transform-style: preserve-3d;
    overflow: hidden;
}

.btn-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.3s var(--smooth-timing);
}

.btn-3d:hover::before {
    transform: translateX(100%);
}

.btn-3d-primary {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
    color: var(--white);
    box-shadow: var(--shadow-3d-sm);
}

.btn-3d-primary:hover {
    transform: translateY(-2px) translateZ(5px);
    box-shadow: var(--shadow-3d-md);
    background: linear-gradient(135deg, var(--primary-purple-light), var(--primary-purple));
}

.btn-3d-primary:active {
    transform: translateY(0) translateZ(0);
    box-shadow: var(--shadow-3d-sm);
}

/* ===== GLASSMORPHISM COMPONENTS ===== */

.glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
}

.glass-card {
    background: rgba(45, 45, 45, 0.3);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

/* ===== NEUMORPHISM COMPONENTS ===== */

.neu {
    background: var(--dark-card);
    box-shadow: 8px 8px 16px var(--neu-dark), -8px -8px 16px var(--neu-light);
    border-radius: var(--radius-lg);
    border: none;
}

.neu-inset {
    background: var(--dark-card);
    box-shadow: var(--neu-inset-light), var(--neu-inset-dark);
    border-radius: var(--radius-md);
}

/* ===== LOADING ANIMATIONS ===== */

.loading-3d {
    display: inline-block;
    width: 40px;
    height: 40px;
    position: relative;
    transform-style: preserve-3d;
    animation: rotate3d 2s infinite linear;
}

.loading-3d::before,
.loading-3d::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid var(--primary-purple);
    border-radius: 50%;
}

.loading-3d::after {
    transform: rotateY(90deg);
    border-top-color: var(--primary-purple-light);
}

@keyframes rotate3d {
    0% {
        transform: rotateX(0deg) rotateY(0deg);
    }

    25% {
        transform: rotateX(90deg) rotateY(0deg);
    }

    50% {
        transform: rotateX(90deg) rotateY(90deg);
    }

    75% {
        transform: rotateX(0deg) rotateY(90deg);
    }

    100% {
        transform: rotateX(0deg) rotateY(0deg);
    }
}

/* ===== FLOATING ANIMATIONS ===== */

.float {
    animation: float 3s ease-in-out infinite;
}

.float-delayed {
    animation: float 3s ease-in-out infinite;
    animation-delay: 1s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotateZ(0deg);
    }

    50% {
        transform: translateY(-10px) rotateZ(2deg);
    }
}

/* ===== PULSE GLOW EFFECT ===== */

.pulse-glow {
    position: relative;
    overflow: hidden;
}

.pulse-glow::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, var(--primary-purple) 0%, transparent 70%);
    opacity: 0;
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {

    0%,
    100% {
        opacity: 0;
        transform: scale(0.8);
    }

    50% {
        opacity: 0.3;
        transform: scale(1.2);
    }
}

/* ===== HOME PAGE SPECIFIC 3D STYLES ===== */

/* Hero Section 3D */
.hero {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(126, 87, 194, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-visual-3d {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-main-icon {
    font-size: 8rem;
    color: var(--primary-purple);
    position: relative;
    z-index: 3;
}

.floating-elements-3d {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-elements-3d .element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.8;
}

.floating-elements-3d .element-1 {
    top: 20%;
    left: 20%;
    color: #FFD700;
}

.floating-elements-3d .element-2 {
    top: 30%;
    right: 20%;
    color: #FF6B6B;
}

.floating-elements-3d .element-3 {
    bottom: 30%;
    left: 30%;
    color: #4ECDC4;
}

.floating-elements-3d .element-4 {
    bottom: 20%;
    right: 30%;
    color: var(--primary-purple-light);
}

.hero-bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-bg-shapes .shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-purple), transparent);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.hero-bg-shapes .shape-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.hero-bg-shapes .shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.hero-bg-shapes .shape-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 50%;
    animation-delay: 4s;
}

/* Categories Grid 3D */
.categories-grid-3d {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.category-card-3d {
    padding: var(--spacing-xl);
    text-align: center;
    text-decoration: none;
    color: inherit;
    position: relative;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.category-icon-3d {
    font-size: 3rem;
    color: var(--primary-purple);
    margin-bottom: var(--spacing-md);
    transition: transform 0.3s var(--smooth-timing);
}

.category-card-3d:hover .category-icon-3d {
    transform: scale(1.1) rotateY(10deg);
}

.category-info {
    text-align: center;
}

.category-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

.category-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
}

.category-arrow {
    color: var(--primary-purple);
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s var(--smooth-timing);
}

.category-card-3d:hover .category-arrow {
    opacity: 1;
    transform: translateX(0);
}

/* Products Grid 3D */
.products-grid-3d {
    display: grid;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.products-grid-3d.grid-cols-5 {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
}

/* Enhanced Product Card 3D */
.product-card-3d.compact-card {
    background: var(--dark-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all 0.4s var(--smooth-timing);
}

.product-card-3d.compact-card .product-image {
    height: 180px;
    position: relative;
    overflow: hidden;
}

.product-card-3d.compact-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s var(--smooth-timing);
}

.product-card-3d.compact-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: opacity 0.3s var(--smooth-timing);
}

.product-card-3d:hover .product-overlay {
    opacity: 1;
}

.product-badge {
    position: absolute;
    top: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.sale-badge {
    right: var(--spacing-sm);
    background: var(--error-color);
    color: white;
}

.product-badge.featured-badge {
    left: var(--spacing-sm);
    background: var(--warning-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-visual-3d {
        height: 300px;
    }

    .hero-main-icon {
        font-size: 6rem;
    }

    .categories-grid-3d {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }

    .products-grid-3d.grid-cols-5 {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}