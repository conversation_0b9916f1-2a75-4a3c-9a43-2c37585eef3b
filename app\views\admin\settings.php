<section class="admin-section">
    <h1>Site Settings</h1>
    <?php if (!empty($success)): ?>
        <div class="alert alert-success">Settings updated successfully!</div>
    <?php endif; ?>
    <?php if (!empty($error)): ?>
        <div class="alert alert-danger">Error: <?= htmlspecialchars($error) ?></div>
    <?php endif; ?>
    <form action="/admin/settings" method="POST" class="admin-form">
        <label>Site Name <input type="text" name="site_name" value="<?= htmlspecialchars($settings['site_name'] ?? 'Cleanance Lab') ?>" required></label>
        <label>Site Description <input type="text" name="site_description" value="<?= htmlspecialchars($settings['site_description'] ?? 'Modern e-commerce platform') ?>"></label>
        <label>Currency <input type="text" name="currency" value="<?= htmlspecialchars($settings['currency'] ?? 'USD') ?>"></label>
        <label>Tax Rate (%) <input type="number" name="tax_rate" step="0.01" value="<?= $settings['tax_rate'] ?? '8.5' ?>"></label>
        <label>Shipping Cost <input type="number" name="shipping_cost" step="0.01" value="<?= $settings['shipping_cost'] ?? '10.00' ?>"></label>
        <label>Enable Reviews
            <select name="enable_reviews">
                <option value="1" <?= (!isset($settings['enable_reviews']) || $settings['enable_reviews']) ? 'selected' : '' ?>>Yes</option>
                <option value="0" <?= (isset($settings['enable_reviews']) && !$settings['enable_reviews']) ? 'selected' : '' ?>>No</option>
            </select>
        </label>
        <label>Enable Wishlist
            <select name="enable_wishlist">
                <option value="1" <?= (!isset($settings['enable_wishlist']) || $settings['enable_wishlist']) ? 'selected' : '' ?>>Yes</option>
                <option value="0" <?= (isset($settings['enable_wishlist']) && !$settings['enable_wishlist']) ? 'selected' : '' ?>>No</option>
            </select>
        </label>
        <label>Enable Discount Codes
            <select name="enable_discount_codes">
                <option value="1" <?= (!isset($settings['enable_discount_codes']) || $settings['enable_discount_codes']) ? 'selected' : '' ?>>Yes</option>
                <option value="0" <?= (isset($settings['enable_discount_codes']) && !$settings['enable_discount_codes']) ? 'selected' : '' ?>>No</option>
            </select>
        </label>
        <button type="submit" class="btn btn-primary">Save Settings</button>
    </form>
</section>
<style>
    .admin-section {
        padding: 40px 0;
        max-width: 600px;
        margin: 0 auto;
    }

    .admin-form {
        display: flex;
        flex-direction: column;
        gap: 18px;
        background: var(--dark-card);
        padding: 32px;
        border-radius: 12px;
    }

    .admin-form label {
        color: var(--text-primary);
        font-weight: 500;
    }

    .admin-form input,
    .admin-form select {
        width: 100%;
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        background: var(--dark-surface);
        color: var(--text-primary);
    }

    .alert {
        padding: 12px 18px;
        border-radius: 8px;
        margin-bottom: 18px;
    }

    .alert-success {
        background: #43a047;
        color: #fff;
    }

    .alert-danger {
        background: #e53935;
        color: #fff;
    }
</style>