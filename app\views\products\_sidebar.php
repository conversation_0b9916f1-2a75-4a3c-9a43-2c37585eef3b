<div class="filters-sidebar" id="filtersSidebar">
    <div class="filters-header">
        <h3>Filters</h3>
        <button class="clear-filters" onclick="clearFilters()">Clear All</button>
    </div>

    <!-- Search Filter -->
    <div class="filter-group">
        <h4>Search</h4>
        <form action="" method="GET" class="search-filter-form" id="searchForm">
            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                placeholder="Search products..." class="search-input" id="searchInput">
            <button type="submit" class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>

    <!-- Category Filter -->
    <div class="filter-group">
        <h4>Categories</h4>
        <div class="filter-options">
            <label class="filter-option">
                <input type="radio" name="category" value=""
                    <?= empty($currentCategory) ? 'checked' : '' ?>
                    onchange="filterProducts()">
                <span>All Categories</span>
            </label>
            <?php foreach ($categories as $category): ?>
                <label class="filter-option">
                    <input type="radio" name="category" value="<?= htmlspecialchars($category['slug']) ?>"
                        <?= $currentCategory === $category['slug'] ? 'checked' : '' ?>
                        onchange="filterProducts()">
                    <span><?= htmlspecialchars($category['name']) ?></span>
                </label>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Price Filter -->
    <div class="filter-group">
        <h4>Price Range</h4>
        <div class="price-filter">
            <div class="price-inputs">
                <input type="number" id="minPrice" name="min_price"
                    value="<?= htmlspecialchars($minPrice) ?>"
                    placeholder="Min" min="0" step="0.01">
                <span>-</span>
                <input type="number" id="maxPrice" name="max_price"
                    value="<?= htmlspecialchars($maxPrice) ?>"
                    placeholder="Max" min="0" step="0.01">
            </div>
            <button type="button" class="btn btn-primary btn-sm" onclick="filterProducts()">
                Apply
            </button>
        </div>
    </div>

    <!-- Availability Filter -->
    <div class="filter-group">
        <h4>Availability</h4>
        <div class="filter-options">
            <label class="filter-option">
                <input type="checkbox" name="in_stock" value="1"
                    <?= isset($_GET['in_stock']) ? 'checked' : '' ?>
                    onchange="filterProducts()">
                <span>In Stock</span>
            </label>
            <label class="filter-option">
                <input type="checkbox" name="on_sale" value="1"
                    <?= isset($_GET['on_sale']) ? 'checked' : '' ?>
                    onchange="filterProducts()">
                <span>On Sale</span>
            </label>
        </div>
    </div>
</div>