<div class="container mt-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>My Account
                    </h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/asma/public/account" class="list-group-item list-group-item-action active">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="/asma/public/profile" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-edit me-2"></i>Profile Settings
                    </a>
                    <a href="/asma/public/orders" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>My Orders
                    </a>
                    <a href="/asma/public/account/addresses" class="list-group-item list-group-item-action">
                        <i class="fas fa-map-marker-alt me-2"></i>Addresses
                    </a>
                    <a href="/asma/public/wishlist" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i>Wishlist
                    </a>
                    <a href="/asma/public/change-password" class="list-group-item list-group-item-action">
                        <i class="fas fa-key me-2"></i>Change Password
                    </a>
                    <a href="/asma/public/account/settings" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2"></i>Account Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Welcome Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-2">Welcome back, <?php echo htmlspecialchars($_SESSION['user']['first_name'] ?? 'User'); ?>!</h4>
                            <p class="text-muted mb-0">Manage your account, view orders, and update your preferences.</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="avatar-placeholder bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 2rem;">
                                <?php echo strtoupper(substr($_SESSION['user']['first_name'] ?? 'U', 0, 1)); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card shadow-sm text-center">
                        <div class="card-body">
                            <i class="fas fa-shopping-bag text-primary mb-2" style="font-size: 2rem;"></i>
                            <h5 class="card-title"><?php echo $orderCount ?? 0; ?></h5>
                            <p class="card-text text-muted">Total Orders</p>
                            <a href="/asma/public/orders" class="btn btn-outline-primary btn-sm">View Orders</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card shadow-sm text-center">
                        <div class="card-body">
                            <i class="fas fa-heart text-danger mb-2" style="font-size: 2rem;"></i>
                            <h5 class="card-title"><?php echo $wishlistCount ?? 0; ?></h5>
                            <p class="card-text text-muted">Wishlist Items</p>
                            <a href="/asma/public/wishlist" class="btn btn-outline-danger btn-sm">View Wishlist</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card shadow-sm text-center">
                        <div class="card-body">
                            <i class="fas fa-map-marker-alt text-success mb-2" style="font-size: 2rem;"></i>
                            <h5 class="card-title"><?php echo $addressCount ?? 0; ?></h5>
                            <p class="card-text text-muted">Saved Addresses</p>
                            <a href="/asma/public/account/addresses" class="btn btn-outline-success btn-sm">Manage Addresses</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Orders
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentOrders)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentOrders as $order): ?>
                                        <tr>
                                            <td>#<?php echo $order['id']; ?></td>
                                            <td><?php echo date('M d, Y', strtotime($order['created_at'])); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo getStatusColor($order['status']); ?>">
                                                    <?php echo ucfirst($order['status']); ?>
                                                </span>
                                            </td>
                                            <td>$<?php echo number_format($order['total_amount'], 2); ?></td>
                                            <td>
                                                <a href="/asma/public/order/<?php echo $order['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    View Details
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="/asma/public/orders" class="btn btn-primary">View All Orders</a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-bag text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted">No orders yet</h5>
                            <p class="text-muted">Start shopping to see your order history here.</p>
                            <a href="/asma/public/products" class="btn btn-primary">Browse Products</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function getStatusColor($status)
{
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'processing':
            return 'info';
        case 'shipped':
            return 'primary';
        case 'delivered':
            return 'success';
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}
?>

<style>
    /* Dashboard Navigation */
    .dashboard-nav {
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .dashboard-nav-list {
        display: flex;
        list-style: none;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
        margin: 0;
        padding: 0;
    }

    .dashboard-nav-item {
        flex: 1;
        min-width: 200px;
    }

    .dashboard-nav-link {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        color: var(--text-primary);
        text-decoration: none;
        border-radius: var(--radius-md);
        transition: all var(--transition-fast);
        background: var(--dark-surface);
        border: 1px solid var(--border-color);
    }

    .dashboard-nav-link:hover {
        background: var(--primary-purple);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .dashboard-nav-link.active {
        background: var(--primary-purple);
        color: var(--white);
        box-shadow: var(--shadow-md);
    }

    .dashboard-nav-link i {
        font-size: var(--font-size-lg);
        width: 20px;
        text-align: center;
    }

    /* Dashboard Cards */
    .dashboard-card {
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        transition: all var(--transition-normal);
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-purple);
    }

    .dashboard-card-header {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: var(--white);
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-color);
    }

    .dashboard-card-header h5 {
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .dashboard-card-body {
        padding: var(--spacing-lg);
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .stat-card {
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-purple), var(--primary-purple-light));
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-purple);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-2xl);
        color: var(--white);
    }

    .stat-number {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--primary-purple);
        margin-bottom: var(--spacing-sm);
    }

    .stat-label {
        color: var(--text-secondary);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: var(--font-size-sm);
    }

    /* Recent Activity */
    .activity-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        background: var(--dark-surface);
        margin-bottom: var(--spacing-sm);
        transition: all var(--transition-fast);
    }

    .activity-item:hover {
        background: var(--primary-purple);
        color: var(--white);
        transform: translateX(4px);
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-purple);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--font-size-lg);
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        margin-bottom: var(--spacing-xs);
    }

    .activity-time {
        font-size: var(--font-size-sm);
        color: var(--text-muted);
    }

    /* Buttons */
    .btn {
        border-radius: var(--radius-md);
        font-weight: 500;
        padding: var(--spacing-sm) var(--spacing-lg);
        transition: all var(--transition-fast);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        border: none;
        cursor: pointer;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: var(--white);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, var(--primary-purple-dark), var(--primary-purple));
        color: var(--white);
    }

    .btn-outline {
        background: transparent;
        color: var(--primary-purple);
        border: 2px solid var(--primary-purple);
    }

    .btn-outline:hover {
        background: var(--primary-purple);
        color: var(--white);
    }

    /* Page Header */
    .page-header {
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        border: 1px solid var(--border-color);
    }

    .page-title {
        color: var(--text-primary);
        font-weight: 700;
        margin-bottom: var(--spacing-sm);
        font-size: var(--font-size-3xl);
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Form Controls */
    .form-control {
        background: var(--dark-surface);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
        border-radius: var(--radius-md);
        padding: var(--spacing-sm) var(--spacing-md);
        transition: all var(--transition-fast);
    }

    .form-control:focus {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 0.2rem rgba(126, 87, 194, 0.25);
        outline: none;
    }

    .form-control[readonly] {
        background: var(--dark-bg);
        opacity: 0.8;
    }

    .form-label {
        color: var(--text-primary);
        font-weight: 500;
        margin-bottom: var(--spacing-sm);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .dashboard-nav-list {
            flex-direction: column;
        }

        .dashboard-nav-item {
            min-width: auto;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- Dashboard Navigation -->
<nav class="dashboard-nav">
    <ul class="dashboard-nav-list">
        <li class="dashboard-nav-item">
            <a href="/asma/public/account" class="dashboard-nav-link active">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
        </li>
        <li class="dashboard-nav-item">
            <a href="/asma/public/orders" class="dashboard-nav-link">
                <i class="fas fa-shopping-bag"></i>
                <span>Orders</span>
            </a>
        </li>
        <li class="dashboard-nav-item">
            <a href="/asma/public/addresses" class="dashboard-nav-link">
                <i class="fas fa-map-marker-alt"></i>
                <span>Addresses</span>
            </a>
        </li>
        <li class="dashboard-nav-item">
            <a href="/asma/public/settings" class="dashboard-nav-link">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </li>
        <li class="dashboard-nav-item">
            <a href="/asma/public/wishlist" class="dashboard-nav-link">
                <i class="fas fa-heart"></i>
                <span>Wishlist</span>
            </a>
        </li>
    </ul>
</nav>

<!-- Page Header -->
<div class="page-header">
    <h1 class="page-title">Welcome back, <?php echo htmlspecialchars($_SESSION['user']['first_name'] ?? 'User'); ?>!</h1>
    <p class="page-subtitle">Manage your account, track orders, and update your preferences.</p>
</div>

<!-- Stats Overview -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-shopping-bag"></i>
        </div>
        <div class="stat-number"><?php echo $orderCount ?? 0; ?></div>
        <div class="stat-label">Total Orders</div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-box"></i>
        </div>
        <div class="stat-number"><?php echo $pendingOrders ?? 0; ?></div>
        <div class="stat-label">Pending Orders</div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-heart"></i>
        </div>
        <div class="stat-number"><?php echo $wishlistCount ?? 0; ?></div>
        <div class="stat-label">Wishlist Items</div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-map-marker-alt"></i>
        </div>
        <div class="stat-number"><?php echo $addressCount ?? 0; ?></div>
        <div class="stat-label">Saved Addresses</div>
    </div>
</div>

<!-- Dashboard Content -->
<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-8 mb-4">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <h5>
                    <i class="fas fa-user-circle"></i>
                    Profile Information
                </h5>
            </div>
            <div class="dashboard-card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">First Name</label>
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($_SESSION['user']['first_name'] ?? ''); ?>" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Last Name</label>
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($_SESSION['user']['last_name'] ?? ''); ?>" readonly>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-control" value="<?php echo htmlspecialchars($_SESSION['user']['email'] ?? ''); ?>" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Phone Number</label>
                    <input type="tel" class="form-control" value="<?php echo htmlspecialchars($_SESSION['user']['phone'] ?? 'Not provided'); ?>" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Member Since</label>
                    <input type="text" class="form-control" value="<?php echo date('F j, Y', strtotime($_SESSION['user']['created_at'] ?? 'now')); ?>" readonly>
                </div>
                <a href="/asma/public/account" class="btn btn-primary">
                    <i class="fas fa-edit"></i>Edit Profile
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <h5>
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="dashboard-card-body">
                <div class="d-grid gap-3">
                    <a href="/asma/public/orders" class="btn btn-outline">
                        <i class="fas fa-shopping-bag"></i>View Orders
                    </a>
                    <a href="/asma/public/addresses" class="btn btn-outline">
                        <i class="fas fa-map-marker-alt"></i>Manage Addresses
                    </a>
                    <a href="/asma/public/change-password" class="btn btn-outline">
                        <i class="fas fa-key"></i>Change Password
                    </a>
                    <a href="/asma/public/settings" class="btn btn-outline">
                        <i class="fas fa-cog"></i>Account Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="dashboard-card">
            <div class="dashboard-card-header">
                <h5>
                    <i class="fas fa-clock"></i>
                    Recent Orders
                </h5>
            </div>
            <div class="dashboard-card-body">
                <?php if (!empty($recentOrders)): ?>
                    <?php foreach ($recentOrders as $order): ?>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Order #<?php echo htmlspecialchars($order['id']); ?> placed</div>
                                <div class="activity-time"><?php echo date('M j, Y g:i A', strtotime($order['created_at'])); ?></div>
                            </div>
                            <div class="activity-amount">
                                <strong>$<?php echo number_format($order['total_amount'], 2); ?></strong>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <div class="text-center mt-4">
                        <a href="/asma/public/orders" class="btn btn-primary">View All Orders</a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">No recent orders</p>
                        <a href="/asma/public/products" class="btn btn-primary">Start Shopping</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>