<?php

/**
 * User Model
 * Handles all user-related database operations
 */

class User
{
    private $db;

    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Create a new user
     */
    public function create($data)
    {
        $sql = "INSERT INTO users (name, email, password, phone, address, city, state, zip_code, country, email_verified_at, verification_token, created_at) 
                VALUES (:name, :email, :password, :phone, :address, :city, :state, :zip_code, :country, :email_verified_at, :verification_token, NOW())";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'zip_code' => $data['zip_code'] ?? null,
            'country' => $data['country'] ?? null,
            'email_verified_at' => null,
            'verification_token' => bin2hex(random_bytes(32))
        ]);
    }

    /**
     * Find user by email
     */
    public function findByEmail($email)
    {
        $sql = "SELECT * FROM users WHERE email = :email";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['email' => $email]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Find user by ID
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM users WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update user profile
     */
    public function update($id, $data)
    {
        $sql = "UPDATE users SET name = :name, phone = :phone, address = :address, city = :city, state = :state, zip_code = :zip_code, country = :country, updated_at = NOW() WHERE id = :id";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'id' => $id,
            'name' => $data['name'],
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'zip_code' => $data['zip_code'] ?? null,
            'country' => $data['country'] ?? null
        ]);
    }

    /**
     * Update password
     */
    public function updatePassword($id, $password)
    {
        $sql = "UPDATE users SET password = :password, updated_at = NOW() WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'id' => $id,
            'password' => password_hash($password, PASSWORD_DEFAULT)
        ]);
    }

    /**
     * Verify email
     */
    public function verifyEmail($token)
    {
        $sql = "UPDATE users SET email_verified_at = NOW(), verification_token = NULL WHERE verification_token = :token";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['token' => $token]);
    }

    /**
     * Set reset token
     */
    public function setResetToken($email, $token)
    {
        $sql = "UPDATE users SET reset_token = :token, reset_token_expires_at = DATE_ADD(NOW(), INTERVAL 1 HOUR) WHERE email = :email";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['email' => $email, 'token' => $token]);
    }

    /**
     * Reset password with token
     */
    public function resetPassword($token, $password)
    {
        $sql = "UPDATE users SET password = :password, reset_token = NULL, reset_token_expires_at = NULL, updated_at = NOW() 
                WHERE reset_token = :token AND reset_token_expires_at > NOW()";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'token' => $token,
            'password' => password_hash($password, PASSWORD_DEFAULT)
        ]);
    }

    /**
     * Get all users (admin)
     */
    public function getAll($limit = 20, $offset = 0)
    {
        $sql = "SELECT id, name, email, phone, email_verified_at, created_at FROM users ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Count total users
     */
    public function count()
    {
        $sql = "SELECT COUNT(*) FROM users";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchColumn();
    }
}
