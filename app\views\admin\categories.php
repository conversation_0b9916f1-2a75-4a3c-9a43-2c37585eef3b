<section class="admin-section">
    <div class="admin-header">
        <h1>Categories</h1>
        <a href="/admin/categories/add" class="btn btn-primary">Add Category</a>
    </div>
    <div class="admin-table-wrapper">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Parent</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($categories)): foreach ($categories as $cat): ?>
                        <tr>
                            <td><?= $cat['id'] ?></td>
                            <td><?= htmlspecialchars($cat['name']) ?></td>
                            <td><?= htmlspecialchars($cat['parent_name'] ?? '-') ?></td>
                            <td><?= $cat['is_active'] ? 'Active' : 'Inactive' ?></td>
                            <td>
                                <a href="/admin/categories/edit/<?= $cat['id'] ?>" class="btn btn-sm">Edit</a>
                                <form action="/admin/categories/delete/<?= $cat['id'] ?>" method="POST" style="display:inline;">
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Delete this category?')">Delete</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach;
                else: ?>
                    <tr>
                        <td colspan="5" style="text-align:center; color:var(--text-secondary);">No categories found.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</section>
<style>
    .admin-section {
        padding: 40px 0;
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
    }

    .admin-table-wrapper {
        overflow-x: auto;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--dark-card);
    }

    .admin-table th,
    .admin-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--dark-surface);
        color: var(--primary-purple);
        font-weight: 600;
    }

    .btn-sm {
        padding: 4px 12px;
        font-size: 0.95em;
    }

    .btn-danger {
        background: #e53935;
        color: #fff;
    }

    .btn-danger:hover {
        background: #b71c1c;
    }
</style>