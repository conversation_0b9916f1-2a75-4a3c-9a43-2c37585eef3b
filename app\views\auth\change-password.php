<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        Change Password
                    </h4>
                </div>
                <div class="card-body p-4">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($success)): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <form action="/asma/public/change-password" method="POST" id="changePasswordForm">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Current Password
                            </label>
                            <input type="password"
                                class="form-control"
                                id="current_password"
                                name="current_password"
                                required>
                            <div class="form-text">Enter your current password to verify your identity.</div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="fas fa-key me-2"></i>New Password
                            </label>
                            <input type="password"
                                class="form-control"
                                id="new_password"
                                name="new_password"
                                required
                                minlength="8">
                            <div class="form-text">
                                Password must be at least 8 characters long and contain:
                                <ul class="mt-1 mb-0">
                                    <li>At least one uppercase letter</li>
                                    <li>At least one lowercase letter</li>
                                    <li>At least one number</li>
                                    <li>At least one special character</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-check-circle me-2"></i>Confirm New Password
                            </label>
                            <input type="password"
                                class="form-control"
                                id="confirm_password"
                                name="confirm_password"
                                required>
                            <div class="form-text">Re-enter your new password to confirm.</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update Password
                            </button>
                            <a href="/asma/public/profile" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Profile
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        // Password validation
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

        if (!passwordRegex.test(newPassword)) {
            e.preventDefault();
            alert('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.');
            return;
        }

        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('New password and confirm password do not match.');
            return;
        }
    });
</script>

<style>
    .card {
        border: none;
        border-radius: 15px;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: none;
    }

    .btn {
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 500;
    }

    .form-control {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
    }

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .alert {
        border-radius: 10px;
        border: none;
    }

    .form-text ul {
        font-size: 0.875rem;
        color: #6c757d;
    }
</style>