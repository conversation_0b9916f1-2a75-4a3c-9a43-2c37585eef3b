<div class="privacy-policy">
    <div class="container">
        <!-- <PERSON> Header -->
        <div class="page-header">
            <h1>Privacy Policy</h1>
            <p>Last updated: <?= date('F j, Y') ?></p>
        </div>

        <!-- Policy Overview -->
        <div class="policy-overview">
            <div class="overview-card">
                <h2>Your Privacy Matters</h2>
                <p>At Cleanance Lab, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or make a purchase.</p>
            </div>
        </div>

        <!-- Information We Collect -->
        <div class="policy-section">
            <h2>Information We Collect</h2>

            <div class="info-categories">
                <div class="category-card">
                    <h3>Personal Information</h3>
                    <p>When you create an account, make a purchase, or contact us, we may collect:</p>
                    <ul>
                        <li>Name and contact information (email, phone, address)</li>
                        <li>Payment information (processed securely by our payment partners)</li>
                        <li>Account credentials and preferences</li>
                        <li>Communication history with our support team</li>
                    </ul>
                </div>

                <div class="category-card">
                    <h3>Usage Information</h3>
                    <p>We automatically collect certain information about your use of our website:</p>
                    <ul>
                        <li>IP address and device information</li>
                        <li>Browser type and operating system</li>
                        <li>Pages visited and time spent on site</li>
                        <li>Search queries and product interactions</li>
                        <li>Referral sources and marketing campaign data</li>
                    </ul>
                </div>

                <div class="category-card">
                    <h3>Cookies and Tracking</h3>
                    <p>We use cookies and similar technologies to:</p>
                    <ul>
                        <li>Remember your preferences and login status</li>
                        <li>Analyze website traffic and performance</li>
                        <li>Provide personalized content and recommendations</li>
                        <li>Improve our services and user experience</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- How We Use Information -->
        <div class="policy-section">
            <h2>How We Use Your Information</h2>

            <div class="usage-grid">
                <div class="usage-item">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>Order Processing</h3>
                    <p>Process and fulfill your orders, send order confirmations, and provide customer support.</p>
                </div>

                <div class="usage-item">
                    <i class="fas fa-user"></i>
                    <h3>Account Management</h3>
                    <p>Create and manage your account, process returns, and maintain your preferences.</p>
                </div>

                <div class="usage-item">
                    <i class="fas fa-bullhorn"></i>
                    <h3>Marketing & Communications</h3>
                    <p>Send promotional emails, newsletters, and updates about new products and offers.</p>
                </div>

                <div class="usage-item">
                    <i class="fas fa-chart-line"></i>
                    <h3>Analytics & Improvement</h3>
                    <p>Analyze website usage, improve our services, and develop new features.</p>
                </div>

                <div class="usage-item">
                    <i class="fas fa-shield-alt"></i>
                    <h3>Security & Fraud Prevention</h3>
                    <p>Protect against fraud, ensure account security, and comply with legal obligations.</p>
                </div>

                <div class="usage-item">
                    <i class="fas fa-headset"></i>
                    <h3>Customer Support</h3>
                    <p>Respond to your inquiries, provide technical support, and resolve issues.</p>
                </div>
            </div>
        </div>

        <!-- Information Sharing -->
        <div class="policy-section">
            <h2>Information Sharing and Disclosure</h2>

            <div class="sharing-info">
                <div class="sharing-card">
                    <h3>We Do Not Sell Your Information</h3>
                    <p>We do not sell, rent, or trade your personal information to third parties for their marketing purposes.</p>
                </div>

                <div class="sharing-details">
                    <h3>When We May Share Information</h3>
                    <div class="sharing-list">
                        <div class="sharing-item">
                            <h4>Service Providers</h4>
                            <p>We share information with trusted third-party service providers who help us operate our business, such as payment processors, shipping partners, and customer support tools.</p>
                        </div>

                        <div class="sharing-item">
                            <h4>Legal Requirements</h4>
                            <p>We may disclose information when required by law, court order, or government request, or to protect our rights, property, or safety.</p>
                        </div>

                        <div class="sharing-item">
                            <h4>Business Transfers</h4>
                            <p>In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the business transaction.</p>
                        </div>

                        <div class="sharing-item">
                            <h4>With Your Consent</h4>
                            <p>We may share information with third parties when you explicitly consent to such sharing.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Security -->
        <div class="policy-section">
            <h2>Data Security</h2>

            <div class="security-info">
                <div class="security-card">
                    <h3>How We Protect Your Information</h3>
                    <div class="security-measures">
                        <div class="measure">
                            <i class="fas fa-lock"></i>
                            <div>
                                <h4>Encryption</h4>
                                <p>All sensitive data is encrypted using industry-standard SSL/TLS protocols.</p>
                            </div>
                        </div>

                        <div class="measure">
                            <i class="fas fa-server"></i>
                            <div>
                                <h4>Secure Servers</h4>
                                <p>Your data is stored on secure servers with multiple layers of protection.</p>
                            </div>
                        </div>

                        <div class="measure">
                            <i class="fas fa-user-shield"></i>
                            <div>
                                <h4>Access Controls</h4>
                                <p>Strict access controls limit who can access your personal information.</p>
                            </div>
                        </div>

                        <div class="measure">
                            <i class="fas fa-sync"></i>
                            <div>
                                <h4>Regular Updates</h4>
                                <p>We regularly update our security measures to protect against new threats.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Your Rights -->
        <div class="policy-section">
            <h2>Your Privacy Rights</h2>

            <div class="rights-grid">
                <div class="right-card">
                    <i class="fas fa-eye"></i>
                    <h3>Access</h3>
                    <p>You have the right to access the personal information we hold about you.</p>
                </div>

                <div class="right-card">
                    <i class="fas fa-edit"></i>
                    <h3>Correction</h3>
                    <p>You can request corrections to inaccurate or incomplete information.</p>
                </div>

                <div class="right-card">
                    <i class="fas fa-trash"></i>
                    <h3>Deletion</h3>
                    <p>You can request deletion of your personal information in certain circumstances.</p>
                </div>

                <div class="right-card">
                    <i class="fas fa-ban"></i>
                    <h3>Opt-Out</h3>
                    <p>You can opt out of marketing communications at any time.</p>
                </div>

                <div class="right-card">
                    <i class="fas fa-download"></i>
                    <h3>Portability</h3>
                    <p>You can request a copy of your data in a portable format.</p>
                </div>

                <div class="right-card">
                    <i class="fas fa-gavel"></i>
                    <h3>Objection</h3>
                    <p>You can object to certain processing of your personal information.</p>
                </div>
            </div>

            <div class="rights-contact">
                <h3>Exercising Your Rights</h3>
                <p>To exercise any of these rights, please contact us using the information provided below. We will respond to your request within 30 days.</p>
                <a href="<?= UrlHelper::url('/contact') ?>" class="btn btn-primary">
                    <i class="fas fa-envelope"></i>
                    Contact Us
                </a>
            </div>
        </div>

        <!-- Cookies Policy -->
        <div class="policy-section">
            <h2>Cookies and Tracking Technologies</h2>

            <div class="cookies-info">
                <div class="cookies-overview">
                    <h3>What Are Cookies?</h3>
                    <p>Cookies are small text files stored on your device that help us provide a better user experience. They remember your preferences, analyze site usage, and enable certain features.</p>
                </div>

                <div class="cookie-types">
                    <h3>Types of Cookies We Use</h3>
                    <div class="cookie-list">
                        <div class="cookie-item">
                            <h4>Essential Cookies</h4>
                            <p>Required for basic website functionality, such as shopping cart and login sessions.</p>
                        </div>

                        <div class="cookie-item">
                            <h4>Analytics Cookies</h4>
                            <p>Help us understand how visitors use our website and improve our services.</p>
                        </div>

                        <div class="cookie-item">
                            <h4>Marketing Cookies</h4>
                            <p>Used to deliver relevant advertisements and track marketing campaign performance.</p>
                        </div>

                        <div class="cookie-item">
                            <h4>Preference Cookies</h4>
                            <p>Remember your settings and preferences for a personalized experience.</p>
                        </div>
                    </div>
                </div>

                <div class="cookie-controls">
                    <h3>Managing Cookies</h3>
                    <p>You can control cookies through your browser settings. However, disabling certain cookies may affect website functionality.</p>
                    <div class="cookie-options">
                        <button class="btn btn-outline" onclick="acceptAllCookies()">
                            <i class="fas fa-check"></i>
                            Accept All Cookies
                        </button>
                        <button class="btn btn-outline" onclick="rejectNonEssential()">
                            <i class="fas fa-times"></i>
                            Reject Non-Essential
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Children's Privacy -->
        <div class="policy-section">
            <h2>Children's Privacy</h2>
            <div class="children-info">
                <p>Our website is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If you believe we have collected information from a child under 13, please contact us immediately.</p>
            </div>
        </div>

        <!-- International Transfers -->
        <div class="policy-section">
            <h2>International Data Transfers</h2>
            <div class="transfers-info">
                <p>Your information may be transferred to and processed in countries other than your own. We ensure that such transfers comply with applicable data protection laws and implement appropriate safeguards to protect your information.</p>
            </div>
        </div>

        <!-- Policy Updates -->
        <div class="policy-section">
            <h2>Changes to This Policy</h2>
            <div class="updates-info">
                <p>We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy on this page and updating the "Last updated" date. We encourage you to review this policy periodically.</p>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="policy-section">
            <h2>Contact Us</h2>
            <div class="contact-info">
                <div class="contact-details">
                    <h3>Questions About This Policy?</h3>
                    <p>If you have any questions about this Privacy Policy or our privacy practices, please contact us:</p>

                    <div class="contact-methods">
                        <div class="contact-method">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <h4>Email</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>

                        <div class="contact-method">
                            <i class="fas fa-phone"></i>
                            <div>
                                <h4>Phone</h4>
                                <p>+****************</p>
                            </div>
                        </div>

                        <div class="contact-method">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <h4>Address</h4>
                                <p>123 Cleanance Street<br>Lab District, CL 12345<br>United States</p>
                            </div>
                        </div>
                    </div>

                    <div class="contact-actions">
                        <a href="<?= UrlHelper::url('/contact') ?>" class="btn btn-primary">
                            <i class="fas fa-envelope"></i>
                            Send Message
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .privacy-policy {
        padding: var(--spacing-xl) 0;
        background-color: var(--dark-surface);
        min-height: 100vh;
    }

    .page-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .page-header h1 {
        font-size: var(--font-size-4xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-header p {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Section Headers */
    .privacy-policy h2 {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xl);
        text-align: center;
    }

    /* Policy Overview */
    .policy-overview {
        margin-bottom: var(--spacing-3xl);
    }

    .overview-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
    }

    .overview-card h2 {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .overview-card p {
        color: var(--text-secondary);
        line-height: 1.6;
        font-size: var(--font-size-lg);
        margin: 0;
    }

    /* Policy Sections */
    .policy-section {
        margin-bottom: var(--spacing-3xl);
    }

    .policy-section h2 {
        margin-bottom: var(--spacing-xl);
    }

    /* Information Categories */
    .info-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }

    .category-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        transition: all var(--transition-fast);
    }

    .category-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .category-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .category-card p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);
    }

    .category-card ul {
        list-style: none;
        padding: 0;
    }

    .category-card li {
        color: var(--text-secondary);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--border-color);
        position: relative;
        padding-left: var(--spacing-lg);
    }

    .category-card li::before {
        content: '•';
        position: absolute;
        left: 0;
        color: var(--primary-purple);
        font-weight: bold;
    }

    .category-card li:last-child {
        border-bottom: none;
    }

    /* Usage Grid */
    .usage-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .usage-item {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
        transition: all var(--transition-fast);
    }

    .usage-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .usage-item i {
        font-size: var(--font-size-3xl);
        color: var(--primary-purple);
        margin-bottom: var(--spacing-md);
    }

    .usage-item h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .usage-item p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Information Sharing */
    .sharing-info {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: var(--spacing-2xl);
    }

    .sharing-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
    }

    .sharing-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .sharing-card p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    .sharing-details h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .sharing-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .sharing-item {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .sharing-item h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .sharing-item p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Data Security */
    .security-info {
        display: flex;
        justify-content: center;
    }

    .security-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        max-width: 800px;
        width: 100%;
    }

    .security-card h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        text-align: center;
    }

    .security-measures {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }

    .measure {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .measure i {
        font-size: var(--font-size-xl);
        color: var(--primary-purple);
        margin-top: 2px;
    }

    .measure h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .measure p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        line-height: 1.6;
        margin: 0;
    }

    /* Your Rights */
    .rights-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .right-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
        transition: all var(--transition-fast);
    }

    .right-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .right-card i {
        font-size: var(--font-size-2xl);
        color: var(--primary-purple);
        margin-bottom: var(--spacing-md);
    }

    .right-card h3 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .right-card p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        line-height: 1.6;
        margin: 0;
    }

    .rights-contact {
        text-align: center;
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .rights-contact h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .rights-contact p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    /* Cookies Policy */
    .cookies-info {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .cookies-overview {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .cookies-overview h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .cookies-overview p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    .cookie-types h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }

    .cookie-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .cookie-item {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .cookie-item h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .cookie-item p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        line-height: 1.6;
        margin: 0;
    }

    .cookie-controls {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
    }

    .cookie-controls h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .cookie-controls p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    .cookie-options {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Other Sections */
    .children-info,
    .transfers-info,
    .updates-info {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }

    .children-info p,
    .transfers-info p,
    .updates-info p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
    }

    /* Contact Information */
    .contact-info {
        display: flex;
        justify-content: center;
    }

    .contact-details {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        max-width: 600px;
        width: 100%;
    }

    .contact-details h3 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        text-align: center;
    }

    .contact-details p {
        color: var(--text-secondary);
        text-align: center;
        margin-bottom: var(--spacing-lg);
    }

    .contact-methods {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .contact-method {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background-color: var(--dark-surface);
        border-radius: var(--radius-md);
    }

    .contact-method i {
        font-size: var(--font-size-lg);
        color: var(--primary-purple);
    }

    .contact-method h4 {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .contact-method p {
        color: var(--text-secondary);
        margin: 0;
    }

    .contact-actions {
        text-align: center;
    }

    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background-color: var(--dark-surface);
        border-color: var(--primary-purple);
        color: var(--primary-purple);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .sharing-info {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }
    }

    @media (max-width: 768px) {
        .privacy-policy {
            padding: var(--spacing-lg) 0;
        }

        .page-header h1 {
            font-size: var(--font-size-3xl);
        }

        .info-categories,
        .usage-grid,
        .rights-grid,
        .cookie-list {
            grid-template-columns: 1fr;
        }

        .security-measures {
            grid-template-columns: 1fr;
        }

        .cookie-options {
            flex-direction: column;
        }

        .contact-method {
            flex-direction: column;
            text-align: center;
        }
    }

    /* Light Mode Support */
    .light-mode .privacy-policy {
        background-color: var(--light-surface);
    }

    .light-mode .overview-card,
    .light-mode .category-card,
    .light-mode .usage-item,
    .light-mode .sharing-card,
    .light-mode .sharing-item,
    .light-mode .security-card,
    .light-mode .right-card,
    .light-mode .rights-contact,
    .light-mode .cookies-overview,
    .light-mode .cookie-item,
    .light-mode .cookie-controls,
    .light-mode .children-info,
    .light-mode .transfers-info,
    .light-mode .updates-info,
    .light-mode .contact-details {
        background-color: var(--light-card);
        border-color: var(--light-border);
    }

    .light-mode .contact-method {
        background-color: var(--light-surface);
    }

    .light-mode .btn-outline {
        border-color: var(--light-border);
        color: var(--light-text-primary);
    }

    .light-mode .btn-outline:hover {
        background-color: var(--light-surface);
    }
</style>

<script>
    function acceptAllCookies() {
        // Set all cookies
        document.cookie = "essential_cookies=true; path=/; max-age=31536000";
        document.cookie = "analytics_cookies=true; path=/; max-age=31536000";
        document.cookie = "marketing_cookies=true; path=/; max-age=31536000";
        document.cookie = "preference_cookies=true; path=/; max-age=31536000";

        alert('All cookies have been accepted.');
    }

    function rejectNonEssential() {
        // Only set essential cookies
        document.cookie = "essential_cookies=true; path=/; max-age=31536000";
        document.cookie = "analytics_cookies=false; path=/; max-age=31536000";
        document.cookie = "marketing_cookies=false; path=/; max-age=31536000";
        document.cookie = "preference_cookies=false; path=/; max-age=31536000";

        alert('Only essential cookies have been accepted.');
    }
</script>