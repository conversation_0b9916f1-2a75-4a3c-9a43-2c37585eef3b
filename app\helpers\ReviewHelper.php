<?php

/**
 * Review Helper Class
 * Handles product reviews and ratings functionality
 */
class ReviewHelper
{
    private static $pdo;

    /**
     * Initialize review helper
     */
    public static function init($pdo)
    {
        self::$pdo = $pdo;
    }

    /**
     * Get reviews for a product
     */
    public static function getProductReviews($productId, $limit = 10, $offset = 0)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT r.*, u.first_name, u.last_name, u.username
                FROM product_reviews r
                LEFT JOIN users u ON r.user_id = u.id
                WHERE r.product_id = ? AND r.is_approved = 1
                ORDER BY r.created_at DESC
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$productId, $limit, $offset]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error fetching product reviews: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get review statistics for a product
     */
    public static function getProductReviewStats($productId)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT 
                    COUNT(*) as total_reviews,
                    AVG(rating) as average_rating,
                    SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
                    SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
                    SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
                    SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
                    SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
                FROM product_reviews 
                WHERE product_id = ? AND is_approved = 1
            ");
            $stmt->execute([$productId]);
            $stats = $stmt->fetch();

            if ($stats['total_reviews'] > 0) {
                $stats['rating_distribution'] = [
                    5 => round(($stats['five_star'] / $stats['total_reviews']) * 100),
                    4 => round(($stats['four_star'] / $stats['total_reviews']) * 100),
                    3 => round(($stats['three_star'] / $stats['total_reviews']) * 100),
                    2 => round(($stats['two_star'] / $stats['total_reviews']) * 100),
                    1 => round(($stats['one_star'] / $stats['total_reviews']) * 100)
                ];
            } else {
                $stats['rating_distribution'] = [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0];
            }

            return $stats;
        } catch (PDOException $e) {
            error_log("Error fetching review stats: " . $e->getMessage());
            return [
                'total_reviews' => 0,
                'average_rating' => 0,
                'rating_distribution' => [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0]
            ];
        }
    }

    /**
     * Add a new review
     */
    public static function addReview($productId, $userId, $rating, $title, $comment)
    {
        try {
            // Check if user already reviewed this product
            $stmt = self::$pdo->prepare("
                SELECT id FROM product_reviews 
                WHERE product_id = ? AND user_id = ?
            ");
            $stmt->execute([$productId, $userId]);
            
            if ($stmt->fetch()) {
                return ['success' => false, 'error' => 'You have already reviewed this product'];
            }

            // Add the review
            $stmt = self::$pdo->prepare("
                INSERT INTO product_reviews (product_id, user_id, rating, title, comment, is_approved, created_at)
                VALUES (?, ?, ?, ?, ?, 1, NOW())
            ");
            
            if ($stmt->execute([$productId, $userId, $rating, $title, $comment])) {
                // Update product average rating
                self::updateProductRating($productId);
                
                return ['success' => true, 'message' => 'Review added successfully'];
            } else {
                return ['success' => false, 'error' => 'Failed to add review'];
            }
        } catch (PDOException $e) {
            error_log("Error adding review: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Update product average rating
     */
    private static function updateProductRating($productId)
    {
        try {
            $stmt = self::$pdo->prepare("
                UPDATE products 
                SET average_rating = (
                    SELECT AVG(rating) 
                    FROM product_reviews 
                    WHERE product_id = ? AND is_approved = 1
                )
                WHERE id = ?
            ");
            $stmt->execute([$productId, $productId]);
        } catch (PDOException $e) {
            error_log("Error updating product rating: " . $e->getMessage());
        }
    }

    /**
     * Check if user can review product (has purchased it)
     */
    public static function canUserReview($productId, $userId)
    {
        try {
            // Check if user has purchased this product
            $stmt = self::$pdo->prepare("
                SELECT COUNT(*) as count
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE oi.product_id = ? AND o.user_id = ? AND o.status = 'completed'
            ");
            $stmt->execute([$productId, $userId]);
            $result = $stmt->fetch();

            if ($result['count'] == 0) {
                return ['can_review' => false, 'reason' => 'You must purchase this product to review it'];
            }

            // Check if user already reviewed
            $stmt = self::$pdo->prepare("
                SELECT COUNT(*) as count
                FROM product_reviews
                WHERE product_id = ? AND user_id = ?
            ");
            $stmt->execute([$productId, $userId]);
            $result = $stmt->fetch();

            if ($result['count'] > 0) {
                return ['can_review' => false, 'reason' => 'You have already reviewed this product'];
            }

            return ['can_review' => true];
        } catch (PDOException $e) {
            error_log("Error checking review permission: " . $e->getMessage());
            return ['can_review' => false, 'reason' => 'Unable to verify purchase'];
        }
    }

    /**
     * Get helpful reviews (most liked)
     */
    public static function getHelpfulReviews($productId, $limit = 3)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT r.*, u.first_name, u.last_name, u.username,
                       COALESCE(helpful_votes, 0) as helpful_count
                FROM product_reviews r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN (
                    SELECT review_id, COUNT(*) as helpful_votes
                    FROM review_votes
                    WHERE is_helpful = 1
                    GROUP BY review_id
                ) rv ON r.id = rv.review_id
                WHERE r.product_id = ? AND r.is_approved = 1
                ORDER BY helpful_count DESC, r.rating DESC
                LIMIT ?
            ");
            $stmt->execute([$productId, $limit]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error fetching helpful reviews: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Vote on review helpfulness
     */
    public static function voteOnReview($reviewId, $userId, $isHelpful)
    {
        try {
            // Check if user already voted
            $stmt = self::$pdo->prepare("
                SELECT id FROM review_votes 
                WHERE review_id = ? AND user_id = ?
            ");
            $stmt->execute([$reviewId, $userId]);
            
            if ($stmt->fetch()) {
                // Update existing vote
                $stmt = self::$pdo->prepare("
                    UPDATE review_votes 
                    SET is_helpful = ?, updated_at = NOW()
                    WHERE review_id = ? AND user_id = ?
                ");
                $stmt->execute([$isHelpful, $reviewId, $userId]);
            } else {
                // Add new vote
                $stmt = self::$pdo->prepare("
                    INSERT INTO review_votes (review_id, user_id, is_helpful, created_at)
                    VALUES (?, ?, ?, NOW())
                ");
                $stmt->execute([$reviewId, $userId, $isHelpful]);
            }

            return ['success' => true];
        } catch (PDOException $e) {
            error_log("Error voting on review: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to record vote'];
        }
    }

    /**
     * Generate review summary
     */
    public static function generateReviewSummary($productId)
    {
        $stats = self::getProductReviewStats($productId);
        $reviews = self::getProductReviews($productId, 5);

        $summary = [
            'total_reviews' => $stats['total_reviews'],
            'average_rating' => round($stats['average_rating'], 1),
            'rating_distribution' => $stats['rating_distribution'],
            'recent_reviews' => $reviews
        ];

        // Extract common keywords from reviews
        if (!empty($reviews)) {
            $allComments = implode(' ', array_column($reviews, 'comment'));
            $summary['keywords'] = self::extractKeywords($allComments);
        } else {
            $summary['keywords'] = [];
        }

        return $summary;
    }

    /**
     * Extract keywords from review text
     */
    private static function extractKeywords($text, $limit = 10)
    {
        // Simple keyword extraction (in production, use more sophisticated NLP)
        $text = strtolower($text);
        $text = preg_replace('/[^a-z\s]/', '', $text);
        
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their', 'a', 'an'];
        
        $words = explode(' ', $text);
        $words = array_filter($words, function($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords);
        });
        
        $wordCount = array_count_values($words);
        arsort($wordCount);
        
        return array_slice(array_keys($wordCount), 0, $limit);
    }

    /**
     * Moderate reviews (admin function)
     */
    public static function moderateReview($reviewId, $isApproved, $adminId)
    {
        try {
            $stmt = self::$pdo->prepare("
                UPDATE product_reviews 
                SET is_approved = ?, moderated_by = ?, moderated_at = NOW()
                WHERE id = ?
            ");
            
            if ($stmt->execute([$isApproved, $adminId, $reviewId])) {
                // Update product rating if review was approved/disapproved
                $stmt = self::$pdo->prepare("SELECT product_id FROM product_reviews WHERE id = ?");
                $stmt->execute([$reviewId]);
                $review = $stmt->fetch();
                
                if ($review) {
                    self::updateProductRating($review['product_id']);
                }
                
                return ['success' => true];
            } else {
                return ['success' => false, 'error' => 'Failed to moderate review'];
            }
        } catch (PDOException $e) {
            error_log("Error moderating review: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }
}
