-- Migration: Create New Feature Tables (Simple)
-- Date: 2025-01-18

-- Create wishlists table
CREATE TABLE IF NOT EXISTS wishlists (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create product_comparisons table
CREATE TABLE IF NOT EXISTS product_comparisons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>reate review_votes table
CREATE TABLE IF NOT EXISTS review_votes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    review_id INT NOT NULL,
    user_id INT NOT NULL,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create recently_viewed table
CREATE TABLE IF NOT EXISTS recently_viewed (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    session_id VARCHAR(128) NULL,
    product_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create newsletter_subscribers table
CREATE TABLE IF NOT EXISTS newsletter_subscribers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NULL,
    status ENUM('active', 'unsubscribed', 'bounced') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL,
    is_verified BOOLEAN DEFAULT FALSE
);

-- Create email_templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    variables TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create site_settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NULL,
    setting_type ENUM('text', 'textarea', 'boolean', 'number', 'json', 'file') DEFAULT 'text',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create banners table
CREATE TABLE IF NOT EXISTS banners (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    subtitle TEXT NULL,
    image_path VARCHAR(500) NULL,
    link_url VARCHAR(500) NULL,
    button_text VARCHAR(100) NULL,
    position ENUM('hero', 'top', 'middle', 'bottom', 'sidebar') DEFAULT 'hero',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add some basic indexes
CREATE INDEX IF NOT EXISTS idx_wishlists_user ON wishlists(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlists_product ON wishlists(product_id);
CREATE INDEX IF NOT EXISTS idx_comparisons_user ON product_comparisons(user_id);
CREATE INDEX IF NOT EXISTS idx_comparisons_product ON product_comparisons(product_id);
CREATE INDEX IF NOT EXISTS idx_review_votes_review ON review_votes(review_id);
CREATE INDEX IF NOT EXISTS idx_recently_viewed_user ON recently_viewed(user_id);
CREATE INDEX IF NOT EXISTS idx_recently_viewed_product ON recently_viewed(product_id);
CREATE INDEX IF NOT EXISTS idx_newsletter_email ON newsletter_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_banners_active ON banners(is_active);

-- Insert default email templates
INSERT IGNORE INTO email_templates (name, subject, body, variables) VALUES
('order_confirmation', 'Order Confirmation - {order_number}', 
'<h2>Thank you for your order!</h2>
<p>Dear {customer_name},</p>
<p>We have received your order and it is being processed.</p>
<p><strong>Order Number:</strong> {order_number}</p>
<p><strong>Order Total:</strong> {order_total}</p>
<p><strong>Order Date:</strong> {order_date}</p>
<p>You will receive another email when your order ships.</p>
<p>Thank you for shopping with {app_name}!</p>', 
'customer_name, order_number, order_total, order_date, app_name'),

('password_reset', 'Password Reset Request - {app_name}', 
'<h2>Password Reset Request</h2>
<p>Dear {customer_name},</p>
<p>You have requested to reset your password. Click the link below to reset it:</p>
<p><a href="{reset_link}" style="background: #7E57C2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
<p>This link will expire at {expires_at}.</p>
<p>If you did not request this, please ignore this email.</p>
<p>Best regards,<br>{app_name}</p>', 
'customer_name, reset_link, expires_at, app_name'),

('welcome', 'Welcome to {app_name}!', 
'<h2>Welcome to {app_name}!</h2>
<p>Dear {customer_name},</p>
<p>Thank you for creating an account with us. We are excited to have you as part of our community!</p>
<p>You can now:</p>
<ul>
<li>Browse our products</li>
<li>Add items to your wishlist</li>
<li>Track your orders</li>
<li>Leave product reviews</li>
</ul>
<p>Happy shopping!</p>
<p>Best regards,<br>The {app_name} Team</p>', 
'customer_name, app_name');

-- Insert default site settings
INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
('site_name', 'Cleanance Lab', 'text', 'general', 'Website name', TRUE),
('site_description', 'Your trusted source for quality products', 'textarea', 'general', 'Website description', TRUE),
('contact_email', '<EMAIL>', 'text', 'contact', 'Contact email address', TRUE),
('theme_primary_color', '#7E57C2', 'text', 'theme', 'Primary theme color', TRUE),
('enable_reviews', '1', 'boolean', 'features', 'Enable product reviews', FALSE),
('enable_wishlist', '1', 'boolean', 'features', 'Enable wishlist feature', FALSE),
('enable_comparison', '1', 'boolean', 'features', 'Enable product comparison', FALSE),
('products_per_page', '12', 'number', 'catalog', 'Products per page', FALSE);
