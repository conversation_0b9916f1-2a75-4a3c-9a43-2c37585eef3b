<?php

/**
 * Wishlist Helper Class
 * Handles user wishlist functionality
 */
class WishlistHelper
{
    private static $pdo;

    /**
     * Initialize wishlist helper
     */
    public static function init($pdo)
    {
        self::$pdo = $pdo;
    }

    /**
     * Add product to wishlist
     */
    public static function addToWishlist($userId, $productId)
    {
        try {
            // Check if product already in wishlist
            $stmt = self::$pdo->prepare("
                SELECT id FROM wishlists 
                WHERE user_id = ? AND product_id = ?
            ");
            $stmt->execute([$userId, $productId]);
            
            if ($stmt->fetch()) {
                return ['success' => false, 'error' => 'Product already in wishlist'];
            }

            // Add to wishlist
            $stmt = self::$pdo->prepare("
                INSERT INTO wishlists (user_id, product_id, created_at)
                VALUES (?, ?, NOW())
            ");
            
            if ($stmt->execute([$userId, $productId])) {
                return ['success' => true, 'message' => 'Product added to wishlist'];
            } else {
                return ['success' => false, 'error' => 'Failed to add to wishlist'];
            }
        } catch (PDOException $e) {
            error_log("Error adding to wishlist: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Remove product from wishlist
     */
    public static function removeFromWishlist($userId, $productId)
    {
        try {
            $stmt = self::$pdo->prepare("
                DELETE FROM wishlists 
                WHERE user_id = ? AND product_id = ?
            ");
            
            if ($stmt->execute([$userId, $productId])) {
                return ['success' => true, 'message' => 'Product removed from wishlist'];
            } else {
                return ['success' => false, 'error' => 'Failed to remove from wishlist'];
            }
        } catch (PDOException $e) {
            error_log("Error removing from wishlist: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Toggle product in wishlist
     */
    public static function toggleWishlist($userId, $productId)
    {
        if (self::isInWishlist($userId, $productId)) {
            return self::removeFromWishlist($userId, $productId);
        } else {
            return self::addToWishlist($userId, $productId);
        }
    }

    /**
     * Check if product is in wishlist
     */
    public static function isInWishlist($userId, $productId)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT COUNT(*) as count
                FROM wishlists 
                WHERE user_id = ? AND product_id = ?
            ");
            $stmt->execute([$userId, $productId]);
            $result = $stmt->fetch();
            
            return $result['count'] > 0;
        } catch (PDOException $e) {
            error_log("Error checking wishlist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user's wishlist
     */
    public static function getUserWishlist($userId, $limit = null, $offset = 0)
    {
        try {
            $sql = "
                SELECT w.*, p.*, c.name as category_name, pi.image_path as primary_image
                FROM wishlists w
                JOIN products p ON w.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                WHERE w.user_id = ? AND p.is_active = 1
                ORDER BY w.created_at DESC
            ";
            
            if ($limit) {
                $sql .= " LIMIT ? OFFSET ?";
                $stmt = self::$pdo->prepare($sql);
                $stmt->execute([$userId, $limit, $offset]);
            } else {
                $stmt = self::$pdo->prepare($sql);
                $stmt->execute([$userId]);
            }
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error fetching wishlist: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get wishlist count for user
     */
    public static function getWishlistCount($userId)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT COUNT(*) as count
                FROM wishlists w
                JOIN products p ON w.product_id = p.id
                WHERE w.user_id = ? AND p.is_active = 1
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            
            return $result['count'];
        } catch (PDOException $e) {
            error_log("Error getting wishlist count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Clear user's wishlist
     */
    public static function clearWishlist($userId)
    {
        try {
            $stmt = self::$pdo->prepare("DELETE FROM wishlists WHERE user_id = ?");
            
            if ($stmt->execute([$userId])) {
                return ['success' => true, 'message' => 'Wishlist cleared'];
            } else {
                return ['success' => false, 'error' => 'Failed to clear wishlist'];
            }
        } catch (PDOException $e) {
            error_log("Error clearing wishlist: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Move wishlist items to cart
     */
    public static function moveToCart($userId, $productIds = null)
    {
        try {
            // Get wishlist items
            if ($productIds) {
                $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
                $sql = "
                    SELECT w.product_id, p.price, p.stock_quantity
                    FROM wishlists w
                    JOIN products p ON w.product_id = p.id
                    WHERE w.user_id = ? AND w.product_id IN ($placeholders) AND p.is_active = 1
                ";
                $params = array_merge([$userId], $productIds);
            } else {
                $sql = "
                    SELECT w.product_id, p.price, p.stock_quantity
                    FROM wishlists w
                    JOIN products p ON w.product_id = p.id
                    WHERE w.user_id = ? AND p.is_active = 1
                ";
                $params = [$userId];
            }

            $stmt = self::$pdo->prepare($sql);
            $stmt->execute($params);
            $items = $stmt->fetchAll();

            $movedCount = 0;
            $errors = [];

            foreach ($items as $item) {
                if ($item['stock_quantity'] > 0) {
                    // Add to cart
                    $stmt = self::$pdo->prepare("
                        INSERT INTO cart_items (user_id, product_id, quantity, created_at)
                        VALUES (?, ?, 1, NOW())
                        ON DUPLICATE KEY UPDATE quantity = quantity + 1
                    ");
                    
                    if ($stmt->execute([$userId, $item['product_id']])) {
                        // Remove from wishlist
                        self::removeFromWishlist($userId, $item['product_id']);
                        $movedCount++;
                    }
                } else {
                    $errors[] = "Product ID {$item['product_id']} is out of stock";
                }
            }

            return [
                'success' => true,
                'moved_count' => $movedCount,
                'errors' => $errors,
                'message' => "$movedCount items moved to cart"
            ];

        } catch (PDOException $e) {
            error_log("Error moving wishlist to cart: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Get wishlist statistics
     */
    public static function getWishlistStats($userId)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT 
                    COUNT(*) as total_items,
                    SUM(p.price) as total_value,
                    AVG(p.price) as average_price,
                    COUNT(CASE WHEN p.stock_quantity > 0 THEN 1 END) as in_stock_items,
                    COUNT(CASE WHEN p.stock_quantity = 0 THEN 1 END) as out_of_stock_items
                FROM wishlists w
                JOIN products p ON w.product_id = p.id
                WHERE w.user_id = ? AND p.is_active = 1
            ");
            $stmt->execute([$userId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting wishlist stats: " . $e->getMessage());
            return [
                'total_items' => 0,
                'total_value' => 0,
                'average_price' => 0,
                'in_stock_items' => 0,
                'out_of_stock_items' => 0
            ];
        }
    }

    /**
     * Get popular wishlist items (admin function)
     */
    public static function getPopularWishlistItems($limit = 10)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT p.*, COUNT(w.id) as wishlist_count, c.name as category_name
                FROM products p
                JOIN wishlists w ON p.id = w.product_id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = 1
                GROUP BY p.id
                ORDER BY wishlist_count DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting popular wishlist items: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Share wishlist (generate shareable link)
     */
    public static function generateShareableWishlist($userId)
    {
        try {
            // Generate unique share token
            $shareToken = bin2hex(random_bytes(16));
            
            // Store or update share token
            $stmt = self::$pdo->prepare("
                INSERT INTO wishlist_shares (user_id, share_token, created_at, expires_at)
                VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY))
                ON DUPLICATE KEY UPDATE 
                share_token = VALUES(share_token),
                created_at = VALUES(created_at),
                expires_at = VALUES(expires_at)
            ");
            
            if ($stmt->execute([$userId, $shareToken])) {
                return [
                    'success' => true,
                    'share_token' => $shareToken,
                    'share_url' => APP_URL . '/wishlist/shared/' . $shareToken
                ];
            } else {
                return ['success' => false, 'error' => 'Failed to generate share link'];
            }
        } catch (PDOException $e) {
            error_log("Error generating shareable wishlist: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }

    /**
     * Get shared wishlist
     */
    public static function getSharedWishlist($shareToken)
    {
        try {
            $stmt = self::$pdo->prepare("
                SELECT ws.user_id, u.first_name, u.last_name
                FROM wishlist_shares ws
                JOIN users u ON ws.user_id = u.id
                WHERE ws.share_token = ? AND ws.expires_at > NOW()
            ");
            $stmt->execute([$shareToken]);
            $shareInfo = $stmt->fetch();
            
            if (!$shareInfo) {
                return ['success' => false, 'error' => 'Invalid or expired share link'];
            }

            $wishlist = self::getUserWishlist($shareInfo['user_id']);
            
            return [
                'success' => true,
                'owner_name' => $shareInfo['first_name'] . ' ' . $shareInfo['last_name'],
                'wishlist' => $wishlist
            ];
        } catch (PDOException $e) {
            error_log("Error getting shared wishlist: " . $e->getMessage());
            return ['success' => false, 'error' => 'Database error occurred'];
        }
    }
}
