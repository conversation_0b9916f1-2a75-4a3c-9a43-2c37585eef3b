<section class="admin-section">
    <h1>Order Detail</h1>
    <div class="order-info">
        <p><strong>Order #:</strong> <?= $order['order_number'] ?></p>
        <p><strong>Date:</strong> <?= $order['created_at'] ?></p>
        <p><strong>Status:</strong> <?= ucfirst($order['status']) ?></p>
        <p><strong>Total:</strong> $<?= number_format($order['total_amount'], 2) ?></p>
    </div>
    <div class="customer-info">
        <h2>Customer Info</h2>
        <p><strong>Name:</strong> <?= htmlspecialchars($order['customer_name']) ?></p>
        <p><strong>Email:</strong> <?= htmlspecialchars($order['customer_email']) ?></p>
    </div>
    <h2>Order Items</h2>
    <div class="admin-table-wrapper">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($items as $item): ?>
                    <tr>
                        <td><?= htmlspecialchars($item['product_name']) ?></td>
                        <td><?= $item['quantity'] ?></td>
                        <td>$<?= number_format($item['unit_price'], 2) ?></td>
                        <td>$<?= number_format($item['total_price'], 2) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div class="order-actions">
        <form method="POST" action="/admin/orders/<?= $order['id'] ?>/status" style="display:inline;">
            <select name="status">
                <option value="pending" <?= $order['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                <option value="processing" <?= $order['status'] === 'processing' ? 'selected' : '' ?>>Processing</option>
                <option value="shipped" <?= $order['status'] === 'shipped' ? 'selected' : '' ?>>Shipped</option>
                <option value="delivered" <?= $order['status'] === 'delivered' ? 'selected' : '' ?>>Delivered</option>
                <option value="cancelled" <?= $order['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
            </select>
            <button class="btn btn-sm">Update Status</button>
        </form>
        <form method="POST" action="/admin/orders/<?= $order['id'] ?>/cancel" style="display:inline;">
            <button class="btn btn-sm btn-danger" onclick="return confirm('Cancel this order?')">Cancel Order</button>
        </form>
    </div>
</section>
<style>
    .admin-section {
        padding: 40px 0;
        max-width: 900px;
        margin: 0 auto;
    }

    .order-info,
    .customer-info {
        background: var(--dark-card);
        padding: 24px;
        border-radius: 12px;
        margin-bottom: 24px;
    }

    .order-actions {
        margin-top: 24px;
    }

    .btn-sm {
        padding: 4px 12px;
        font-size: 0.95em;
    }

    .btn-danger {
        background: #e53935;
        color: #fff;
    }

    .btn-danger:hover {
        background: #b71c1c;
    }

    .admin-table-wrapper {
        overflow-x: auto;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--dark-card);
    }

    .admin-table th,
    .admin-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-color);
    }

    .admin-table th {
        background: var(--dark-surface);
        color: var(--primary-purple);
        font-weight: 600;
    }
</style>